import datetime
import os
import logging
import time
import shutil
import glob
from inspect import getouterframes, currentframe
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from selenium.webdriver.common.alert import Alert
from selenium.webdriver.common.action_chains import ActionChains
import selenium.webdriver.edge.webdriver
from base.snap_store import WebRingsSiteTestSnapShotInfo

from util.config import CommonSettings
__settings = CommonSettings.get_settings()
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
__handler = logging.FileHandler(filename=os.path.join(__settings.log_dir, "helper.log"), mode="w")
__formatter = logging.Formatter('%(asctime)s - %(levelname)s:%(name)s - %(message)s')
__handler.setFormatter(__formatter)
logger.addHandler(__handler)

scnt = 0

def handle_logger(f):
    def _wrapper(*args, **keywords):
        # デコレート対象の関数の実行
        v = f(*args, **keywords)
        try:
            current_url = None
            call_parent = "NONE"
            call_function_name = "NONE"
            call_function_line = "NONE"
            for frame_info in getouterframes(currentframe()):
                if(os.path.basename(frame_info.filename).startswith("test_")):
                    call_parent = os.path.basename(frame_info.filename).replace("-","")
                    call_function_name = frame_info.function
                    call_function_line = frame_info.lineno
                    break
            for arg in args:
                if isinstance(arg, (selenium.webdriver.edge.webdriver.WebDriver)):
                    current_url = arg.current_url
                    break
            if current_url is not None:
                logger.info(f'{current_url} - {f.__name__} - {call_parent} - {call_function_name} - line{call_function_line}')
        except:
            # アラートが出てる画面の場合、どうしてもdriver.current_urlで例外が出てしまうのでそこを無視する
            pass
        return v
    return _wrapper

DEFAULT_WAIT_TIME = 5

class WebDriverHandleHelper(object):

    @classmethod
    def wait_page_loaded(cls, driver, wait_timeout=10):
        WebDriverWait(driver, wait_timeout).until(EC.presence_of_all_elements_located)

    @classmethod
    def wait_page_bottom_visible(cls, driver, wait_timeout=1):
        WebDriverWait(driver, wait_timeout).until(EC.visibility_of_element_located((By.ID, "Footer")))
    
    @classmethod
    def wait_download(cls, driver, wait_timeout=20):
        # ブラウザがロード中(クルクルしてる最中)は下記で待てる。ロードが終わると結果がcompleteで返ってくる。
        ret = driver.execute_script("return document.readyState;")

    @classmethod
    @handle_logger
    def get(cls, driver, url):
        ret = driver.get(url)
        cls.wait_page_loaded(driver)
        return ret
    
    @classmethod
    @handle_logger
    def find_element_by_name(cls, driver, name):
        elm = WebDriverWait(driver, DEFAULT_WAIT_TIME).until(
            EC.presence_of_element_located((By.NAME, name))
        )
        return elm

    @classmethod
    @handle_logger
    def find_element_by_id(cls, driver, idstr):
        elm = WebDriverWait(driver, DEFAULT_WAIT_TIME).until(
            EC.presence_of_element_located((By.ID, idstr))
        )
        return elm

    @classmethod
    @handle_logger
    def find_element_by_xpath(cls, driver, name):
        elm = WebDriverWait(driver, DEFAULT_WAIT_TIME).until(
            EC.presence_of_element_located((By.XPATH, name))
        )
        return elm
    @classmethod
    @handle_logger
    def find_element_by_tag_name(cls, driver, tag_name):
        elm = WebDriverWait(driver, DEFAULT_WAIT_TIME).until(
            EC.presence_of_element_located((By.TAG_NAME, tag_name))
        )
        return elm
        
    @classmethod
    @handle_logger
    def find_elements_by_class_no_wait(cls, driver, class_name):
        els = driver.find_elements(By.CLASS_NAME, class_name)
        return els

    @classmethod
    @handle_logger
    def find_element_by_css_selector(cls, driver, selector):
        elm = WebDriverWait(driver, DEFAULT_WAIT_TIME).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
        )
        return driver.find_element(By.CSS_SELECTOR, selector)

    @classmethod
    @handle_logger
    def find_elements_by_css_selector(cls, driver, selector):
        elm = WebDriverWait(driver, DEFAULT_WAIT_TIME).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
        )
        return driver.find_elements(By.CSS_SELECTOR, selector)

    @classmethod
    @handle_logger
    def find_element_with_web_element_by_css_selector(cls, driver, el, selector):
        elm = WebDriverWait(el, DEFAULT_WAIT_TIME).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
        )
        return el.find_element(By.CSS_SELECTOR, selector)

    @classmethod
    @handle_logger
    def find_elements_with_web_element_by_css_selector(cls, driver, el, selector):
        elm = WebDriverWait(el, DEFAULT_WAIT_TIME).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
        )
        return el.find_elements(By.CSS_SELECTOR, selector)

    @classmethod
    @handle_logger
    def find_elements_with_web_element_by_xpath(cls, driver, el, xpath_str):
        elm = WebDriverWait(el, DEFAULT_WAIT_TIME).until(
            EC.presence_of_element_located((By.XPATH, xpath_str))
        )
        return el.find_elements(By.XPATH, xpath_str)

    @classmethod
    @handle_logger
    def send_keys_by_name(cls, driver, name, value, clear=True):
        elm = cls.find_element_by_name(driver, name)
        # elm.send_keys("")
        if(clear):
            elm.clear()
        elm.send_keys(value)

    @classmethod
    @handle_logger
    def send_keys_by_id(cls, driver, idstr, value, clear=True):
        elm = cls.find_element_by_id(driver, idstr)
        # elm.send_keys("")
        if(clear):
            elm.clear()
        elm.send_keys(value)

    @classmethod
    @handle_logger
    def input_clear_by_id(cls, driver, idstr, value):
        elm = cls.find_element_by_id(driver, idstr)
        elm.clear()

    @classmethod
    @handle_logger
    def click_by_name(cls, driver, name):
        elm = cls.find_element_by_name(driver, name)
        elm.click()

    @classmethod
    @handle_logger
    def click_by_id(cls, driver, idstr):
        elm = cls.find_element_by_id(driver, idstr)
        elm.click()

    @classmethod
    @handle_logger
    def click_by_id_and_ok(cls, driver, idstr):
        elm = cls.find_element_by_id(driver, idstr)
        elm.click()

        time.sleep(1)
        a = Alert(driver)
        ret = a.text
        a.accept()
        return ret
    
    @classmethod
    @handle_logger
    def click_by_id_and_cansel(cls, driver, idstr):
        elm = cls.find_element_by_id(driver, idstr)
        elm.click()

        time.sleep(1)
        a = Alert(driver)
        ret = a.text
        a.dismiss()
        return ret

    @classmethod
    @handle_logger
    def click_by_id_and_alert_ok(cls, driver, idstr):
        elm = cls.find_element_by_id(driver, idstr)
        elm.click()
        return cls.alert_ok(driver)

    @classmethod
    @handle_logger
    def select_by_id(cls, driver, idstr, text=None, value=None):
        if(cls.has_elements_by_css_selector(driver, f"#{idstr}")):
            elm = cls.find_element_by_id(driver, idstr)
            select_elm = Select(elm)
            if(value is not None):
                select_elm.select_by_value(value)
            else:
                select_elm.select_by_visible_text(text)
    
    @classmethod
    @handle_logger
    def select_by_name(cls, driver, name_str, text=None, value=None):
        if(cls.has_elements_by_css_selector(driver, f"select[name='{name_str}']")):
            elm = cls.find_element_by_name(driver, name_str)
            select_elm = Select(elm)
            if(value is not None):
                select_elm.select_by_value(value)
            else:
                select_elm.select_by_visible_text(text)

    @classmethod
    def has_elements_by_css_selector(cls, driver, css_str):
        """要素が存在するかチェック"""
        # Seleniumは要素がない場合は例外を出すので、JS直呼び以外はこれでチェックするしかない
        # ヘルパーのを使うと10秒待機してしまうので、driverを直接呼ぶ
        ret = True
        script_text = f"return document.querySelector(\"{css_str}\")"
        try:
            script_ret = driver.execute_script(script_text)
            if script_ret is not None:
                ret = True
            else:
                ret = False
        except:
            ret = False
        if ret == True:
            try:
                el = cls.find_element_by_css_selector(driver, css_str)
                if(el is not None and el.is_displayed()):
                    ret = True
                else:
                    ret = False
            except:
                ret = False            
        return ret

    @classmethod
    @handle_logger
    def select_option(cls, elm, text=None):
        """現行移行用"""
        select_elm = Select(elm)
        select_elm.select_by_visible_text(text)

    @classmethod
    @handle_logger
    def alert_ok(cls, driver):
        a = Alert(driver)
        ret = a.text
        a.accept()
        return ret

    @classmethod
    @handle_logger
    def alert_cancel(cls, driver):
        a = Alert(driver)
        ret = a.text
        a.dismiss()
        return ret

    @classmethod
    @handle_logger
    def alert_ok_many(cls, driver, limit_count=10):
        ret = []
        for count in range(limit_count):
            tmp_text = cls.__alert_ok_wait(driver)
            if(tmp_text != ""):
                ret.append(tmp_text)
            else:
                break
        return ret

    @classmethod
    def __alert_ok_wait(cls, driver):
        ret = ""
        try:
            wait = WebDriverWait(driver, 1)
            wait.until(EC.alert_is_present())
            alert = driver.switch_to.alert
            ret = alert.text
            alert.accept()
        except Exception as e:
            pass  # アラートが出なかった場合
        return ret


    @classmethod
    @handle_logger
    def execute_script(cls, driver, script_text):
        """指定されたScriptの実行"""
        driver.execute_script(script_text)

    @classmethod
    @handle_logger
    def expand_scroll_dom(cls, driver, css_selector):
        elm = cls.find_element_by_css_selector(driver, css_selector)
        elm_height = driver.execute_script("return arguments[0].scrollHeight", elm)
        style_height = f"{elm_height}px"
        driver.execute_script("arguments[0].style.height = arguments[1]", elm, style_height)

    @classmethod
    @handle_logger
    def save_screenshot(cls, driver, file_path, fullsize=False):
        
        default_screen_width  = 1366
        default_screen_height = 768
        
        cls.wait_page_loaded(driver,wait_timeout=10)

        if fullsize:
            # ページの左上までスクロール
            driver.execute_script("window.scrollTo(0, 0);")

            #スクリーンサイズ取得（フルスクリーン前提）
            screen_width  = 1366
            screen_height = 768

            # ページサイズ取得
            total_width = 0
            total_height = 0
            try:
                total_width  = driver.execute_script("return document.body.scrollWidth")
                total_height = driver.execute_script("return document.body.scrollHeight")
            except Exception as e:
                logger.info(str(e))
                print("!!!GET WINDOWS SIZE ERROR!!!", str(e))

            # IE用にページサイズがスクリーンサイズ以下の場合に補正
            if total_height < screen_height:
                total_height = screen_height
            if total_width < screen_width:
                total_width = screen_width

            view_height = screen_height
            view_width = screen_width

            driver.set_window_size(total_width,total_height)

            # window.scrollTo(x-coord, y-coord)
            # driver.execute_script(f"window.scrollTo({total_width}, {total_height});")
            
            # FooterのDOMが表示されるまで待機(現行WebRings福祉用なのでタイムアウトしても無視)
            try:
                cls.wait_page_bottom_visible(driver)
            except:
                pass
        
        driver.save_screenshot(file_path)
        if fullsize:
            driver.set_window_size(default_screen_width,default_screen_height)

    @classmethod
    @handle_logger
    def change_euc_driver(cls, driver):
        driver.switch_to.default_content()
        iframe = cls.find_element_by_css_selector(driver, "#_wr_body_panel > iframe")
        driver.switch_to.frame(iframe)
    
    @classmethod
    @handle_logger
    def change_euc_main_panel_driver(cls, driver):
        driver.switch_to.default_content()
        cls.change_euc_driver(driver)
        for el in cls.find_elements_by_css_selector(driver, "iframe"):
            name_text = str(el.get_attribute("name"))
            if(name_text.endswith("r")):
                driver.switch_to.frame(el)
                break

    @classmethod
    @handle_logger
    def change_euc_search_panel_driver(cls, driver):
        driver.switch_to.default_content()
        cls.change_euc_driver(driver)
        for el in cls.find_elements_by_css_selector(driver, "iframe"):
            src_text = str(el.get_attribute("src"))
            name_text = str(el.get_attribute("name"))
            if("scrEUCsearch.cshtml" in src_text):
                if(len(name_text.split("-")) >= 3):
                    ActionChains(driver).scroll_to_element(el).perform()
                    driver.switch_to.frame(el)
                    break
    
    @classmethod
    @handle_logger
    def change_param_query_search_panel_driver(cls, driver):
        driver.switch_to.default_content()
        cls.change_euc_driver(driver)
        for el in cls.find_elements_by_css_selector(driver, "iframe"):
            src_text = str(el.get_attribute("src"))
            name_text = str(el.get_attribute("name"))
            if("ScrEUCSearchPQ.cshtml" in src_text):
                ActionChains(driver).scroll_to_element(el).perform()
                driver.switch_to.frame(el)
                break

    @classmethod
    @handle_logger
    def change_teikei_search_panel_driver(cls, driver):
        driver.switch_to.default_content()
        cls.change_euc_driver(driver)
        for el in cls.find_elements_by_css_selector(driver, "iframe"):
            src_text = str(el.get_attribute("src"))
            name_text = str(el.get_attribute("name"))
            if("scrEUCsearch.cshtml" in src_text):
                if(len(name_text) >= 10):
                    ActionChains(driver).scroll_to_element(el).perform()
                    driver.switch_to.frame(el)
                    break
    @classmethod
    @handle_logger
    def euc_search_click(cls, driver):
        start_dt = datetime.datetime.now()
        cls.click_by_id(driver, "getSearch")
        cls.wait_euc_page_unlock(driver)
        end_dt = datetime.datetime.now()
        time_delta = end_dt - start_dt
        span_mis = (time_delta.seconds * 1000000) + time_delta.microseconds
        return int(span_mis / 1000)

    @classmethod
    @handle_logger
    def euc_shikaku_radio_click(cls, driver):
        elem = cls.__euc_license_radio("License")
        if(elem is not None):
            elem.click()

    @classmethod
    @handle_logger
    def euc_db_radio_click(cls, driver):
        elem = cls.__euc_license_radio("DB")
        if(elem is not None):
            elem.click()
    
    @classmethod
    def __euc_license_radio(cls, value_body):
        ret = None
        for el in cls.find_element_by_css_selector(driver, "input[name='licenseRadio']"):
            if(str(el.get_attribute("value")).strip() == value_body):
                ret = el
        return ret

    @classmethod
    @handle_logger
    def open_euc_side_menu_by_name(cls, driver, side_menu_name):
        ret = False
        for el in cls.find_elements_by_css_selector(driver, "div.treeTitle"):
            menu_text = str(el.text).strip()
            if(menu_text == side_menu_name):
                el.click()
                ret = True
                time.sleep(0.5)  # 開ききる前に次に行ってしまうので少しだけ待つ
                break
        return ret

    @classmethod
    @handle_logger
    def click_euc_csv_file_list_button(cls, driver):
        ret = False
        try:
            el = cls.find_element_by_id(driver, "open_csvFileList")
            if el is not None:
                el.click()
                ret = True
        except:
            pass
        return ret

    @classmethod
    @handle_logger
    def click_euc_no_file_list_result_alert_if_visible(cls, driver):
        """CSVファイルリストのデータ無し警告が出てる場合クリックする。クリックした場合Trueを返す。"""
        ret = False
        try:
            for el in cls.find_elements_by_css_selector(driver, "div.confirm_modal"):
                tmp_text = str(el.text).strip()
                if("CSVファイルが存在しません" in tmp_text):
                    el_btn = el.find_element(By.CSS_SELECTOR, "button")
                    el_btn.click()
                    ret = True
                    break
        except:
            pass
        return ret

    @classmethod
    @handle_logger
    def click_euc_csv_file_list_dialog_close(cls, driver):
        """CSVファイルリストを閉じるボタンをクリック。クリック出来た場合はTrueを返す。"""
        ret = False
        try:
            for el in cls.find_elements_by_css_selector(driver, "div.ui-dialog-titlebar"):
                tmp_text = str(el.text).strip()
                if("ＣＳＶファイルリスト" in tmp_text):
                    el_btn = el.find_element(By.CSS_SELECTOR, "button")
                    el_btn.click()
                    ret = True
                    break
        except:
            pass
        return ret
    
    @classmethod
    @handle_logger
    def down_load_euc_csv_file_by_file_name(cls, driver, self_inst, case_name, file_name, is_after_delete=True):
        """CSVのファイル名指定でDLする。DL出来た場合はTrueを返す。※処理が終わるまで待たない。"""
        ret = False
        del_clicked = False
        try:
            for el in cls.find_elements_by_css_selector(driver, "table.grid_CsvDraw tr"):
                tmp_text = str(el.text).strip()
                if(file_name in tmp_text):
                    el_btn = el.find_element(By.CSS_SELECTOR, "td.dlCsv img")
                    el_btn.click()
                    ret = True

                    time.sleep(1)  # 少し待たないとファイルが出来てない
                    store = WebRingsSiteTestSnapShotInfo.get_store()
                    # 一時フォルダから名前を変更して移動
                    for file_path in glob.glob(self_inst.settings.dl_work_dir + "/*"):
                        file_base_name = os.path.basename(file_path)
                        save_file_path = cls.__get_download_file_path(self_inst, file_base_name, case_name)
                        shutil.move(file_path, save_file_path)
                        store.add_download_file(save_file_path, self_inst)
                    if(is_after_delete):
                        el_btn = el.find_element(By.CSS_SELECTOR, "td.delCsv img")
                        el_btn.click()
                        del_clicked=True
                    break
            if(del_clicked):
                delete_div = cls.find_element_by_id(driver, "csvDeleteConfirm")
                el_btn = delete_div.find_element(By.CSS_SELECTOR, "button.confirm_positive")
                el_btn.click()
                if(cls.has_elements_by_css_selector(driver, "#scrEUCcsvMessage button.confirm_positive")):
                    cls.find_element_by_css_selector(driver, "#scrEUCcsvMessage button.confirm_positive").click()
                    ret = True
        except Exception as e:
            print(e)
        return ret

    @classmethod
    def __get_download_file_path(cls, self_inst, file_name, case_name):
        full_class_name = "-".join(self_inst.__class__.__module__.split('.')) + "-" + self_inst.__class__.__name__
        return os.path.join(self_inst.settings.dl_dir, full_class_name + "-" + case_name + "-" + file_name)
    
    @classmethod
    @handle_logger
    def open_euc_by_name(cls, driver, euc_name):
        WebDriverHandleHelper.open_euc_side_menu_by_name(driver, "テーブル参照")
        # 一回全部開く
        for el in cls.find_elements_by_css_selector(driver, "div[id='license_tables_License'] dt"):
            if(str(el.get_attribute("class")) != "active"):
                el.click()
                time.sleep(0.2)  # 開ききる前に次に行ってしまうので少しだけ待つ
        # 該当EUCを探してダブルクリック
        actionChains = ActionChains(driver)
        for el in cls.find_elements_by_css_selector(driver, "div[id='license_tables_License'] dd p"):
            if(el.get_attribute("title") == euc_name):
                actionChains.double_click(el).perform()
                cls.wait_euc_page_unlock(driver)
                break
    
    @classmethod
    @handle_logger
    def open_param_query_by_name(cls, driver, euc_name):
        WebDriverHandleHelper.open_euc_side_menu_by_name(driver, "パラメータクエリ")
        # 一回全部開く
        for el in cls.find_elements_by_css_selector(driver, "div[id='pq_tables_pqLicense'] dt"):
            if(str(el.get_attribute("class")) != "active"):
                el.click()
                time.sleep(0.2)  # 開ききる前に次に行ってしまうので少しだけ待つ
        # 該当EUCを探してダブルクリック
        # actionChains = ActionChains(driver)
        for el in cls.find_elements_by_css_selector(driver, "div[id='pq_tables_pqLicense'] dd p"):
            if(el.get_attribute("title") == euc_name):
                el.click()
                # actionChains.double_click(el).perform()
                cls.wait_euc_page_unlock(driver)
                break

    @classmethod
    @handle_logger
    def open_teikei_by_name(cls, driver, euc_name):
        ret = False
        WebDriverHandleHelper.open_euc_side_menu_by_name(driver, "定型管理")
        # 一回全部開く
        for el in cls.find_elements_by_css_selector(driver, "div[id='TeikeiList'] ul li"):
            if(str(el.get_attribute("aria-expanded")) == "false"):
                open_icon_el = cls.find_element_with_web_element_by_css_selector(driver, el, "i.jstree-icon")
                open_icon_el.click()
                time.sleep(0.2)  # 開ききる前に次に行ってしまうので少しだけ待つ
        # 該当EUCを探してダブルクリック
        actionChains = ActionChains(driver)
        for el in cls.find_elements_by_css_selector(driver, "div[id='TeikeiList'] a.jstree-anchor"):
            tmp_text = el.text.strip()
            if(tmp_text == euc_name):
                actionChains.double_click(el).perform()
                cls.wait_euc_page_unlock(driver)
                ret = True
                break
        return ret

    @classmethod
    @handle_logger
    def click_max_col_warn_dialog_if_visible(cls, driver):
        try:
            for el in cls.find_elements_by_css_selector(driver, "div[id='maxColumnsOver'] button"):
                el.click()
                time.sleep(0.2)  # 開ききる前に次に行ってしまうので少しだけ待つ
        except:
            pass

    @classmethod
    @handle_logger
    def click_euc_no_result_alert_if_visible(cls, driver):
        """EUCの検索結果0件のアラートをクリック出来ていたらTrueを返す"""
        cls.change_euc_main_panel_driver(driver)
        ret = False
        try:
            for el in cls.find_elements_by_css_selector(driver, "#scrEUCMsgTpl"):
                tmp_text = el.text.strip()
                tmp_text = el.get_attribute("textContent")
                # if("検索結果がありません" in tmp_text):
                if(el.is_displayed()):
                    el_btn = el.find_element(By.CSS_SELECTOR, "button.confirm_positive")
                    el_btn.click()
                    ret = True
        except Exception as e:
            print(e)
        cls.change_euc_driver(driver)
        return ret

    @classmethod
    @handle_logger
    def click_euc_csv_output_confirm_alert_if_visible(cls, driver):
        """EUCの確認アラートをクリック出来ていたらTrueを返す"""
        ret = False
        try:
            if(cls.has_elements_by_css_selector(driver, "#scrEUCMsgTpl button.confirm_positive")):
                tmp_text = str(cls.find_element_by_css_selector(driver, "#scrEUCMsgTpl").text).strip()
                if("指定されたファイル名は既に存在します。" in tmp_text):
                    ret = False
                else:
                    ret = True
                cls.find_element_by_css_selector(driver, "#scrEUCMsgTpl button.confirm_positive").click()
        except:
            pass
        return ret

    @classmethod
    @handle_logger
    def click_euc_csv_output_cancel_if_visible(cls, driver):
        """EUCのCSV出力キャンセルをクリック出来ていたらTrueを返す"""
        ret = False
        try:
            if(cls.has_elements_by_css_selector(driver, "button.csv_cancel")):
                cls.find_element_by_css_selector(driver, "button.csv_cancel").click()
        except:
            pass
        return ret

    @classmethod
    @handle_logger
    def click_euc_csv_output_dialog_open_button(cls, driver):
        """EUCのCSV出力ボタンをクリックする。クリック出来たらTrueを返す"""
        ret = False
        try:
            WebDriverHandleHelper.change_euc_driver(driver)
            if(cls.has_elements_by_css_selector(driver, "button.getCsv")):
                cls.find_element_by_css_selector(driver, "button.getCsv").click()
                ret = True
        except Exception as e:
            print(e)
            ret = False
        return ret

    @classmethod
    @handle_logger
    def click_euc_csv_output_button(cls, driver, file_name, is_all_data=False):
        """EUCのCSVダイアログの出力ボタンをクリックする。クリック出来たらTrueを返す"""
        ret = False
        try:
            WebDriverHandleHelper.change_euc_driver(driver)
            # ファイル名をクリア
            cls.input_clear_by_id(driver, "csvFileName", "")
            cls.send_keys_by_id(driver, "csvFileName", file_name)
            if(is_all_data is False):
                el = cls.find_element_by_css_selector(driver, "#csvCounts-ResultOnly input")
                if(el is not None):
                    el.click()
            el = cls.find_element_by_css_selector(driver, "button.csv_output")
            el.click()
            time.sleep(0.5)
            alert_clicked = cls.click_euc_csv_output_confirm_alert_if_visible(driver)
            if(alert_clicked):
                ret = True
        except Exception as e:
            print(e)
            ret = False
        return ret

    @classmethod
    def get_euc_name_list(cls, driver):
        ret = []
        WebDriverHandleHelper.change_euc_driver(driver)
        # テーブル参照のメニューを開く
        WebDriverHandleHelper.open_euc_side_menu_by_name(driver, "テーブル参照")
        # 一回全部開く
        for el in cls.find_elements_by_css_selector(driver, "div[id='license_tables_License'] dt"):
            if(str(el.get_attribute("class")) != "active"):
                el.click()
                time.sleep(0.2)  # 開ききる前に次に行ってしまうので少しだけ待つ
        for el in cls.find_elements_by_css_selector(driver, "div[id='license_tables_License'] dd p"):
            ret.append(el.get_attribute("title"))
        return ret

    @classmethod
    @handle_logger
    def set_param_query_params(cls, driver, value_list=None):
        """パラメータクエリのパラメータ引数を引数のlist順に上から指定する。"""
        ret = False
        if(value_list is not None):
            for i, el in enumerate(cls.find_elements_by_css_selector(driver, "div.pq_modal input.pQueryVal")):
                if(len(value_list) > i):
                    el.clear()
                    el.send_keys(value_list[i])
                    ret = True
        return ret
    @classmethod
    @handle_logger
    def click_param_query_params_search(cls, driver):
        """パラメータクエリのパラメータ引数を引数のlist順に上から指定する。"""
        ret = False
        el = cls.find_element_by_id(driver, "btn_searchPq")
        el.click()
        if(cls.click_euc_confirm_alert_if_visible(driver)):
            ret = False
        else:
            ret = True
        return ret

    @classmethod
    @handle_logger
    def click_euc_confirm_alert_if_visible(cls, driver):
        """EUCの確認アラートをクリック出来ていたらTrueを返す"""
        ret = False
        try:
            if(cls.has_elements_by_css_selector(driver, "#scrEUCMsgTpl button.confirm_positive")):
                cls.find_element_by_css_selector(driver, "#scrEUCMsgTpl button.confirm_positive").click()
                ret = True
        except:
            pass
        return ret

    @classmethod
    def euc_scroll_bottom(cls, driver):
        driver.switch_to.default_content()
        driver.execute_script('window.scrollTo(0, document.body.scrollHeight);')
        iframe = cls.find_element_by_css_selector(driver, "#_wr_body_panel > iframe")
        driver.switch_to.frame(iframe)

    @classmethod
    @handle_logger
    def set_filter_rec(cls, driver, item_name, item_condition, item_value1, item_value2="", item_and="",s1=False,s2=False,e1=False,e2=False):
        """EUCのフィルター指定"""
        cls.euc_scroll_bottom(driver)
        cls.change_euc_search_panel_driver(driver)
        return cls.__set_filter_rec(driver, item_name=item_name, item_condition=item_condition, item_value1=item_value1,item_value2=item_value2, item_and=item_and, s1=s1, s2=s2, e1=e1, e2=e2)

    @classmethod
    @handle_logger
    def set_param_query_filter_rec(cls, driver, item_name, item_condition, item_value1, item_value2="", item_and="",s1=False,s2=False,e1=False,e2=False):
        """パラメータクエリのフィルター指定"""
        cls.euc_scroll_bottom(driver)
        cls.change_param_query_search_panel_driver(driver)
        return cls.__set_filter_rec(driver, item_name=item_name, item_condition=item_condition, item_value1=item_value1,item_value2=item_value2, item_and=item_and, s1=s1, s2=s2, e1=e1, e2=e2)

    @classmethod
    @handle_logger
    def set_teikei_filter_rec(cls, driver, item_name, item_condition, item_value1, item_value2="", item_and="",s1=False,s2=False,e1=False,e2=False):
        """定型のフィルター指定"""
        cls.euc_scroll_bottom(driver)
        cls.change_teikei_search_panel_driver(driver)
        return cls.__set_filter_rec(driver, item_name=item_name, item_condition=item_condition, item_value1=item_value1,item_value2=item_value2, item_and=item_and, s1=s1, s2=s2, e1=e1, e2=e2, is_add=False)

    @classmethod
    def __set_filter_rec(cls, driver, item_name, item_condition, item_value1, item_value2="", item_and="",s1=False,s2=False,e1=False,e2=False, is_add=True):
        """EUCのフィルター指定本体"""
        ret = False
        try:
            for el in cls.find_elements_by_css_selector(driver, "table.filterCmnTbl tbody"):
                temp_elem = el.find_element(By.CSS_SELECTOR, "select.filterColumnName")
                filter_select = Select(temp_elem)
                selected_value_text = filter_select.first_selected_option.text
                
                if(is_add):
                    if(selected_value_text != ""):
                        continue
                else:
                    if(selected_value_text != item_name):
                        continue
                filter_select.select_by_visible_text(item_name)

                temp_elem = el.find_element(By.CSS_SELECTOR, "select.filterCondition")
                filter_select = Select(temp_elem)
                filter_select.select_by_visible_text(item_condition)
                for i, value_el in enumerate(cls.find_elements_with_web_element_by_css_selector(driver, el, "input[name='filterValue']")):
                    if(i <= 0):
                        if(value_el.is_displayed()):
                            value_el.clear()
                            value_el.send_keys(item_value1)
                            ret = True
                    else:
                        if(value_el.is_displayed()):
                            value_el.clear()
                            value_el.send_keys(item_value2)
                if(is_add and item_and != ""):
                    temp_elem = cls.find_element_with_web_element_by_css_selector(driver, el,"img.addFilter")
                    temp_elem.click()
                    temp_elem = cls.find_element_with_web_element_by_css_selector(driver, el,"select.andOr")
                    filter_select = Select(temp_elem)
                    filter_select.select_by_visible_text(item_and)
                if(s1):
                    temp_elem = cls.find_element_with_web_element_by_css_selector(driver, el,"span.bracket.bs_1")
                    temp_elem.click()
                if(s2):
                    temp_elem = cls.find_element_with_web_element_by_css_selector(driver, el,"span.bracket.bs_2")
                    temp_elem.click()
                if(e1):
                    temp_elem = cls.find_element_with_web_element_by_css_selector(driver, el,"span.bracket.be_1")
                    temp_elem.click()
                if(e2):
                    temp_elem = cls.find_element_with_web_element_by_css_selector(driver, el,"span.bracket.be_2")
                    temp_elem.click()
                break
        except Exception as e:
            print(e)
            ret = False
        cls.change_euc_driver(driver)
        return ret


    @classmethod
    @handle_logger
    def click_euc_fukushi_print_button(cls, driver):
        """福祉印刷ボタンクリック"""
        ret = False
        cls.change_euc_main_panel_driver(driver)
        el = cls.find_element_by_id(driver, "PopEUCHukushiPrintReady")
        el.click()
        if(cls.has_elements_by_css_selector(driver, "#freeDialog_1")):
            ret = True
            
        cls.change_euc_driver(driver)
        return ret
        
    @classmethod
    @handle_logger
    def set_fukushi_print_params(cls, driver, value_list=None):
        """福祉印刷パラメータセット"""
        ret = False
        cls.change_euc_main_panel_driver(driver)
        for i, el in enumerate(cls.find_elements_by_css_selector(driver, "div.HukushiParam input.inputField")):
            if(el.is_displayed() and len(value_list) > i):
                el.clear()
                el.send_keys(value_list[i])
                ret = True
        cls.change_euc_driver(driver)
        return ret

    @classmethod
    @handle_logger
    def click_euc_fukushi_print_exec_button(cls, driver):
        """福祉印刷の印刷ボタンクリック"""
        ret = False
        cls.change_euc_main_panel_driver(driver)
        el = cls.find_element_by_css_selector(driver, "div.HukushiBtns #printDo")
        el.click()
        time.sleep(3)
        if(cls.has_elements_by_css_selector(driver, "div.confirm_wrap")):
            for el in cls.find_elements_by_css_selector(driver, "div.confirm_wrap"):
                tmp_text = el.text.strip()
                if("印刷に成功" in tmp_text):
                    button_elem = cls.find_element_with_web_element_by_css_selector(driver, el, "button.confirm_positive")
                    button_elem.click()
                    ret = True
        cls.change_euc_driver(driver)
        return ret

    @classmethod
    @handle_logger
    def click_euc_fukushi_print_exec_history_button(cls, driver):
        """福祉印刷の実行履歴ボタンクリック"""
        ret = False
        cls.change_euc_main_panel_driver(driver)
        el = cls.find_element_by_id(driver, "PopEUCHukushiPrintHistory")
        el.click()
        time.sleep(2)
        if(cls.has_elements_by_css_selector(driver, "#freeDialog_2")):
            ret = True

        cls.change_euc_driver(driver)
        return ret

    @classmethod
    @handle_logger
    def click_euc_fukushi_print_exec_history_reload_button(cls, driver):
        """福祉印刷の実行状態更新ボタンクリック"""
        ret = False
        cls.change_euc_main_panel_driver(driver)
        el = cls.find_element_by_css_selector(driver, "div.HukushiJobSelect button.history_reload")
        el.click()
        ret = True
        cls.change_euc_driver(driver)
        return ret
    
    @classmethod
    @handle_logger
    def click_euc_fukushi_print_exec_history_close_button(cls, driver):
        """福祉印刷の実行履歴ダイアログ閉じるボタン"""
        ret = False
        cls.change_euc_main_panel_driver(driver)
        el = cls.find_element_by_css_selector(driver, "div[aria-describedby='freeDialog_2'] button.ui-dialog-titlebar-close")
        el.click()
        ret = True
        cls.change_euc_driver(driver)
        return ret

    @classmethod
    @handle_logger
    def click_euc_fukushi_print_exec_dialog_close_button(cls, driver):
        """福祉印刷の実行指示ダイアログ閉じるボタン"""
        ret = False
        cls.change_euc_main_panel_driver(driver)
        el = cls.find_element_by_css_selector(driver, "div[aria-describedby='freeDialog_1'] button.ui-dialog-titlebar-close")
        el.click()
        ret = True
        cls.change_euc_driver(driver)
        return ret

    @classmethod
    @handle_logger
    def wait_fukushi_print_nomal_end(cls, driver):
        """福祉印刷の実行履歴が正常終了になるまで待つ"""
        ret = False
        is_proc_continue = True
        # MAX20回まで確認
        for count in range(20):
            cls.change_euc_main_panel_driver(driver)
            for i, el in enumerate(cls.find_elements_by_css_selector(driver, "div.HukushiWrap.history table.grid_CsvDraw td.hd_runState")):
                tmp_text = el.text.strip()
                if("処理中" in tmp_text):
                    is_proc_continue = True
                else:
                    is_proc_continue = False
                # 最初の1件だけ見る
                break
            if(is_proc_continue):
                cls.click_euc_fukushi_print_exec_history_reload_button(driver)
                time.sleep(3)
            else:
                break
            
        ret = False if is_proc_continue else True
        cls.change_euc_driver(driver)
        return ret
    
    @classmethod
    @handle_logger
    def down_load_euc_fukushi_print_pdf(cls, driver, self_inst, case_name):
        """福祉印刷のPDFをDLする(最初の1レコードのみ)。DL出来た場合はTrueを返す。"""
        ret = False
        try:
            cls.change_euc_main_panel_driver(driver)
            for i, el in enumerate(cls.find_elements_by_css_selector(driver, "div.history table.grid_CsvDraw img.pdfDw")):
                el.click()
                time.sleep(1)  # 少し待たないとファイルが出来てない
                store = WebRingsSiteTestSnapShotInfo.get_store()
                # 一時フォルダから名前を変更して移動
                for file_path in glob.glob(self_inst.settings.dl_work_dir + "/*"):
                    file_base_name = os.path.basename(file_path)
                    save_file_path = cls.__get_download_file_path(self_inst, file_base_name, case_name)
                    shutil.move(file_path, save_file_path)
                    store.add_download_file(save_file_path, self_inst)
                    ret = True
                break
        except Exception as e:
            print(e)
        return ret

    @classmethod
    def close_euc_all_tab(cls, driver):
        driver.execute_script("allClose();")

    @classmethod
    def wait_euc_page_unlock(cls, driver, wait_timeout=10):
        """ページロックのUIが解除されるまで待機する"""
        loader_visible = cls.wait_euc_loading_visible(driver)
        if(loader_visible):
            cls.__wait_euc_page_unlock(driver, wait_timeout)
    
    @classmethod
    def wait_euc_loading_visible(cls, driver, wait_time=1):
        is_page_loading = False
        sleep_span = 0.2
        sleep_limit_count = int(wait_time / sleep_span)
        is_page_loading = cls.is_euc_loading(driver)
        if(not is_page_loading):
            for i in range(0, sleep_limit_count):
                if(not cls.is_euc_loading(driver)):
                    is_page_loading = False
                    time.sleep(sleep_span)
                else:
                    is_page_loading = True
                    break
        return is_page_loading
    @classmethod
    def __wait_euc_page_unlock(cls, driver, wait_timeout=10):
        sleep_span = 0.2
        sleep_limit_count = int(wait_timeout / sleep_span)
        is_page_loading = cls.is_euc_loading(driver)
        if(is_page_loading):
            for i in range(0, sleep_limit_count):
                if(cls.is_euc_loading(driver)):
                    time.sleep(sleep_span)
                else:
                    break

    @classmethod
    def is_euc_loading(cls, driver):
        is_page_loading = False
        try:
            elem = driver.find_element(By.CSS_SELECTOR, "div:has(>#lorder-tag)")
            if(elem is not None):
                is_page_loading = elem.is_displayed()
        except Exception as e:
            is_page_loading = False
        return is_page_loading

    @classmethod
    def read_lines_file_data_store(cls, self_inst, file_name, case_name):
        ret = []
        file_path = cls.__get_file_data_store_path(self_inst, file_name, case_name)
        if(os.path.exists(file_path)):
            with open(file_path, mode="r", encoding="utf8") as f:
                ret = f.read().splitlines()
        return ret

    @classmethod
    def write_text_file_data_store(cls, self_inst, file_name, case_name, text):
        file_path = cls.__get_file_data_store_path(self_inst, file_name, case_name)
        with open(file_path, mode="w", encoding="utf8") as f:
            f.write(text)
        return file_path

    @classmethod
    def __get_file_data_store_path(cls, self_inst, file_name, case_name):
        full_class_name = "-".join(self_inst.__class__.__module__.split('.')) + "-" + self_inst.__class__.__name__
        return os.path.join(self_inst.settings.file_store_dir, full_class_name + "-" + case_name + "-" + file_name)
    