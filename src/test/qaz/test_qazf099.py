import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class Test_QAZF099(FukushiSiteTestCaseBase):
    """Test_QAZF099"""
    
    def test_case_001(self):
        """test_case_001"""           
        driver = None
        test_data = self.common_test_data

        self.do_login()
        self.click_button_by_label("住民コード振替")
        self.screen_shot("QAZF099_1",caption="QAZF099_初期表示")

        self.driver.find_element(By.ID, "TxtOldAtenaCode").click()
        self.find_element(By.ID,"TxtOldAtenaCode").send_keys(test_data.get("qazf099_atena_code1"))
        self.driver.find_element(By.ID, "CmdShokiHyoji").click()
        self.screen_shot("QAZF099_2",caption="QAZF099_初期表示ボタン押下")

        self.driver.find_element(By.ID, "span_CmdOldKojinKensaku").click()
        self.screen_shot("QAZF099_3",caption="QAZF099_旧住基の個人検索ボタン押下")

        self.driver.find_element(By.ID, "AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qazf099_atena_code1"))
        self.driver.find_element(By.ID, "span_Kensaku").click()
        self.screen_shot("QAZF099_4",caption="QAZF099_（旧住基）個人検索表示用の検索ボタン押下")

        self.driver.find_element(By.ID, "span_CmdNewKojinKensaku").click()
        self.screen_shot("QAZF099_5",caption="QAZF099_新住基の個人検索ボタン押下")

        self.driver.find_element(By.ID, "AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qazf099_atena_code2"))
        self.driver.find_element(By.ID, "span_Kensaku").click()
        self.screen_shot("QAZF099_6",caption="QAZF099_（新住基）個人検索表示用の検索ボタン押下")      
        
        self.driver.find_element(By.ID, "CmdKensaku").click()
        self.screen_shot("QAZF099_7",caption="QAZF099_検索ボタン押下")    

        self.driver.find_element(By.ID, "CmdSubeteSentaku").click()
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF099_8",caption="QAZF099_全て選択ボタン押下")  

        self.driver.find_element(By.ID, "CmdSubeteKaijo").click()
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF099_9",caption="QAZF099_全て解除ボタン押下")  

        self.driver.find_element(By.ID, "span_CmdHaitaShori").click()
        assert self.driver.switch_to.alert.text == "現在表示中の事業の排他処理を行います。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF099_10",caption="QAZF099_排他処理")

        self.driver.find_element(By.ID, "span_CmdHaitaKaijo").click()
        assert self.driver.switch_to.alert.text == "排他を解除します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF099_11",caption="QAZF099_排他を解除")

        self.driver.find_element(By.ID, "JigyoShoriChkBox_8").click()
        self.driver.find_element(By.ID, "span_CmdKoushin").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF099_12",caption="QAZF099_更新")

        # リスト
        self.driver.find_element(By.ID, "CmdShokiHyoji").click()
        self.driver.find_element(By.ID, "span_CmdOldKojinKensaku").click()
        self.driver.find_element(By.ID, "AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qazf099_atena_code2"))
        self.driver.find_element(By.ID, "span_Kensaku").click()
        self.driver.find_element(By.ID, "span_CmdNewKojinKensaku").click()
        self.driver.find_element(By.ID, "AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qazf099_atena_code1"))
        self.driver.find_element(By.ID, "span_Kensaku").click()     
        self.driver.find_element(By.ID, "CmdKensaku").click()
        self.driver.find_element(By.ID, "JigyoShoriChkBox_8").click()
        self.driver.find_element(By.ID, "span_CmdKoushin").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()