import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

# 転用元シナリオ：TestQAG020_01100103
class TestQAG020_01100403(FukushiSiteTestCaseBase):
    """TestQAG020_01100403"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 精神手帳自立支援医療進達簿を出力できることを確認する。進達内容を登録できることを確認する。
    def test_QAG020_01100403(self):
        """進達入力"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG020")

        # シナリオ非表示のため飛ばして実行(進達内容を登録できることを確認する。)
        # # 1 自立支援医療(精神通院)資格管理画面: 「戻る」ボタン押下
        # self.return_click()

        # # 2 受給状況画面: 表示
        # self.screen_shot("受給状況画面_2")

        # # 3 受給状況画面: 「戻る」ボタン押下
        # self.return_click()

        # # 4 個人検索画面: 表示
        # self.screen_shot("個人検索画面_4")

        # # 5 個人検索画面: 「戻る」ボタン押下
        # self.return_click()

        # # 6 メインメニュー画面: 表示
        # self.screen_shot("メインメニュー画面_6")

        # # 7 メインメニュー画面: 「精神手帳・通院進達」ボタン押下
        # self.click_button_by_label("精神手帳・通院進達")

        # # 8 精神手帳・通院進達対象者検索画面: 表示
        # self.screen_shot("精神手帳・通院進達対象者検索画面_8")

        # # 9 メインメニュー画面: 表示
        # self.screen_shot("メインメニュー画面_9")

        # # 10 メインメニュー画面: 「精神手帳・通院進達」ボタン押下
        # self.click_button_by_label("精神手帳・通院進達")

        # # 11 精神手帳・通院進達対象者検索画面: 表示
        # self.screen_shot("精神手帳・通院進達対象者検索画面_11")

        # # 12 精神手帳・通院進達対象者検索画面: 業務「障害」選択事業「精神手帳」選択
        # self.form_input_by_id(idstr="Gyomu", text="障害")
        # self.form_input_by_id(idstr="Jigyo", text="精神手帳")

        # # 13 精神手帳・通院進達対象者検索画面: 「確定」ボタン押下
        # self.click_button_by_label("確定")

        # # 14 精神手帳・通院進達対象者検索画面: 表示
        # self.screen_shot("精神手帳・通院進達対象者検索画面_14")

        # # 15 精神手帳・通院進達対象者検索画面: 申請日「○○」入力
        # self.form_input_by_id(idstr="ShinseiKaishiYMD", value="20240701")

        # # 16 精神手帳・通院進達対象者検索画面: 「検索」ボタン押下
        # self.click_button_by_label("検索")

        # # 17 精神手帳・通院進達対象者一覧画面: 表示
        # self.screen_shot("精神手帳・通院進達対象者一覧画面_17")

        # # 18 精神手帳・通院進達対象者一覧画面: 進達日「○○」入力進達先「（パラメータ指定）」入力文書記号「（パラメータ指定）」入力文書番号（パラメータ指定）」入力枝番「（パラメータ指定）」入力
        # # NG 進達先、文書記号、文書番号、枝番が見当たりません。
        # self.form_input_by_id(idstr="ShintatsuYMD1", value="20240701")

        # # 19 精神手帳・通院進達対象者一覧画面: 住民コードが一致する行のチェックボックス選択
        # self.form_input_by_id(idstr="ChkSentakuNo_1", value="1")

        # # 20 精神手帳・通院進達対象者一覧画面: 表示
        # self.screen_shot("精神手帳・通院進達対象者一覧画面_20")

        # # 21 精神手帳・通院進達対象者一覧画面: 「センター進達情報」ボタン押下
        # self.click_button_by_label("センター進達情報")

        # # 22 精神手帳・通院進達対象者一覧画面: 表示
        # self.screen_shot("精神手帳・通院進達対象者一覧画面_23")

        # # 23 精神手帳・通院進達対象者一覧画面: 「住所」ボタン押下
        # self.click_button_by_label("住所")

        # # 24 精神手帳・通院進達対象者一覧画面: 表示
        # self.screen_shot("精神手帳・通院進達対象者一覧画面_24")

        # # 25 精神手帳・通院進達対象者一覧画面: 「自立支援医療（精神通院）」ボタン押下
        # self.click_button_by_label("自立支援医療（精神通院）")

        # # 26 精神手帳・通院進達対象者一覧画面: 表示
        # self.screen_shot("精神手帳・通院進達対象者一覧画面_26")

        # # 27 精神手帳・通院進達対象者一覧画面: 「登録」ボタン押下
        # self.pdf_output_and_download(button_id="TourokuBtn", case_name=" 精神手帳・通院進達対象者一覧画面")
        # # self.click_button_by_label("登録")

        # # 28 精神手帳・通院進達対象者一覧画面: 「ファイルを開く(O)」ボタンを押下
        # # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        # # self.assert_message_area("登録しました ")
        # # self.screen_shot("精神手帳・通院進達対象者一覧画面_28")

        # # 29 精神手帳・通院進達対象者一覧画面: 表示
        # # self.screen_shot("精神手帳・通院進達対象者一覧画面_29")

        # # 30 精神手帳・通院進達対象者一覧画面: ×ボタン押下でPDFを閉じる

        # # 31 精神手帳・通院進達対象者一覧画面: 「戻る」ボタン押下
        # self.return_click()

        # # 32 メインメニュー画面: 表示
        # self.do_login()
        # self.screen_shot("メインメニュー画面_32")

        # # 33 メインメニュー画面: 「バッチ起動」ボタン押下
        # self.batch_kidou_click()

        # # 34 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_34")

        # # 35 バッチ起動画面: 業務「障害」選択
        # self.form_input_by_id(idstr="GyomuSelect", text="障害")

        # # 36 バッチ起動画面: 事業「自立支援医療（精神通院）」選択
        # self.form_input_by_id(idstr="JigyoSelect", text="自立支援医療(精神通院)")

        # # 37 バッチ起動画面: 処理区分「随時処理」選択
        # self.form_input_by_id(idstr="ShoriKubunSelect", text="随時処理")

        # # 38 バッチ起動画面: 処理分類「進達一覧作成」選択
        # self.form_input_by_id(idstr="ShoriBunruiSelect", text="進達一覧作成")

        # # 39 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_39")

        # # 40 バッチ起動画面: 「障害者手帳交付申請者一覧_進達_再出力処理」の行の数字ボタン押下
        # self.click_batch_job_button_by_label("障害者手帳交付申請者一覧_進達_再出力処理")

        # # 41 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_41")

        # # 42 バッチ起動画面: 「進達年月日開始」入力「進達年月日終了」入力「文書番号」入力
        # # NG no ID
        # params = [
        #     {"title": "進達年月日開始", "type": "text", "value": "20230702"},
        #     {"title": "進達年月日終了", "type": "text", "value": "20230706"},
        #     {"title": "文書番号", "type": "text", "value": "test"}
        # ]
        # self.set_job_params(params)
        # self.screen_shot("バッチ起動画面_42")

        # # 43 バッチ起動画面: 「処理開始」ボタン押下
        # self.exec_batch_job()

        # # 44 バッチ起動画面: 「ファイルを開く(O)」ボタンを押下
        # # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        # self.print_online_reports(case_name="バッチ起動画面")
        # # self.click_button_by_label("ファイルを開く(O)")
        # # self.assert_message_area("プレビューを表示しました ")

        # # 45 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_46")

        # # 46 バッチ起動画面: ×ボタン押下でPDFを閉じ
        # # self.screen_shot("バッチ起動画面_46")

        # # 47 バッチ起動画面: 「実行履歴」ボタン押下
        # self.click_job_exec_log()

        # # 48 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_49")

        # # 49 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.click_job_exec_log_search()

        # # 50 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_50")

        # # 51 ジョブ実行履歴画面: 「戻る」ボタン押下
        # self.return_click()

        # # 52 メインメニュー画面: 表示
        # self.screen_shot("メインメニュー画面_52")

        # 資格管理画面まで遷移
        self.click_by_id(idstr="CmdButton1_1")

        # 1 自立支援医療(精神通院)資格管理画面: 「進達入力」ボタン押下
        self.click_button_by_label("進達入力")

        # 2 自立支援医療(精神通院)資格管理画面: 進達年月日「20230701」入力 判定予定日「20230701」入力 判定予定時間「15:00」入力 進達番号「12345」入力
        time.sleep(1)
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=case_data.get("shintatsu_ymd", ""))
        self.form_input_by_id(idstr="TxtHanteiYMD", value=case_data.get("hantei_ymd", ""))
        self.form_input_by_id(idstr="TxtHanteiYotei_Ji", value="15")
        self.form_input_by_id(idstr="TxtHanteiYotei_Fun", value="00")
        self.form_input_by_id(idstr="TxtShintatsuBango", value="12345")
        self.form_input_by_id(idstr="TxtUketsukeBango", value="")
        self.form_input_by_id(idstr="TxtShinseiYMD", value=case_data.get("shintatsu_ymd", ""))
        # 3 自立支援医療(精神通院)資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 4 自立支援医療(精神通院)資格管理画面: 表示
        # メッセージエリアに「登録しました」と表示されていることを確認する
        self.assert_message_area("登録しました")
        self.screen_shot("資格管理画面_4")