import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG020_01109906(FukushiSiteTestCaseBase):
    """TestQAG020_01109906"""
    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    def test_QAG020_01109906(self):
        """住記異動リスト出力処理、住記異動自動喪失処理"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 2 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_2")

        # 3 バッチ起動画面: 業務：障害事業：自立支援医療(精神通院)処理区分：バッチ処理　処理分類：月次処理
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="自立支援医療(精神)")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="バッチ処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="帳票出力処理（随時）")
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 「住記異動リスト出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("住記異動リスト出力処理")
        

        # 5 バッチ起動画面: 申請年月日はじめ「20241011」を入力申請年月日おわり「20241011」を入力
        params = [
            {"title": "更新年月日（はじめ）", "type": "text", "value": case_data.get("start_ymd", "")},
            {"title": "更新年月日（おわり）", "type": "text", "value": case_data.get("end_ymd", "")}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_5")
        # 6 バッチ起動画面: 「処理開始」ボタン押下
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 7 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 8 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_8")

        # 9 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_9")

        # 10 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 11 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 12 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_12")

        # 13 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 14 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_14")

        # 15 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 16 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 17 バッチ起動画面: 「住記異動自動喪失処理」のNoボタン押下
        self.click_batch_job_button_by_label("住記異動自動喪失処理")

        # 18 バッチ起動画面: 申請年月日はじめ「20241011」を入力申請年月日おわり「20241011」を入力
        params = [
            {"title": "住記異動日（はじめ）", "type": "text", "value": case_data.get("start_ymd", "")},
            {"title": "住記異動日（おわり）", "type": "text", "value": case_data.get("end_ymd", "")}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_18")

        # 19 バッチ起動画面: 「処理開始」ボタン押下
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 20 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 21 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_21")

        # 22 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 23 ジョブ帳票履歴画面: 「戻る」ボタン押下
        self.return_click()

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG020")

        # 24 自立支援医療(精神通院)資格管理画面: 表示
        self.screen_shot("自立支援医療(精神通院)資格管理画面_24")
