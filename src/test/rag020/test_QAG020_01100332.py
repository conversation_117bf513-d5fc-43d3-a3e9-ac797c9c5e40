import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG020_01100322(FukushiSiteTestCaseBase):
    """TestQAG020_01100322"""
    #変更決定通知書のバッチ処理は未実装のためオンライン帳票のみにしているためスキップとする。
    @unittest.skip('skipped')
    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 変更決定通知書を出力できることを確認する。
    def test_QAG020_01100332(self):
        """変更決定通知書作成"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        ymd_1 = case_data.get("ymd_1", "")
        ymd_2 = case_data.get("ymd_2", "")
        value_1 = case_data.get("value_1", "")

        self.do_login()
        # 1 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 2 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_2")

        # 3 バッチ起動画面: 業務：障害事業：自立支援医療(精神通院)処理区分：バッチ処理処理分類：月次処理
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="自立支援医療(精神)")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="バッチ処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="月次処理")
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 「変更決定通知書一括出力」のNoボタン押下
        self.click_batch_job_button_by_label("変更決定通知書一括出力")

        # 5 バッチ起動画面: 福祉事務所コード「○○」選択
        params = [
            {"title": "福祉事務所コード", "type": "text", "value": "○○"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 文書記号「○○」文書番号「○○」
        params = [
            {"title": "文書記号", "type": "text", "value": "○○"},
            {"title": "文書番号", "type": "text", "value": "○○"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_6")

        # 7 バッチ起動画面: 決定年月日＜はじめ＞「20240801」を入力決定年月日＜おわり＞「20240801」を入力申請種別「3:変更」を入力発行年月日「20240801」を入力
        params = [
            {"title": "決定年月日", "type": "text", "value": ymd_1},
            {"title": "決定年月日", "type": "text", "value": ymd_1},
            {"title": "申請種別", "type": "text", "value": value_1},
            {"title": "発行年月日", "type": "select", "value": ymd_1}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: 決定年月日＜はじめ＞「20240901」を入力決定年月日＜おわり＞「20240901」を入力申請種別「3:変更」を入力発行年月日「20240901」を入力
        params = [
            {"title": "決定年月日", "type": "text", "value": ymd_2},
            {"title": "決定年月日", "type": "text", "value": ymd_2},
            {"title": "申請種別", "type": "text", "value": value_1},
            {"title": "発行年月日", "type": "select", "value": ymd_2}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 10 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 11 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_11")

        # 12 ジョブ実行履歴画面: 「検索」ボタン押下
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 13 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_13")

        # 14 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 15 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_15")

        # 16 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 17 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「変更決定通知書」のNoボタン押下
        # self.click_batch_job_button_by_label("変更決定通知書")

        # 19 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 20 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_20")

        # 22 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 23 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_23")
