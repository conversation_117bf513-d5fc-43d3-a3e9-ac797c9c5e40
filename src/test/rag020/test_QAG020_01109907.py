import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG020_01109907(FukushiSiteTestCaseBase):
    """TestQAG020_01109907"""
    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 副本データができることを確認する。
    def test_QAG020_01109907(self):
        """精神通院_所得照会抽出"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 2 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_2")

        # 3 バッチ起動画面: 業務：障害事業：自立支援医療(精神通院)処理区分：副本登録処理分類：特定個人情報の管理番号８
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="自立支援医療(精神)")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="副本登録")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="特定個人情報の管理番号８")
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 「精神通院_副本登録用データ抽出処理」のNoボタン押下
        self.click_batch_job_button_by_label("精神通院_副本登録用データ抽出処理")

        # 5 バッチ起動画面: 「処理開始」ボタン押下
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 6 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 7 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_7")

        # 8 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 9 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_9")
