import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC020_1050301(FukushiSiteTestCaseBase):
    """TestQAC020_1050301"""

    def setUp(self):
        case_data = self.test_data["TestQAC020_1050301"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC020", "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
                      "TARGET_NENDO":case_data.get("nendo", ""),"TARGET_SOUSYOTOKU":case_data.get("soushotoku", "")}
        self.exec_sqlfile("Test_QAC020_1050301.sql", params=sql_params)
        super().setUp()

    # 資格喪失届を提出した住民に対し、その他必要な情報の登録ができることを確認する。
    def test_QAC020_1050301(self):
        """届出情報入力"""

        case_data = self.test_data["TestQAC020_1050301"]
        atena_code = case_data.get("atena_code", "")
        shinseishubetsu = case_data.get("shinseishubetsu", "")
        shinseiriyu = case_data.get("shinseiriyu", "")
        shinseiymd = case_data.get("shinseiymd", "")
        soshitsuymd = case_data.get("soshitsuymd", "")

        ### 認定履歴作成 START###
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC020")
        # 認定請求・申請登録
        self.click_button_by_label("申請内容入力")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="新規")
        self.click_button_by_label("確定")

        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230101")
        self.form_input_by_id(idstr="SeiyakuumuChkBox", value="1")
        self.form_input_by_id(idstr="Shougai1Cmb", text="肢体不自由")
        self.form_input_by_id(idstr="TxtNinteiYMD1", value="20251031")
        self.form_input_by_id(idstr="SelectHanyo1", text="三 両上肢の機能に著しい障害を有するもの")

        # 福祉世帯
        self.open_common_buttons_area()
        self.click_button_by_label("福祉世帯情報")
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230101")
        self.form_input_by_id(idstr="ChkFlg_2", value="1")
        self.form_input_by_id(idstr="ChkFlg_3", value="1")
        self.click_button_by_label("入力完了") 

        self.form_input_by_id(idstr="TxtKaitei", value="202302")
        self.click_button_by_label("月額計算")  # Button with ID: CmdGetsugakuKeisan
        self.click_button_by_label("登録")
        self.alert_ok()

        self.click_button_by_label("決定内容入力")
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230101")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        ### 認定履歴作成 END###

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 5 個人検索画面: 「検索」ボタン押下

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「障害児福祉手当」ボタン押下
        self.click_button_by_label("障害児福祉手当")

        # 8 "障害児福祉手当 受給者台帳画面": 表示
        self.screen_shot("障害児福祉手当 受給者台帳画面_8")

        # 9 "障害児福祉手当 受給者台帳画面": 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 "障害児福祉手当 受給者台帳画面": "申請種別「資格喪失」選択 申請理由「死亡」選択"
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinseishubetsu)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinseiriyu)
        self.screen_shot("障害児福祉手当 受給者台帳画面_10")

        # 11 "障害児福祉手当 受給者台帳画面": 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 "障害児福祉手当 受給者台帳画面": "申請日「20230701」 資格喪失日「20230630」"
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinseiymd)
        self.form_input_by_id(idstr="TxtKaitei", value=soshitsuymd)
        self.screen_shot("障害児福祉手当 受給者台帳画面_12")

        # 13 "障害児福祉手当 受給者台帳画面": 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 14 "障害児福祉手当 受給者台帳画面": 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 15 "障害児福祉手当 受給者台帳画面": 表示 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("障害児福祉手当 受給者台帳画面_15")
