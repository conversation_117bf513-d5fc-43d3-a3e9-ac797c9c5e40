import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC020_1050101(FukushiSiteTestCaseBase):
    """TestQAC020_1050101"""

    def setUp(self):
        case_data = self.test_data["TestQAC020_1050101"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC020", "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
                      "TARGET_NENDO":case_data.get("nendo", ""),"TARGET_SOUSYOTOKU":case_data.get("soushotoku", "")}
        self.exec_sqlfile("Test_QAC020_1050101.sql", params=sql_params)
        super().setUp()

    # 認定申請した住民およびその他必要な情報の登録ができることを確認する。
    def test_QAC020_1050101(self):
        """申請情報登録"""

        case_data = self.test_data["TestQAC020_1050101"]
        atena_code = case_data.get("atena_code", "")
        shinsei_ymd = case_data.get("shinsei_ymd", "")
        shokan_ku = case_data.get("shokan_ku", "")
        shinsei_shubetsu_cmb = case_data.get("shinsei_shubetsu_cmb", "")
        shinsei_siyuu_cmb = case_data.get("shinsei_siyuu_cmb", "")
        nintei_ymd = case_data.get("nintei_ymd", "")
        kaitei_ymd = case_data.get("kaitei_ymd", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()  # Button with ID: CmdProcess1_1

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面:
        self.screen_shot("個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「障害児福祉手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC020")  # ID QAC020

        # 8 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_8")

        # 9 障害児福祉手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")  # Button with ID: CmdShinsei

        # 10 障害児福祉手当資格管理画面: 申請種別「認定請求」選択申請理由「新規」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="新規")
        self.screen_shot("障害児福祉手当資格管理画面_10")

        # 11 障害児福祉手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")  # Button with ID: CmdKakutei

        # 12 障害児福祉手当資格管理画面: 申請日「20230401」担当所管区「第一区」選択誓約有無「チェック」
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinsei_ymd)
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text=shokan_ku)
        self.form_input_by_id(idstr="SeiyakuumuChkBox", value="1")
        self.screen_shot("障害児福祉手当資格管理画面_12")

        # 13 障害児福祉手当資格管理画面: 「手帳情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("手帳情報")  # Button with ID: btnCommon11

        # 14 手帳情報画面: 表示
        self.screen_shot("手帳情報画面_14")

        # 15 手帳情報画面: 「戻る」ボタン押下
        self.return_click()

        # 16 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_16")

        # 17 障害児福祉手当資格管理画面: 障害区分１「肢体不自由」選択有期認定年月１「20251031」認定基準１「三 両上肢の機能に著しい障害を有するもの又は両上肢の全ての指を欠くもの若しくは両上肢の全ての指の機能に著しい障害を有するもの」選択
        self.form_input_by_id(idstr="Shougai1Cmb", text="肢体不自由")
        self.form_input_by_id(idstr="TxtNinteiYMD1", value=nintei_ymd)
        self.form_input_by_id(idstr="SelectHanyo1", text="三 両上肢の機能に著しい障害を有するもの")
        self.screen_shot("障害児福祉手当資格管理画面_17")

        # 18 障害児福祉手当資格管理画面: 「障害程度審査情報1」ボタン押下
        self.click_button_by_label("障害程度審査情報１")  # Button with ID: CmdShougaiShinsa1

        # 19 障害審査情報画面: 表示
        self.screen_shot("障害審査情報画面_19")

        # 20 障害審査情報画面: 判定機関依頼日「20230401」判定機関判定能力判定「能力判定結果を記載」資格判定方法「審査機関」選択資格判定内容「診断書」選択
        self.form_input_by_id(idstr="TxtHanteiYMD", value=20230401)
        self.form_input_by_id(idstr="TxtNouryoku", value="能力判定結果を記載")
        self.form_input_by_id(idstr="CmbHRiyu", text="審査機関")
        self.form_input_by_id(idstr="CmbHNaiyou", text="診断書")

        # 21 障害審査情報画面: 表示
        self.screen_shot("障害審査情報画面_21")

        # 22 障害審査情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")  # Button with ID: CmdNKanryo

        # 23 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_23")

        # 24 障害児福祉手当資格管理画面: 「福祉世帯情報」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("福祉世帯情報")  # Button with ID: btnCommon2

        # 25 福祉世帯情報画面: 表示
        self.screen_shot("福祉世帯情報画面_25")

        # 26 福祉世帯情報画面: 本人から見た続柄「本人」選択受給者との関係「本人」該当日「20230501」
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value=20230501)
        self.form_input_by_id(idstr="ChkFlg_2", value="1")
        self.form_input_by_id(idstr="ChkFlg_3", value="1")

        # 27 福祉世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")  # Button with ID: CmdNKanryo

        # 28 障害児福祉手当資格管理画面: 「所得情報」ボタン押下
        self.click_button_by_label("所得情報")  # Button with ID: btnCommon3

        # 29 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_29")

        # 30 住民税世帯情報画面: 対象者世帯一覧「1」ボタン押下
        self.click_button_by_label("1")  # Button with ID: CmdNo0 instead of self.click_by_id("CmdNo0")

        # 31 住民税個人情報画面: 表示
        self.screen_shot("住民税個人情報画面_27")

        # 32 住民税個人情報画面: 「戻る」ボタン押下
        self.return_click()

        # 33 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_29")

        # 34 住民税世帯情報画面: 「戻る」ボタン押下
        self.return_click()

        # 35 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_35")

        # 36 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_36")

        # 37 障害児福祉手当資格管理画面: 「メモ情報」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("メモ情報")  # Button with ID: btnCommon12

        # 38 メモ情報画面: 表示
        self.screen_shot("メモ情報画面_38")

        # 39 メモ情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")  # self.click_by_id("CmdTsuika")

        # 40 メモ情報画面: 内容「１２３４５テスト」
        self.form_input_by_id(idstr="TxtNaiyo", value="１２３４５テスト")
        self.screen_shot("メモ情報画面_40")

        # 41 メモ情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")  # self.click_by_id("CmdToroku")
        self.alert_ok()

        # 42 メモ情報画面: 「戻る」ボタン押下
        self.return_click()

        # 43 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_43")

        # 44 障害児福祉手当資格管理画面: 「提出書類管理」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("提出書類管理")  # Button with ID: btnCommon15

        # 45 提出書類管理: 表示
        self.screen_shot("提出書類管理_45")

        # 46 提出書類管理: 「追加」ボタン押下
        self.click_button_by_label("追加")  # Button with ID: BtnTsuika

        # 47 提出書類管理: 認定診断書にチェック
        self.form_input_by_id(idstr="ChkTeisyutsu_6", value="1")
        self.screen_shot("提出書類管理_47")

        # 48 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")  # self.click_by_id("BtnKanryo")

        # 49 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_49")

        # 50 障害児福祉手当資格管理画面: 「住所管理」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("住所管理")  # self.click_by_id("btnCommon14")

        # 51 住所管理画面: 表示
        self.screen_shot("住所管理画面_51")

        # 52 住所管理画面: 「追加」ボタン押下
        self.click_button_by_label("追加")  # button with ID: CmdInsert

        # 53 住所管理画面: 「住記情報索引」ボタン押下
        self.click_button_by_label("住記情報索引")  # Button with ID: CmdZukiInfo
        self.screen_shot("住所管理画面_53")

        # 54 住所管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")  # Button with ID: CmdSubmit
        self.alert_ok()

        # 55 住所管理画面: 「戻る」ボタン押下
        self.return_click()

        # 56 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_56")

        # 57 障害児福祉手当資格管理画面: 「連絡先管理」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("連絡先管理")  # Button with ID: btnCommon13

        # 58 連絡先管理: 表示
        self.screen_shot("連絡先管理_58")

        # 59 連絡先管理: 本人連絡先の「追加」ボタン押下
        self.click_button_by_label("追加")  # Button with ID: BtnTsuika_Honnin

        # 60 連絡先管理: 優先順位「携帯電話番号」選択公開/非公開「公開」チェック自宅電話番号「************」携帯電話番号「090-1234-5678」
        self.form_input_by_id(idstr="CmbYusenTEL", text="携帯電話番号")
        self.form_input_by_id(idstr="kokai0", value="1")
        self.form_input_by_id(idstr="TxtTelJitaku", value="************")
        self.form_input_by_id(idstr="TxtTelKeitai", value="090-1234-5678")
        self.screen_shot("連絡先管理_60")

        # 61 連絡先管理: 「登録」ボタン押下
        self.click_button_by_label("登録")  # button with ID: BtnTouroku_Honnin
        self.alert_ok()

        # 62 連絡先管理: 「戻る」ボタン押下
        self.return_click()

        # 63 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_63")

        # 64 障害児福祉手当資格管理画面: 「口座情報」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("口座情報")  # self.click_by_id("btnCommon5")

        # 65 口座情報画面: 表示
        self.screen_shot("口座情報画面_65")

        # 66 口座情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")  # Button with ID: 口座情報_u_Tuika

        # 67 口座情報画面: 有効期間開始「20230501」金融機関コード「0005」支店コード「001」口座種別「普通」選択口座番号「77777777」公開/非公開「公開」チェック
        self.entry_kouza_info(
            start_ymd=kaitei_ymd,
            ginko_code="0005",
            shiten_code="001",
            kouza_shubetsu_text="普通",
            kouza_bango="7777777",
            koukai=True
        )
        self.screen_shot("口座情報画面_67")

        # 68 口座情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")  # Button with ID: 口座情報_u_Touroku
        self.alert_ok()

        # 69 口座情報画面: 「戻る」ボタン押下
        self.return_click()

        # 70 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_70")

        # 71 障害児福祉手当資格管理画面: 開始年月「202305」
        self.form_input_by_id(idstr="TxtKaitei", value="202305")
        self.screen_shot("障害児福祉手当資格管理画面_71")

        # 72 障害児福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")  # Button with ID: CmdGetsugakuKeisan

        # 73 障害児福祉手当資格管理画面: 「所得判定詳細情報」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("所得判定詳細情報")  # button with ID: btnCommon6

        # 74 所得判定詳細情報画面: 表示
        self.screen_shot("所得判定詳細情報画面_74")

        # 75 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 76 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_76")

        # 77 障害児福祉手当資格管理画面: 「住記情報」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("住記情報")  # Button with ID: btnCommon4

        # 78 世帯一覧画面: 表示
        self.screen_shot("世帯一覧画面_78")

        # 79 世帯一覧画面: 世帯一覧「1」Noボタン押下
        self.click_button_by_label("1")  # Instead of self.click_by_id("NoBtn1")

        # 80 住記情報: 表示
        self.screen_shot("住記情報_80")

        # 81 住記情報: 「戻る」ボタン押下
        self.return_click()

        # 82 世帯一覧画面: 表示
        self.screen_shot("世帯一覧画面_82")

        # 83 世帯一覧画面: 「戻る」ボタン押下
        self.return_click()

        # 84 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_84")

        # 85 障害児福祉手当資格管理画面: 「生活保護」ボタン押下
        self.find_common_buttons_open_button().click()
        can_shintatsu_button = self.click_button_by_label("生活保護情報")
        if (can_shintatsu_button):
            self.click_button_by_label("生活保護情報")  # not sure because of the ID not found

            # 86 生活保護情報画面: 表示
            self.screen_shot("生活保護情報画面_86")

            # 87 生活保護情報画面: 「戻る」ボタン押下
            self.return_click()

        # 88 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_88")

        # 89 障害児福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")  # button with ID: CmdGetsugakuKeisan

        # 90 障害児福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")  # button with ID: CmdTouroku
        self.alert_ok()

        # 91 障害児福祉手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("障害児福祉手当資格管理画面_91")
