import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC020_1050303(FukushiSiteTestCaseBase):
    """TestQAC020_1050303"""

    def setUp(self):
        #case_data = self.common_test_data.get(self.__class__.__name__, {})
        case_data = self.test_data["TestQAC020_1050303"]
        super().setUp()

    # 資格喪失届を提出した住民に対し決定登録ができることを確認する。
    def test_QAC020_1050303(self):
        """資格喪失処理"""

        #case_data = self.common_test_data.get(self.__class__.__name__, {})
        case_data = self.test_data["TestQAC020_1050303"]
        atena_code = case_data.get("atena_code", "")
        teishutsuymd = case_data.get("teishutsuymd", "")
        ketteiymd = case_data.get("ketteiymd", "")
        ketteikekka = case_data.get("ketteikekka", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC020")
        # self.do_login()
        # 1 "障害児福祉手当 受給者台帳画面": 「修正」ボタン押下
        self.click_button_by_label("修正")
        self.click_button_by_label("確定")

        # 2 "障害児福祉手当 受給者台帳画面": 「提出書類管理」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("提出書類管理")

        # 3 提出書類管理: 表示
        self.screen_shot("提出書類管理_3")

        # 4 提出書類管理: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 5 提出書類管理: その他の提出日「20230702」
        self.form_input_by_id(idstr="TxtSyoruiYMD_8", value=teishutsuymd)
        self.screen_shot("提出書類管理_5")

        # 6 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 7 "障害児福祉手当 受給者台帳画面": 表示
        self.screen_shot("障害児福祉手当 受給者台帳画面_7")

        # 8 "障害児福祉手当 受給者台帳画面": 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 9 "障害児福祉手当 受給者台帳画面": 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 10 "障害児福祉手当 受給者台帳画面": 表示 「登録しました」のメッセージチェック
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("障害児福祉手当 受給者台帳画面_10")

        # 11 "障害児福祉手当 受給者台帳画面": 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 12 "障害児福祉手当 受給者台帳画面": "判定日「20230702」 判定結果「決定」"
        self.form_input_by_id(idstr="TxtKetteiYMD", value=ketteiymd)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=ketteikekka)
        self.screen_shot("障害児福祉手当 受給者台帳画面_12")

        # 13 "障害児福祉手当 受給者台帳画面": 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 14 "障害児福祉手当 受給者台帳画面": 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 15 "障害児福祉手当 受給者台帳画面": 表示 「登録しました」のメッセージチェック
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("障害児福祉手当 受給者台帳画面_15")
