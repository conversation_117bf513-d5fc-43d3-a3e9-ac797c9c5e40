import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC020_1050108(FukushiSiteTestCaseBase):
    """TestQAC020_1050108"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 転入決定者に対し、オンラインにて支給開始通知書が出力できることを確認する。
    def test_QAC020_1050108(self):
        """支給開始通知書作成"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        self.screen_shot("個人検索画面_5")

        # 5 個人検索画面: 「検索」ボタン押下

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「障害児福祉手当」ボタン押下
        self.click_button_by_label("障害児福祉手当")

        # 8 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_8")

        # 9 障害児福祉手当資格管理画面: 「進達入力」ボタン押下
        self.click_button_by_label("進達入力")

        # 10 障害児福祉手当資格管理画面: 進達日「20230502」進達判定年月日「20230502」進達結果「該当」
        self.form_input_by_id(idstr="TxtShintatsuYMD", value=20230502)
        self.form_input_by_id(idstr="TxtShintatsuHanteiYMD", value=20230502)
        self.form_input_by_id(idstr="ShintasuHanteiCmb", text="該当")
        self.screen_shot("障害児福祉手当資格管理画面_10")

        # 11 障害児福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 12 障害児福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 13 障害児福祉手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("障害児福祉手当資格管理画面_13")

        # 14 障害児福祉手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 15 障害児福祉手当資格管理画面: 判定日「20230502」判定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value=20230502)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.screen_shot("障害児福祉手当資格管理画面_15")

        # 16 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_16")

        # 17 障害児福祉手当資格管理画面: 「所得判定詳細情報」ボタン押下
        self.click_button_by_label("所得判定詳細情報")

        # 18 所得判定詳細情報画面: 表示
        self.screen_shot("所得判定詳細情報画面_18")

        # 19 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 20 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_20")

        # 21 障害児福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 22 障害児福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 23 障害児福祉手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("障害児福祉手当資格管理画面_23")

        # 24 障害児福祉手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 25 資格管理画面: 表示
        self.screen_shot("資格管理画面_25")

        # 26 帳票印刷画面: 「障害児福祉手当支給開始決定通知書」行の印刷チェックボックス選択「障害児福祉手当支給開始決定通知書」行の発行年月日チェックボックス選択「障害児福祉手当支給開始決定通知書」行の発行年月日「20230502」
        self.print_online_reports(case_name="帳票印刷画面", report_name="障害児福祉手当支給開始決定通知書", hakkou_ymd="20230502")
        self.screen_shot("帳票印刷画面_26")

        # 27 帳票印刷画面: 「印刷」ボタン押下

        # 28 帳票印刷画面: 障害児福祉手当支給開始決定通知書「ファイルを開く(O)」ボタンを押下
        # Assert: 「プレビューを表示しました」のメッセージチェック

        # 29 障害児福祉手当支給開始決定通知書（PDF）: 表示
        self.screen_shot("障害児福祉手当支給開始決定通知書（PDF）_29")

        # 30 障害児福祉手当支給開始決定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 31 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_31")
