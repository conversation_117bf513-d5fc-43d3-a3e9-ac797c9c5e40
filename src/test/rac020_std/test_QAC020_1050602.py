import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC020_1050602(FukushiSiteTestCaseBase):
    """TestQAC020_1050602"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 以下のEUCが正しく抽出できることを確認する。・障害児福祉手当支払集計表・障害児福祉手当支払集計表_根拠・障害児福祉手当支給明細書
    def test_QAC020_1050602(self):
        """必要に応じて業務データの抽出・確認_支払関連_"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 EUC画面: 表示
        self.transit_euc_std()
        self.screen_shot("EUC画面_1")

        # 2 EUC画面: テーブル参照「障害児福祉手当支払集計表」ダブルクリック
        self.get_euc_table(table_name="障害児福祉手当支払集計表")

        # 3 EUC_障害児福祉手当支払集計表タグ画面: 表示

        # 4 EUC_障害児福祉手当支払集計表タグ画面: 「検索」ボタン押下

        # 5 [結果]EUC_障害児福祉手当支払集計表タグ画面: 表示

        # 6 [結果]EUC_障害児福祉手当支払集計表タグ画面: ×ボタン押下で閉じる

        # 7 EUC_障害児福祉手当支払集計表タグ画面: 表示

        # 8 EUC_障害児福祉手当支払集計表タグ画面: ×ボタン押下で閉じる

        # 9 EUC画面: 表示
        self.screen_shot("EUC画面_9")

        # 10 EUC画面: テーブル参照「障害児福祉手当支払集計表_根拠」ダブルクリック
        self.get_euc_table(table_name="障害児福祉手当支払集計表_根拠")

        # 11 EUC_障害児福祉手当支払集計表_根拠タグ画面: 表示

        # 12 EUC_障害児福祉手当支払集計表_根拠タグ画面: 「検索」ボタン押下

        # 13 [結果]EUC_障害児福祉手当支払集計表_根拠タグ画面: 表示

        # 14 [結果]EUC_障害児福祉手当支払集計表_根拠タグ画面: ×ボタン押下で閉じる

        # 15 EUC_障害児福祉手当支払集計表_根拠タグ画面: 表示

        # 16 EUC_障害児福祉手当支払集計表_根拠タグ画面: ×ボタン押下で閉じる

        # 17 EUC画面: 表示
        self.screen_shot("EUC画面_17")

        # 18 EUC画面: テーブル参照「障害児福祉手当支給明細書」ダブルクリック
        self.get_euc_table(table_name="障害児福祉手当支給明細書")

        # 19 EUC_障害児福祉手当支給明細書タグ画面: 表示

        # 20 EUC_障害児福祉手当支給明細書タグ画面: 「検索」ボタン押下

        # 21 [結果]EUC_障害児福祉手当支給明細書タグ画面: 表示

        # 22 [結果]EUC_障害児福祉手当支給明細書タグ画面: ×ボタン押下で閉じる

        # 23 EUC_障害児福祉手当支給明細書タグ画面: 表示

        # 24 EUC_障害児福祉手当支給明細書タグ画面: ×ボタン押下で閉じる

        # 25 EUC画面: 表示
        self.screen_shot("EUC画面_25")
