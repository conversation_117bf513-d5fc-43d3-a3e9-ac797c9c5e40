import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC020_1050501(FukushiSiteTestCaseBase):
    """TestQAC020_1050501"""

    def setUp(self):
        case_data = self.test_data["TestQAC020_1050501"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC020", "DELETE_ATENA_CODE": case_data.get("atena_code", ""), "TARGET_NENDO": case_data.get("nendoY", "")}
        self.exec_sqlfile("Test_QAC020_1050501.sql", params=sql_params)
        super().setUp()

    # 差止情報を登録し、対象者への差止通知書、差止解除者へ差止解除通知書がオンライン帳票で出力できることを確認する。
    def test_QAC020_1050501(self):
        """差止、差止解除通知書の作成"""

        case_data = self.test_data["TestQAC020_1050501"]
        atena_code = case_data.get("atena_code", "")
        shinseiShubetsu = case_data.get("shinseiShubetsu", "")
        shinseiRiyuu = case_data.get("shinseiRiyuu", "")
        shinseiYMD = case_data.get("shinseiYMD", "")
        seiyakuumu = case_data.get("seiyakuumu", "")
        shougai1 = case_data.get("shougai1", "")
        ninteiYMD1 = case_data.get("ninteiYMD1", "")
        hanyo1 = case_data.get("hanyo1", "")
        hanteiYMD = case_data.get("hanteiYMD", "")
        hRiyu = case_data.get("hRiyu", "")
        hNaiyou = case_data.get("hNaiyou", "")
        gaitouYMD = case_data.get("gaitouYMD", "")
        txtKaitei = case_data.get("txtKaitei", "")
        shintasuBtn = case_data.get("shintasuBtn", "")
        shintatsuYMD = case_data.get("shintatsuYMD", "")
        shintatsuHanteiYMD = case_data.get("shintatsuHanteiYMD", "")
        shintasuHantei = case_data.get("shintasuHantei", "")
        ketteiYMD = case_data.get("ketteiYMD", "")
        ketteiKekka = case_data.get("ketteiKekka", "")
        cmb_riyu = case_data.get("cmb_riyu", "")
        kettei = case_data.get("kettei", "")
        kaishi = case_data.get("kaishi", "")
        hakko = case_data.get("hakko", "")
        kaizyo = case_data.get("kaizyo", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        self.screen_shot("個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「障害児福祉手当」ボタン押下
        self.click_button_by_label("障害児福祉手当")

        # 8 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_8")

        # ＜対象者の条件＞
        # ・障害児福祉手当の資格を持っている住民
        # ・令和5年8月の未支払情報ありの住民
        self.click_button_by_label("申請内容入力")
        # 申請種別
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinseiShubetsu)
        # 申請理由
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinseiRiyuu)
        self.click_button_by_label("確定")
        # 申請日
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinseiYMD)
        # 誓約有無
        self.form_input_by_id(idstr="SeiyakuumuChkBox", value=seiyakuumu)
        # 障害区分１
        self.form_input_by_id(idstr="Shougai1Cmb", text=shougai1)
        # 有期認定年月１
        self.form_input_by_id(idstr="TxtNinteiYMD1", value=ninteiYMD1)
        # 認定基準１
        self.form_input_by_id(idstr="SelectHanyo1", text=hanyo1)
        self.click_button_by_label("障害程度審査情報1")
        # 判定機関判定依頼日
        self.form_input_by_id(idstr="TxtHanteiYMD", value=hanteiYMD)
        # 資格判定方法
        self.form_input_by_id(idstr="CmbHRiyu", text=hRiyu)
        # 資格判定内容
        self.form_input_by_id(idstr="CmbHNaiyou", text=hNaiyou)
        self.click_button_by_label("入力完了")
        self.click_button_by_label("福祉世帯情報")
        self.fukushi_setai_entry_helper(gaitou_ymd=gaitouYMD, sonota_sakujo_flg=True)
        self.click_button_by_label("入力完了")
        # 支給開始年月
        self.form_input_by_id(idstr="TxtKaitei", value=txtKaitei)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        can_shintatsu_button = self.click_button_by_label(shintasuBtn)
        if (can_shintatsu_button):
            self.form_input_by_id(idstr="TxtShintatsuYMD", value=shintatsuYMD)
            self.form_input_by_id(idstr="TxtShintatsuHanteiYMD", value=shintatsuHanteiYMD)
            self.form_input_by_id(idstr="ShintasuHanteiCmb", text=shintasuHantei)
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました。")

        self.click_button_by_label("決定内容入力")
        self.form_input_by_id(idstr="TxtKetteiYMD", value=ketteiYMD)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=ketteiKekka)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        # 9 障害児福祉手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 10 障害児福祉手当資格管理画面: 「差止情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="差止情報")

        # 11 差止情報画面: 表示
        self.screen_shot("差止情報画面_11")

        # 12 差止情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 13 差止情報画面: 表示
        self.screen_shot("差止情報画面_13")

        # 14 差止情報画面: 差止理由「その他」差止決定日「20230810」差止年月「202308」
        self.form_input_by_id(idstr="CmbRiyu", text=cmb_riyu)
        self.form_input_by_id(idstr="TxtKettei", value=kettei)
        self.form_input_by_id(idstr="TxtKaishi", value=kaishi)
        self.screen_shot("差止情報画面_14")

        # 15 差止情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 16 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_16")

        # 17 障害児福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 18 障害児福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 19 障害児福祉手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("障害児福祉手当資格管理画面_19")

        # 20 障害児福祉手当資格管理画面: 「支払履歴」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="支払履歴")

        # 21 支払履歴画面: 表示
        self.screen_shot("支払履歴画面_21")

        # 22 支払履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 23 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_24")

        # 24 障害児福祉手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 25 障害児福祉手当資格管理画面: 「差止情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="差止情報")

        # 26 差止情報画面: 「No.1」ボタン押下
        self.click_button_by_label("1")

        # 27 差止情報画面: 「印刷」ボタン押下
        self.pdf_output_and_download_no_alert(button_id="CmdPrint",case_name="障害児福祉手当一時差止通知書")

        # 28 差止情報画面: 障害児福祉手当一時差止通知書「ファイルを開く(O)」ボタンを押下
        # Assert: 「プレビューを表示しました」のメッセージチェック
        self.assert_message_area("プレビューを表示しました")

        # 29 障害児福祉手当一時差止通知書（PDF）: 表示
        # self.screen_shot("障害児福祉手当一時差止通知書（PDF）_29")

        # 30 障害児福祉手当一時差止通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 31 差止情報画面: 表示
        self.screen_shot("差止情報画面_31")

        # 32 差止情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 33 差止情報画面: 表示
        self.screen_shot("差止情報画面_33")

        # 34 差止情報画面: 差止解除年月日「20230831」
        self.form_input_by_id(idstr="TxtKaizyo", value=kaizyo)
        self.screen_shot("差止情報画面_34")

        # 35 差止情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 36 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_36")

        # 37 障害児福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 38 障害児福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 39 障害児福祉手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("障害児福祉手当資格管理画面_39")

        # 40 障害児福祉手当資格管理画面: 「支払履歴」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="支払履歴")

        # 41 支払履歴画面: 表示
        self.screen_shot("支払履歴画面_41")

        # 42 支払履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 43 障害児福祉手当資格管理画面: 表示
        self.screen_shot("障害児福祉手当資格管理画面_43")

        # 44 障害児福祉手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 45 障害児福祉手当資格管理画面: 「差止情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="差止情報")

        # 46 差止情報画面: 表示
        self.screen_shot("差止情報画面_46")

        # 47 差止情報画面: 「No.1」ボタン押下
        self.click_by_id("Sel1")
        self.form_input_by_id(idstr="TxtHakko", value=hakko)

        # 48 差止情報画面: 「印刷」ボタン押下
        self.pdf_output_and_download_no_alert(button_id="CmdPrint",case_name="障害児福祉手当一時差止解除通知書")

        # 49 差止情報画面: 障害児福祉手当一時差止解除通知書「ファイルを開く(O)」ボタンを押下
        # Assert: 「プレビューを表示しました」のメッセージチェック
        self.assert_message_area("プレビューを表示しました")

        # 50 障害児福祉手当一時差止解除通知書（PDF）: 表示
        # self.screen_shot("障害児福祉手当一時差止解除通知書（PDF）_50")

        # 51 障害児福祉手当一時差止解除通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 52 差止情報画面: 表示
        self.screen_shot("差止情報画面_52")
