import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC020_1050201(FukushiSiteTestCaseBase):
    """TestQAC020_1050201"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 認定請求書が出力できることを確認する。　※特障、障児
    def test_QAC020_1050201(self):
        """認定請求書出力"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        report_name = case_data.get("report_name", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        self.screen_shot("個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下

        # 6 受給状況画面:表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面:「障害児福祉手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC020")

        # 8 "障害児福祉手当 資格管理画面": 表示
        self.screen_shot("障害児福祉手当 資格管理画面_8")

        # 9 "障害児福祉手当 資格管理画面": 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 10 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_10")

        # 11 帳票印刷画面: 「障害児福祉手当認定請求書」行の印刷チェックボックス選択
        exec_count = self.print_online_reports(case_name="帳票印刷画面", report_name=report_name)
        self.screen_shot("帳票印刷画面_11")

        # 12 帳票印刷画面: 「印刷」ボタン押下

        # 13 帳票印刷画面: 障害児福祉手当認定請求書「ファイルを開く(O)」ボタンを押下 「プレビューを表示しました」のメッセージチェック

        # 14 障害児福祉手当認定請求書（PDF）: 表示
        # self.screen_shot("障害児福祉手当認定請求書（PDF）_14")

        # 15 障害児福祉手当認定請求書（PDF）: ×ボタン押下でPDFを閉じる

        # 16 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 17 "障害児福祉手当 資格管理画面": 表示
        self.screen_shot("障害児福祉手当 資格管理画面_17")
