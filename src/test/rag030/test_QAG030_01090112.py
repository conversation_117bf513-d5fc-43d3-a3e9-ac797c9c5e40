import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG030_01090112(FukushiSiteTestCaseBase):
    """TestQAG030_01090112"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 新規申請した住民に対し決定登録ができることを確認する。受給者番号が付与されていることを確認する。
    def test_QAG030_01090112(self):
        """認定結果の登録_決定_"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG030")
        # 1 自立支援医療(育成医療)資格管理: 「修正」ボタン押下
        self.click_by_id(idstr="CmdButton1_1")
        self.click_button_by_label("修正")

        # 2 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_2")

        # 3 自立支援医療(育成医療)資格管理: 却下理由削除
        self.form_input_by_id(idstr="TxtAreaKyakkaRiyu", value="")

        # 4 自立支援医療(育成医療)資格管理: 決定日「20230701」入力決定結果「決定」選択
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230701")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        
        # 5 自立支援医療(育成医療)資格管理: 有効期間開始日「20230701」入力「有効開始日」ボタン押下
        self.form_input_by_id(idstr="TxtKaishiYMD", value="20230701")
        self.click_button_by_label("有効期間")

        # 6 自立支援医療(育成医療)資格管理: 経過的特例有効期間開始日「（空白）」経過的特例有効期間終了日「（空白）」受給者証適用開始日「20230701」入力
        self.form_input_by_id(idstr="TxtKeikaTokureiKaishiYMD", value="")
        self.form_input_by_id(idstr="TxtKeikaTokureiShuryoYMD", value="")
        self.form_input_by_id(idstr="TxtJukyushashoTekiyoYMD", value="20230701")

        # 7 自立支援医療(育成医療)資格管理: 備考「備考入力テスト８９★１２３４５６７８９★」入力
        self.form_input_by_id(idstr="TxtAreaBiko", value="備考入力テスト８９★１２３４５６７８９★")

        # 8 自立支援医療(育成医療)資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 9 自立支援医療(育成医療)資格管理: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("自立支援医療(育成医療)資格管理_9")

        # 10 自立支援医療(育成医療)資格管理: 表示
        # Assert: 受給者番号が発番されたことを確認する。
        self.screen_shot("自立支援医療(育成医療)資格管理_10")
