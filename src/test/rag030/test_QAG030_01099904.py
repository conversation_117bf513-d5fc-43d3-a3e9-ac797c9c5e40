import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG030_01099904(FukushiSiteTestCaseBase):
    """TestQAG030_01099904"""
    
    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 「実績調査_別添様式4自立支援医療_育成医療の実績」を出力できることを確認する。
    def test_QAG030_01099904(self):
        """実績調査_別添様式2自立支援医療における支給認定の状況"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        ymd_1 = case_data.get("ymd_1", "")
        ymd_2 = case_data.get("ymd_2", "")

        self.do_login()
        # 1 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 2 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_2")

        # 3 バッチ起動画面: 業務：障害 | 事業：自立支援医療(更生医療) | 処理区分：バッチ処理 | 処理分類：統計処理
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="自立支援医療(更生)")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="バッチ処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="統計処理")
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 「実績調査_別添様式4自立支援医療_育成医療の実績」のNoボタン押下
        self.click_batch_job_button_by_label("実績調査_別添様式2自立支援医療における支給認定の状況")

        # 5 バッチ起動画面: 決定年月日開始「○○」を入力 | 決定年月日終了「○○」を入力
        # 請求対象年月開始  「○○」を入力 | 請求対象年月終了「○○」を入力
        params = [
            {"title": "決定年月日 はじめ", "type": "text", "value":ymd_1},
            {"title": "決定年月日 おわり", "type": "text", "value": ymd_2}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「処理開始」ボタン押下
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 7 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 8 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_8")

        # 9 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 10 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_10")

        # 11 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 12 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_12")

        # 13 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)        
        self.screen_shot("ジョブ帳票履歴画面_13")

        # 15 ジョブ帳票履歴画面: 「実績調査（別添様式4自立支援医療（育成医療）の実績）」のNoボタン押下
        # self.click_button_by_label("実績調査（別添様式4自立支援医療（育成医療）の実績）")

        # 16 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # self.click_button_by_label("ファイルを開く")

        # 17 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_17")

        # 18 帳票（PDF）: ×ボタン押下でPDFを閉じる
