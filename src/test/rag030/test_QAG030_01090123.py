import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG030_01090123(FukushiSiteTestCaseBase):
    """TestQAG030_01090123"""
    
    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code")
        params = {"DELETE_ATENA_CODE": atena_code}
        self.exec_sqlfile("RAG030_01090104.sql", params=params)
        super().setUp()

    # 年金照会（個別照会）の依頼情報作成ができることを確認する。実装未着手のため、シナリオはスキップしています。
    def test_QAG030_01090123(self):
        """年金照会_個別照会_"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        shokai_cmb_insert = case_data.get("shokai_cmb_insert", "")
        shokai_insert = case_data.get("shokai_insert", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG030")
        self.click_by_id(idstr="CmdButton1_1")
        # 1 自立支援医療(育成医療)資格管理: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 自立支援医療(育成医療)資格管理: 「年金照会先情報」ボタン押下
        self.click_button_by_label("年金照会先情報")

        # 3 照会先機関登録画面: 表示
        self.screen_shot("照会先機関登録画面_3")

        # 4 照会先機関登録画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 5 照会先機関登録画面: 照会先追加登録対象　本人と父に対して処理「追加」選択
        self.find_element(By.ID, "ShoriCmb_Insert_1").send_keys(shokai_cmb_insert)
        self.find_element(By.ID, "ShoriCmb_Insert_2").send_keys(shokai_cmb_insert)
        
        # 6 照会先機関登録画面: 照会先機関名「日本年金機構」入力 
        self.find_element(By.ID, "TxtShokai_Insert_1").send_keys(shokai_insert)
        self.find_element(By.ID, "TxtShokai_Insert_2").send_keys(shokai_insert)
        
        # 7 照会先機関登録画面: 「一括設定」ボタン押下
        self.click_button_by_label("一括設定")

        # 8 照会先機関登録画面: 「登録」ボタン押下
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")
        
        # 9 照会先機関登録画面: 表示
        self.screen_shot("照会先機関登録画面_9")

        # 10 照会先機関登録画面: 「情報照会」ボタン押下
        self.click_button_by_label("情報照会")

        # 11 バッチ起動画面: 「処理開始」ボタン押下
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 12 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_12")

        # 13 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 14 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 15 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 17 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_17")

        # 18 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 19 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 21 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_21")

        # 22 ジョブ帳票履歴画面: 「照会対象者一覧」のNoボタン押下
        self.click_button_by_label("照会対象者一覧")

        # 23 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 24 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_24")

        # 25 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 26 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_26")

        # 27 ジョブ帳票履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 28 照会先機関登録画面: 「戻る」ボタン押下
        self.return_click()

        # 29 自立支援医療(育成医療)資格管理: 表示
        self.click_button_by_label("確定")
        self.screen_shot("自立支援医療(育成医療)資格管理_29")

        # 30 自立支援医療(育成医療)資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 31 自立支援医療(育成医療)資格管理: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("自立支援医療(育成医療)資格管理_31")

