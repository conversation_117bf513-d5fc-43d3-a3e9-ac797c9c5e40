import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG030_01090301(FukushiSiteTestCaseBase):
    """TestQAG030_01090301"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 【パターン：完全な新規】育成医療未申請者に対して、自立支援医療費支給認定申請書を出力できることを確認する。
    def test_QAG030_01090301(self):
        """自立支援医療費支給認定申請書出力"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        report_name = case_data.get("report_name", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「自立支援医療(育成医療)」ボタン押下
        # self.click_button_by_label("自立支援医療(育成医療)")
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAG030")

        # 8 履歴選択画面: 表示
        self.screen_shot("履歴選択画面_8")

        # 9 履歴選択画面: 「新規資格登録」ボタン押下
        self.click_button_by_label("新規資格登録")

        # 10 履歴選択画面: 「資格履歴」ボタン押下
        # self.click_button_by_label("資格履歴")

        # 11 自立支援医療(育成医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理画面_11")

        # 12 自立支援医療(育成医療)資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")
        self.click_by_id("kirikae")
        # 13 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_13")

        # 14 帳票印刷画面: 「自立支援医療費支給認定申請書」行の印刷チェックボックス選択「自立支援医療費支給認定申請書」行の発行年月日チェックボックス選択発行年月日「○○」入力
        self.form_input_by_id(idstr="insatsuChk_0", value="1")
        self.form_input_by_id(idstr="TxtHakkoYMD", value=case_data.get("hakkou_ymd", ""))
        self.click_by_id(idstr="CmdHYReflect")
        self.screen_shot("帳票印刷画面_14")

        # 15 帳票印刷画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")
        self.alert_ok()
        # 16 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        # 17 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_17")
        self.screen_shot("帳票印刷画面_15")
        # 18 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 19 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 20 自立支援医療(育成医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理画面_20")
