import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG030_01090509(FukushiSiteTestCaseBase):
    """TestQAG030_01090509"""
    
    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # レセプトエラーチェックを実行し、エラー内容を一覧で確認する。・診療年月日に住民登録があるかどうか・資格の受給者番号と一致するか・診療年月が有効期間内かどうか・台帳に登録されている医療機関かどうか・重複請求かどうか・負担上限月額を超えていないか・加入保険が一致しているかどうか・特定疾病療養受領証を使用しているか・公費負担番号が一致しているか
    def test_QAG030_01090509(self):
        """請求情報点検：レセプトエラーチェックの実行_国保_"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        ymd_1 = case_data.get("ymd_1", "")
        hoken_kubu = case_data.get("hoken_kubu", "")
        self.do_login()
        # 1 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 2 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_2")

        # 3 バッチ起動画面: 業務：障害事業：自立支援医療(育成医療)処理区分：バッチ処理処理分類：月次処理
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="自立支援医療(育成)")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="バッチ処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="レセプト処理")
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 「レセプトエラーチェック」のNoボタン押下
        self.click_batch_job_button_by_label("レセプトエラーチェック")

        # 5 バッチ起動画面: 請求年月「○○」を入力
        params = [
            {"title": "請求年月", "type": "text", "value": ymd_1, 
             "title": "保険区分", "type": "selected", "value": hoken_kubu}  # Item with ID: NG
        ]   
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_6")

        # 7 バッチ起動画面: 「処理開始」ボタン押下
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 8 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 9 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_9")

        # 10 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 11 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_11")

        # 12 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 13 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_13")

        # 14 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 15 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_15")

        # 16 ジョブ帳票履歴画面: 「レセプトエラーリスト」のNoボタン押下

        # 17 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 18 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_18")

        # 19 帳票（PDF）: ×ボタン押下でPDFを閉じる
