import time
import unittest
from datetime import datetime, date, timedelta
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.select import Select
class TestQAP010_28010401(KodomoSiteTestCaseBase):
    """TestQAP010_28010401"""

    def setUp(self):
        settings = self.common_test_data
        self.test_data = settings.get("test_qap010_28010401")
        atena_list = settings.get("test_qap010_28010401")
        self.exec_sqlfile("QAP010_28010401_更新スクリプト.sql", params=atena_list)
        super().setUp()
    
    # 現況届出力対象者の抽出が行えることを確認する。
    def test_QAP010_28010401(self):
        """対象者抽出"""
        
        driver = None

        self.do_login()
        """
        # 1 メインメニュー画面: 表示
        self.save_screenshot_migrate(driver, "メインメニュー 画面_1" , True)
        
        # 2 メインメニュー画面: 「バッチ管理」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        time.sleep(1)
        
        # 3 メインメニュー画面: 「即時実行」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 4 メインメニュー画面: 「スケジュール個別追加」ボタンをダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)
        
        # 5 バッチ管理画面: 業務名：子ども子育て支援 サブシステム名：入所 処理名：現況処理
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '入所']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '現況処理']").click()
        self.save_screenshot_migrate(driver, "バッチ管理画面_5" , True)
        
        # 6 バッチ管理画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()
        
        # 7 バッチ起動画面: 現況処理 「(QP2BN06200) 現況届提出対象者データ作成」のNoボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()

        # 8 バッチ起動画面: 発行年月日：  を入力 基準日：  を入力 出力区分：施設等利用給付認定 を選択 指定年月日：  を入力 並び順：  を選択 再発行区分： を選択
        # Assert: パラメータ化
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").send_keys(formatted_date)
        # 基準日
        self.find_element_by_id("tab01_ZEAF002200_Paratest36_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest36_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest36_textboxInput").send_keys(self.test_data.get("case_qap001_kijun_bi"))
        # 出力区分
        self.find_element_by_id("tab01_ZEAF002200_QApara0094_select").click()
        self.find_element_by_id("tab01_ZEAF002200_QApara0094_select").send_keys(self.test_data.get("case_qap001_shutsuryoku_kubun"))
        # 指定年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest59_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest59_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest59_textboxInput").send_keys(self.test_data.get("case_qap001_shitei_nengappi"))
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabi_jun"))
        # 再発行区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").send_keys(self.test_data.get("case_qap001_saihakkou_kubun"))
        self.save_screenshot_migrate(driver, "バッチ管理画面_8" , True)
        
        # 9 バッチ起動画面: 「実行」ボタンを押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()

        # 10 実行結果画面: 表示
        # Assert: 「(QP2BN06200) 現況届提出対象者データ作成」の状態が正常終了となることを確認
        time.sleep(60)
        # 画面読み込みのため「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF002300_BtnKensaku_button").click()
        self.save_screenshot_migrate(driver, "実行結果画面_10" , True)
        
        # 11 実行結果画面: 「No1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 12 結果確認画面: 表示
        self.save_screenshot_migrate(driver, "結果確認画面_12" , True)
        
        # 13 結果確認画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()

        # 14 ファイルダウンロード: 「No1」ボタン押下→#14~16は手動で実施。
        time.sleep(20)
        # 15 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 16 ログ: 表示
        self.save_screenshot_migrate(driver, "ログ_18" , True)
        
        # 17 結果確認画面: 「納品物確認」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # 18 結果確認画面: 表示
        
        # 19 納品物管理画面: 現況届提出対象者データ.csv 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()
        self.save_screenshot_migrate(driver, "納品物管理画面_19" , True)
        
        # 20 ファイルダウンロード: 「No1」ボタン押下→#20~23は手動で実施。
        time.sleep(20)
        # 21 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 22 帳票（PDF）: 表示
        self.screen_shot("帳票（PDF）_24")
        # 23 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
        # 24 納品物管理画面: 現況届一覧.pdf 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnDownload_2_9_button").click()
        self.save_screenshot_migrate(driver, "納品物管理画面_24" , True)
        
        # 25 ファイルダウンロード: 「No1」ボタン押下→#25~28は手動で実施。
        time.sleep(20)
        # 26 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 27 帳票（PDF）: 表示
        self.save_screenshot_migrate(driver, "帳票（PDF）_27" , True)
        # 28 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
        # 29 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.find_element_by_xpath("//*[@id='tab01_ZEAF002700_navi']/li[1]/a").click()

        # 30 バッチ起動画面: 現況処理 「(QP2BN06300) 保育所入所(支給認定)現況届出力」のNoボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_2_4_button").click()

        # 31 バッチ起動画面: 発行年月日： を入力
        # Assert: パラメータ化
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").send_keys(formatted_date)
        self.save_screenshot_migrate(driver, "バッチ起動画面_31" , True)
        
        # 32 バッチ起動画面: 「実行」ボタンを押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()

        # 33 実行結果画面: 表示
        # Assert: 「(QP2BN06200) 現況届提出対象者データ作成」の状態が正常終了となることを確認
        time.sleep(60)
        # 画面読み込みのため「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF002300_BtnKensaku_button").click()
        self.save_screenshot_migrate(driver, "実行結果画面_33" , True)
        
        # 34 実行結果画面: 「No1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 35 結果確認画面: 表示
        self.save_screenshot_migrate(driver, "結果確認画面_35" , True)
        
        # 36 結果確認画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()

        # 37 ファイルダウンロード: 「No1」ボタン押下→#37~39は手動で実施。
        time.sleep(20)
        # 38 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 39 ログ: 表示
        self.save_screenshot_migrate(driver, "ログ_39" , True)
        
        # 40 結果確認画面: 「納品物確認」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()
        
        # 41 結果確認画面: 表示
        
        # 42 納品物管理画面: 現況届.pdf 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()
        self.save_screenshot_migrate(driver, "納品物管理画面_42" , True)
        
        # 43 ファイルダウンロード: 「No1」ボタン押下→#43~46は手動で実施。
        time.sleep(20)
        # 44 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 45 帳票（PDF）: 表示
        self.save_screenshot_migrate(driver, "帳票（PDF）_45" , True)
        # 46 帳票（PDF）: ×ボタン押下でPDFを閉じる
        """
        # 47 メインメニュー画面: 表示
        #self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー画面_1" , True)
        
        # 48 メインメニュー画面: 「バッチ管理」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        time.sleep(1)
        # 49 メインメニュー画面: 「バッチ管理」ボタン押下
        
        # 50 メインメニュー画面: 「即時実行」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 51 メインメニュー画面: 「スケジュール個別追加」ボタンをダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 52 バッチ管理画面: 業務名：子ども子育て支援 サブシステム名：入所 処理名：現況処理
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '入所']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '現況処理(世帯別)']").click()
        self.save_screenshot_migrate(driver, "バッチ管理画面_5" , True)
        
        # 53 バッチ管理画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()
        
        # 54 バッチ起動画面: 現況処理（世帯別） 「(QP2BN09200) 現況届提出対象者データ作成(世帯別)」のNoボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()

        # 55 バッチ起動画面: 発行年月日：  を入力 基準日：  を入力 出力区分： 施設等利用給付認定　を選択 指定年月日：  を入力 並び順：  を選択 再発行区分： を選択
        # Assert: パラメータ化
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").send_keys(formatted_date)
        # 基準日
        self.find_element_by_id("tab01_ZEAF002200_Paratest36_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest36_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest36_textboxInput").send_keys(formatted_date)
        # 出力区分
        self.find_element_by_id("tab01_ZEAF002200_QApara0094_select").click()
        self.find_element_by_id("tab01_ZEAF002200_QApara0094_select").send_keys(self.test_data.get("case_qap001_shutsuryoku_kubun1"))
        # 指定年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest59_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest59_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest59_textboxInput").send_keys(self.test_data.get("case_qap001_shitei_nengappi1"))
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabi_jun1"))
        # 再発行区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").send_keys(self.test_data.get("case_qap001_saihakkou_kubun1"))
        self.save_screenshot_migrate(driver, "バッチ起動画面_8" , True)
        
        # 56 バッチ起動画面: 「実行」ボタンを押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        # 57 実行結果画面: 表示
        # Assert: 「(QP2BN06200) 現況届提出対象者データ作成」の状態が正常終了となることを確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果画面_10" , True)
        
        # 58 実行結果画面: 「No1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()

        # 59 結果確認画面: 表示
        self.save_screenshot_migrate(driver, "結果確認画面_12" , True)
        
        # 60 結果確認画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()
        
        # 61 ファイルダウンロード: 「No1」ボタン押下
        # 62 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 63 ログ: 表示
        self.save_screenshot_migrate(driver, "ログ_16" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 64 結果確認画面: 「納品物確認」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：教育・保育給付認定取消通知書
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_1 = self.test_data.get("ShoriNM_1")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_1)

        # 65 結果確認画面: 表示
        # 66 納品物管理画面: 現況届提出対象者データ.csv 「ダウンロード」ボタン押下
        #self.screen_shot("納品物管理画面_66")
        # 67 ファイルダウンロード: 「No1」ボタン押下
        # 68 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 69 帳票（PDF）: 表示
        #self.screen_shot("帳票（PDF）_69")
        # 70 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
        # 71 納品物管理画面: 現況届一覧(世帯別).pdf 「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()
        #self.save_screenshot_migrate(driver, "納品物管理画面_71" , True)
        
        # 72 ファイルダウンロード: 「No1」ボタン押下
        # 73 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 74 帳票（PDF）: 表示
        # 75 帳票（PDF）: ×ボタン押下でPDFを閉じる
        self.pdf_download("QAPP130200_現況届一覧(世帯別).pdf", "帳票（PDF）_19")
        self.click_button_by_label("閉じる")
        time.sleep(2)       
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 76 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.find_element_by_xpath("//*[@id='tab01_ZEAF002700_navi']/li[1]/a").click()

        # 77 バッチ起動画面: 現況処理 「(QP2BN09300) 保育所入所(支給認定)現況届出力(世帯別)」のNoボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_2_4_button").click()

        # 78 バッチ起動画面: 発行年月日： を入力
        # Assert: パラメータ化
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").send_keys(formatted_date)
        self.save_screenshot_migrate(driver, "バッチ起動画面_26" , True)
        
        # 79 バッチ起動画面: 「実行」ボタンを押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        # 80 実行結果画面: 表示
        # Assert: 「(QP2BN06200) 現況届提出対象者データ作成」の状態が正常終了となることを確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果画面_28" , True)
        
        # 81 実行結果画面: 「No1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 82 結果確認画面: 表示
        self.save_screenshot_migrate(driver, "結果確認画面_30" , True)
        
        # 83 結果確認画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()

        # 84 ファイルダウンロード: 「No1」ボタン押下
        # 85 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 86 ログ: 表示
        self.save_screenshot_migrate(driver, "ログ_34" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 87 結果確認画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：教育・保育給付認定取消通知書
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_1 = self.test_data.get("ShoriNM_1")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_1)

        # 88 結果確認画面: 表示
        # 89 納品物管理画面: 現況届(世帯別).pdf 「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()
        #self.save_screenshot_migrate(driver, "納品物管理画面_89" , True)
        
        # 90 ファイルダウンロード: 「No1」ボタン押下
        # 91 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 92 帳票（PDF）: 表示
        # 93 帳票（PDF）: ×ボタン押下でPDFを閉じる
        self.pdf_download("QAPP130300_現況届(世帯別).pdf", "帳票（PDF）_37")
        self.click_button_by_label("閉じる")
        time.sleep(2)

        # 89 納品物管理画面: 現況届(世帯別)_桁あふれリスト.csv 「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_2_9_button").click()
        #self.save_screenshot_migrate(driver, "納品物管理画面_89" , True)
        
        # 90 ファイルダウンロード: 「No1」ボタン押下
        # 91 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 92 帳票（CSV）: 表示
        # 93 帳票（CSV）: ×ボタン押下でCSVを閉じる
        #self.pdf_download("QAPP130300_現況届(世帯別)_桁あふれリスト.csv", "帳票（CSV）_92")
        #self.click_button_by_label("閉じる")
        #time.sleep(2)

        # 89 納品物管理画面: 現況届(世帯別)_欠字リスト.csv 「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_3_9_button").click()
        #self.save_screenshot_migrate(driver, "納品物管理画面_89" , True)
        
        # 90 ファイルダウンロード: 「No1」ボタン押下
        # 91 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 92 帳票（CSV）: 表示
        # 93 帳票（CSV）: ×ボタン押下でCSVを閉じる
        #self.pdf_download("QAPP130300_現況届(世帯別)_欠字リスト.csv", "帳票（CSV）_92")
        #self.click_button_by_label("閉じる")
        #time.sleep(2)       
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 94 メインメニュー画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー画面_42" , True)
        
        # 95 メインメニュー画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)
        # 96 メインメニュー画面: 「バッチ管理」ボタン押下
        
        # 97 メインメニュー画面: 「即時実行」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 98 メインメニュー画面: 「スケジュール個別追加」ボタンをダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 99 バッチ管理画面: 業務名：子ども子育て支援 サブシステム名：入所 処理名：入所継続届出力処理
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '入所']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '入所継続届出力処理']").click()
        self.save_screenshot_migrate(driver, "バッチ管理画面_46" , True)
        
        # 100 バッチ管理画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 101 バッチ起動画面: 入所継続届出力処理  「(QP2BN07900)入所継続届出力処理」のNoボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()
        
        # 102 バッチ起動画面: 支所：  を入力 基準日：  を入力 世帯台帳番号： を選択 施設種類：  を入力 施設コード：  を選択 発行年月日：  を入力 受託区分：  を選択 再発行区分：  を選択 並び順：  を選択
        # Assert: パラメータ化
        # 支所
        self.find_element_by_id("tab01_ZEAF002200_Paratest03_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest03_select").send_keys(self.test_data.get("case_qap001_shisho"))
        # 世帯台帳番号
        self.find_element_by_id("tab01_ZEAF002200_Paratest260_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest260_textboxInput").send_keys(self.test_data.get("case_qap001_setai_daichou_bangou"))
        # 施設種類
        self.find_element_by_id("tab01_ZEAF002200_Paratest43_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest43_select").send_keys(self.test_data.get("case_qap001_shisetsu_shurui"))
        # 施設コード
        self.find_element_by_id("tab01_ZEAF002200_Paratest23_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest23_textboxInput").send_keys(self.test_data.get("case_qap001_shisetsu_code"))
        # 受託区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest265_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest265_select").send_keys(self.test_data.get("case_qap001_jutaku_kubun"))
        # 再発行区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").send_keys(self.test_data.get("case_qap001_saihakkou_kubun2"))
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabi_jun2"))
        self.save_screenshot_migrate(driver, "バッチ管理画面_49" , True)

        # 103 バッチ起動画面: 「実行」ボタンを押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 104 実行結果画面: 表示
        # Assert: 「(QP2BN07900)入所継続届出力処理」の状態が正常終了となることを確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果画面_51" , True)
        
        # 105 実行結果画面: 「No1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()

        # 106 結果確認画面: 表示
        self.save_screenshot_migrate(driver, "結果確認画面_53" , True)
        
        # 107 結果確認画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()

        # 108 ファイルダウンロード: 「No1」ボタン押下
        # 109 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 110 ログ: 表示
        self.save_screenshot_migrate(driver, "ログ_57" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)

        # 111 結果確認画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：教育・保育給付認定取消通知書
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_2 = self.test_data.get("ShoriNM_2")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_2)

        # 112 結果確認画面: 表示
        # 113 納品物管理画面: 施設上限年齢に達す警告リスト.csv 「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_2_9_button").click()
        #self.save_screenshot_migrate(driver, "納品物管理画面_113" , True)
        
        # 114 ファイルダウンロード: 「No1」ボタン押下
        # 115 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 116 帳票（CSV）: 表示
        # 117 帳票（CSV）: ×ボタン押下でCSVを閉じる
        self.pdf_download("施設上限年齢に達す警告リスト.csv", "帳票（CSV）_60")
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 118 納品物管理画面: 入所継続届対象者一覧.pdf 「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()
        #self.save_screenshot_migrate(driver, "納品物管理画面_118" , True)
        
        # 119 ファイルダウンロード: 「No1」ボタン押下
        # 120 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 121 帳票（PDF）: 表示
        # 122 帳票（PDF）: ×ボタン押下でPDFを閉じる
        self.pdf_download("QAPP120200_入所継続届対象者一覧.pdf", "帳票（PDF）_65")
        self.click_button_by_label("閉じる")
        time.sleep(2)       
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 123 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.find_element_by_xpath("//*[@id='tab01_ZEAF002400_navi']/li[1]/a").click()

        # 124 バッチ起動画面: 入所継続届出力 「(QP2BN08000) 入所継続届出力」のNoボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_2_4_button").click()

        # 125 バッチ起動画面: 発行年月日： を入力
        # Assert: パラメータ化
        #self.save_screenshot_migrate(driver, "バッチ起動画面_125" , True)
        
        # 126 バッチ起動画面: 「実行」ボタンを押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        # 127 実行結果画面: 表示
        # Assert: 「(QP2BN08000) 入所継続届出力」の状態が正常終了となることを確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果画面_73" , True)
        
        # 128 実行結果画面: 「No1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 129 結果確認画面: 表示
        self.save_screenshot_migrate(driver, "結果確認画面_75" , True)
        
        # 130 結果確認画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()

        # 131 ファイルダウンロード: 「No1」ボタン押下
        # 132 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 133 ログ: 表示
        self.save_screenshot_migrate(driver, "ログ_79" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 134 結果確認画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：教育・保育給付認定取消通知書
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_2 = self.test_data.get("ShoriNM_2")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_2)

        # 135 結果確認画面: 表示
        # 136 納品物管理画面: QAPP120300_入所継続届書.pdf 「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()
        #self.save_screenshot_migrate(driver, "納品物管理画面_136" , True)
        
        # 137 ファイルダウンロード: 「No1」ボタン押下
        # 138 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 139 帳票（PDF）: 表示
        # 140 帳票（PDF）: ×ボタン押下でPDFを閉じる
        self.pdf_download("QAPP120300_入所継続届書.pdf", "帳票（PDF）_82")
        self.click_button_by_label("閉じる")
        time.sleep(2)

        # 136 納品物管理画面: QAPP120300_入所継続届書_欠字リスト.csv 「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_3_9_button").click()
        #self.save_screenshot_migrate(driver, "納品物管理画面_136" , True)
        
        # 137 ファイルダウンロード: 「No1」ボタン押下
        # 138 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 139 帳票（CSV）: 表示
        # 140 帳票（CSV）: ×ボタン押下でCSVを閉じる
        #self.pdf_download("QAPP120300_入所継続届書_欠字リスト.csv", "帳票（CSV）_139")
        #self.click_button_by_label("閉じる")
        #time.sleep(2)

        # 136 納品物管理画面: QAPP120300_入所継続届書_桁あふれリスト.csv 「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_2_9_button").click()
        #self.save_screenshot_migrate(driver, "納品物管理画面_136" , True)
        
        # 137 ファイルダウンロード: 「No1」ボタン押下
        # 138 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 139 帳票（CSV）: 表示
        # 140 帳票（CSV）: ×ボタン押下でCSVを閉じる
        #self.pdf_download("QAPP120300_入所継続届書_桁あふれリスト.csv", "帳票（CSV）_139")
        #self.click_button_by_label("閉じる")
        #time.sleep(2)       
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # 141 メインメニュー画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー画面_87" , True)
        
        # 142 メインメニュー画面: 「子ども子育て支援」ボタン押下
        self._goto_kodomo()

        # 143 メインメニュー画面: 「入所管理」ボタン押下
        self._goto_nyuusyokannri()

        # 144 メインメニュー画面: 「現況消込」ボタンをダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'子ども子育て支援')]/ul/li[contains(span,'入所管理')]/ul/li[contains(span,'現況消込')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 145 汎用現況消込対象検索画面: 消込区分「バーコード」を選択
        self.find_element_by_id("tab01_QAZF002300_selKeshikomiKbn_select").click()
        self.find_element_by_xpath("//option[. = 'バーコード']").click()

        # 146 汎用現況消込対象検索画面: 提出年月日「 」を選択
        # Assert: パラメータ化
        # 提出年月日
        self.find_element_by_id("tab01_QAZF002300_txtKensakuTeishutuYMD_textboxInput").click()
        self.find_element_by_id("tab01_QAZF002300_txtKensakuTeishutuYMD_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_QAZF002300_txtKensakuTeishutuYMD_textboxInput").send_keys(formatted_date)
        self.save_screenshot_migrate(driver, "汎用現況消込対象検索画面_92" , True)
        
        # 147 汎用現況消込対象検索画面: 現況種類「 施設等利用給付認定」を選択
        # Assert: パラメータ化
        # 現況種類
        self.find_element_by_id("tab01_QAZF002300_selGenkyoShurui_select").click()
        self.find_element_by_xpath("//option[. = '施設等利用給付認定']").click()
        self.save_screenshot_migrate(driver, "汎用現況消込対象検索画面_93" , True)
        
        # 148 汎用現況消込対象検索画面: バーコード「 」を選択
        # Assert: パラメータ化
        # バーコード
        self.find_element_by_id("tab01_QAZF002300_txtBarcode_textboxInput").click()
        self.find_element_by_id("tab01_QAZF002300_txtBarcode_textboxInput").send_keys(self.test_data.get("case_qap001_vercode"))
        self.save_screenshot_migrate(driver, "汎用現況消込対象検索画面_94" , True)
        
        # 149 汎用現況消込対象検索画面: 「読込」ボタン押下
        self.find_element_by_id("tab01_QAZF002300_btnYomikomi_button").click()
        self.save_screenshot_migrate(driver, "汎用現況消込対象検索画面_95" , True)
        
        # 150 汎用現況消込対象検索画面: № 「1」を選択
        self.find_element_by_id("tab01_ZZZ000000_btnNo2_1_1_button").click()
        
        self.save_screenshot_migrate(driver, "汎用現況消込対象検索画面_97" , True)

        # 151 汎用現況消込対象検索画面: 「登録」ボタン押下
        self.find_element_by_id("tab01_QAZF003800_regbtn_button").click()
        
        # 152 汎用現況消込対象検索画面: 「はい」ボタン押下
        self.find_element_by_id("tempId__1").click()

        # 153 汎用現況消込対象検索画面: 登録確認
        # Assert: 登録しました。を確認
        self.save_screenshot_migrate(driver, "汎用現況消込対象検索画面_99" , True)
        
        # 154 メインメニュー画面: 表示
        #self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        #self.save_screenshot_migrate(driver, "メインメニュー画面_154" , True)
        
        # 155 メインメニュー画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)
        # 156 メインメニュー画面: 「バッチ管理」ボタン押下
        
        # 157 メインメニュー画面: 「即時実行」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        #time.sleep(1)

        # 158 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        #searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        #self.actionChains.double_click(searchBtn).perform()
        #time.sleep(1)

        # 159 バッチ管理画面: 業務名：子ども子育て支援 サブシステム名：入所 処理名：
        #self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        #self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        #self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        #self.find_element_by_xpath("//option[. = '入所']").click()
        #self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        #self.find_element_by_xpath("//option[. = '']").click()
        #self.save_screenshot_migrate(driver, "バッチ管理画面_159" , True)
        
        # 160 メインメニュー画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー画面_100" , True)
        
        # 161 メインメニュー画面: 「バッチ管理」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        time.sleep(1)
        # 162 メインメニュー画面: 「バッチ管理」ボタン押下
        
        # 163 メインメニュー画面: 「即時実行」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 164 メインメニュー画面: 「スケジュール個別追加」ボタンをダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 165 バッチ管理画面: 業務名：子ども子育て支援 サブシステム名：入所 処理名：現況届未提出者督促状_一覧出力処理
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '入所']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '現況届未提出者督促状_一覧出力処理']").click()
        self.save_screenshot_migrate(driver, "バッチ管理画面_104" , True)
        
        # 166 バッチ管理画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 167 バッチ起動画面: 現況届未提出者督促状_一覧出力処理 「(QP2BN06700) 現況届未提出者一覧出力」のNoボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()

        # 168 バッチ起動画面: 所管区：  を選択 出力区分：  を選択 対象年度： を入力 提出期限：  を入力 発行年月日：  を入力 並び順：  を選択 郵便区内特別有無：  を選択
        # Assert: パラメータ化
        # 所管区
        self.find_element_by_id("tab01_ZEAF002200_Paratest02_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest02_select").send_keys(self.test_data.get("case_qap001_shokanku"))
        # 出力区分
        self.find_element_by_id("tab01_ZEAF002200_QApara0087_select").click()
        self.find_element_by_id("tab01_ZEAF002200_QApara0087_select").send_keys(self.test_data.get("case_qap001_shutsuryoku_kubun2"))
        # 対象年度
        self.find_element_by_id("tab01_ZEAF002200_Paratest74_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest74_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest74_textboxInput").send_keys(self.test_data.get("case_qap001_taishou_nendo"))
        # 提出期限
        self.find_element_by_id("tab01_ZEAF002200_QApara0088_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_QApara0088_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_QApara0088_textboxInput").send_keys(formatted_date)
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_QApara0003_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_QApara0003_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_QApara0003_textboxInput").send_keys(formatted_date)
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabi_jun3"))
        # 郵便区内特別有無
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").send_keys(self.test_data.get("case_qap001_yubin_kunai_tokubetsu_umu"))
        self.save_screenshot_migrate(driver, "バッチ起動画面_107" , True)
        
        # 169 バッチ起動画面: 「実行」ボタンを押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        # 170 実行結果画面: 表示
        # Assert: 「(QP2BN06700)現況届未提出者督促状_一覧出力処理」の状態が正常終了となることを確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果画面_109" , True)
        
        # 171 実行結果画面: 「No1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 172 結果確認画面: 表示
        self.save_screenshot_migrate(driver, "結果確認画面_111" , True)
        
        # 173 結果確認画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()

        # 174 ファイルダウンロード: 「No1」ボタン押下
        # 175 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 176 ログ: 表示
        self.save_screenshot_migrate(driver, "ログ_115" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 177 結果確認画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：教育・保育給付認定取消通知書
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_3 = self.test_data.get("ShoriNM_3")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_3)

        # 178 結果確認画面: 表示
        # 179 納品物管理画面: 現況届未提出者一覧.pdf 「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()
        #self.save_screenshot_migrate(driver, "納品物管理画面_179" , True)
        
        # 180 ファイルダウンロード: 「No1」ボタン押下
        # 181 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 182 帳票（PDF）: 表示
        # 183 帳票（PDF）: ×ボタン押下でPDFを閉じる
        self.pdf_download("QAPP109100_現況届未提出者一覧(支給認定分).pdf", "帳票（PDF）_118")
        self.click_button_by_label("閉じる")
        time.sleep(2)       
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 184 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.find_element_by_xpath("//*[@id='tab01_ZEAF002400_navi']/li[1]/a").click()

        # 185 バッチ起動画面: 現況届未提出者督促状_一覧出力処理 「(QP2BN06800) 督促状（現況届提出について）出力」のNoボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_2_4_button").click()

        # 186 バッチ起動画面: 所管区： を選択 発行年月日： を入力 出力区分：施設等利用給付認定 を選択
        # Assert: パラメータ化
        # 所管区
        self.find_element_by_id("tab01_ZEAF002200_Paratest02_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest02_select").send_keys(self.test_data.get("case_qap001_shokanku1"))
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").send_keys(formatted_date)
        # 出力区分
        self.find_element_by_id("tab01_ZEAF002200_QApara0087_select").click()
        self.find_element_by_id("tab01_ZEAF002200_QApara0087_select").send_keys(self.test_data.get("case_qap001_shutsuryoku_kubun3"))
        self.save_screenshot_migrate(driver, "バッチ起動画面_125" , True)
        
        # 187 実行結果画面: 表示
        # Assert: 「(QP2BN06800) 督促状（現況届提出について）出力」の状態が正常終了となることを確認
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果画面_126" , True)
        
        # 188 実行結果画面: 「No1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 189 結果確認画面: 表示
        self.save_screenshot_migrate(driver, "結果確認画面_128" , True)
        
        # 190 結果確認画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()
        
        # 191 ファイルダウンロード: 「No1」ボタン押下
        # 192 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 193 ログ: 表示
        self.save_screenshot_migrate(driver, "ログ_132" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 194 結果確認画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：教育・保育給付認定取消通知書
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_3 = self.test_data.get("ShoriNM_3")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_3)

        # 195 結果確認画面: 表示
        # 196 納品物管理画面: 書類提出のお願い(***).pdf 「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()
        #self.save_screenshot_migrate(driver, "納品物管理画面_196" , True)
        
        # 197 ファイルダウンロード: 「No1」ボタン押下
        # 198 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 199 帳票（PDF）: 表示
        # 200 帳票（PDF）: ×ボタン押下でPDFを閉じる
        self.pdf_download("QAPP105700_書類提出のお願い(支給認定分).pdf", "帳票（PDF）_135")
        self.click_button_by_label("閉じる")
        time.sleep(2)       
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
if __name__ == "__main__":
    unittest.main() 
