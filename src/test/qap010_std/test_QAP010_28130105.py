import time
from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010_28130105(KodomoSiteTestCaseBase):
    """TestQAP010_28130105"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()
    
    # 延長保育実績に関わる各種帳票が作成できることを確認する。
    def test_QAP010_28130105(self):
        """各種帳票作成"""
        
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「バッチ管理」ボタン押下
        # 4 メインメニュー画面: 「即時実行」ボタン押下
        # 5 メインメニュー画面: 「スケジュール個別追加」ボタンをダブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # 6 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_6")
        
        # 7 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：入所処理名：延長保育料決定通知書_一覧出力処理
        # 8 スケジュール個別追加画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_1"),  case_data.get("サブシステム名_1"), case_data.get("処理名_1"))

        # 9 スケジュール個別追加画面: 「No.1」ボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()
        
        # 10 実行指示画面: サービス区分：延長対象年度：対象年月開始：対象年月終了：発行年月日：
        params = [
            {"title":"サービス区分", "type": "select", "value": case_data.get("一覧出力_サービス区分")},
            {"title":"対象年度", "type": "text", "value": case_data.get("一覧出力_対象年度")},
            {"title":"対象年月開始", "type": "text", "value": case_data.get("一覧出力_対象年月開始")},
            {"title":"対象年月終了", "type": "text", "value": case_data.get("一覧出力_対象年月終了")},
            {"title":"発行年月日", "type": "text", "value": case_data.get("一覧出力_発行年月日")},
            {"title":"施設コード", "type": "text", "value": case_data.get("施設コード")},
            {"title":"児童宛名コード", "type": "text", "value": case_data.get("児童宛名コード")},
            {"title":"再発行区分", "type": "select", "value": case_data.get("再発行区分")}
        ]
        self.set_job_param_kodomo(params)
        # 11 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()
        
        # 12 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_12")
        
        # 13 実行結果管理画面: 「No.1」ボタン押下
        # self.click_button_by_label("1")
        # self.wait_page_loaded()
        
        # 14 結果確認画面: 表示
        
        # 15 結果確認画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        
        # 16 納品物管理画面: 表示
        self.screen_shot("納品物管理画面_16")
        
        # 17 納品物管理画面: 「ダウンロード」ボタンを押下
        # 18 ファイルダウンロード画面: 「No.1」ボタン押下
        # 19 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 20 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_20")
        # 21 帳票（PDF）: ×ボタン押下でPDFを閉じる
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_1"),  case_data.get("サブシステム名_1"), case_data.get("処理名_1"))
        self.pdf_download("QAPP111600_延長保育料決定対象者一覧.pdf","")
        self.click_button_by_label("閉じる")
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        # 22 メインメニュー画面: 「バッチ管理」ボタン押下
        # 23 メインメニュー画面: 「即時実行」ボタン押下
        # 24 メインメニュー画面: 「スケジュール個別追加」ボタンをダブルクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()

        
        # 25 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_25")
        
        # 26 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：入所処理名：延長保育料決定通知書_一覧出力処理
        # 27 スケジュール個別追加画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_1"),  case_data.get("サブシステム名_1"), case_data.get("処理名_1"))
       
        # 28 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        time.sleep(2) 
        # 29 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()
        
        # 30 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_30")
        
        # 31 実行結果管理画面: 「No.1」ボタン押下
        # self.click_button_by_label("1")
        # self.wait_page_loaded()
        # 32 結果確認画面: 表示
        # 33 結果確認画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        # 34 納品物管理画面: 表示
        self.screen_shot("納品物管理画面_34")
        # 35 納品物管理画面: 「ダウンロード」ボタンを押下
        # 36 ファイルダウンロード画面: 「No.1」ボタン押下
        # 37 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 38 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_38")
        # 39 帳票（PDF）: ×ボタン押下でPDFを閉じる

         # メインメニュー画面: 「バッチ管理」ボタン押下
         # メインメニュー画面: 「結果管理」ボタン押下
         # メインメニュー画面: 「納品物確認」ボタン押下

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：入所処理名：保育所徴収金減免決定(却下)通知書出力処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_1"),  case_data.get("サブシステム名_1"), case_data.get("処理名_1"))
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP111700_延長保育料決定通知書.pdf","")
        self.click_button_by_label("閉じる")
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        self.find_element_by_xpath("//ul[@id='work_tab']/li[1]/span[2]").click()

        # 41 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_41")
        
        # 42 メインメニュー画面: 「バッチ管理」ボタン押下
        # 43 メインメニュー画面: 「即時実行」ボタン押下
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        # 44 メインメニュー画面: 「スケジュール個別追加」ボタンをダブルクリック
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_2"),  case_data.get("サブシステム名_2"), case_data.get("処理名_2"))
        # 45 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_45")
        
        # 46 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：入所処理名：延長保育料決定（変更）通知
        # 47 スケジュール個別追加画面: 「検索」ボタン押下
        
        # 48 スケジュール個別追加画面: 「No.1」ボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()
        
        # 49 実行指示画面: ※パラメータ不明
        params = [
            {"title":"前回実施日", "type": "text", "value": case_data.get("延長保育料決定（変更）_前回実施日")},
            {"title":"再発行区分", "type": "select", "value": case_data.get("再発行区分")}
        ]
        self.set_job_param_kodomo(params)

        # 50 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()
        
        # 51 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_51")
        
        # 52 メインメニュー画面: 「バッチ管理」ボタン押下
        # 53 メインメニュー画面: 「即時実行」ボタン押下
        # 54 メインメニュー画面: 「スケジュール個別追加」ボタンをダブルクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        
        # 55 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_55")
        
        # 56 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：入所処理名：延長保育料決定（変更）通知
        # 57 スケジュール個別追加画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_2"),  case_data.get("サブシステム名_2"), case_data.get("処理名_2"))
        
        # 58 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()
        
        # 59 実行指示画面: ※パラメータ不明
        # 文書番号：000123
        params = [
            {"title":"文書番号", "type": "text", "value": case_data.get("延長保育料決定（変更）_文書番号")},
        ]
        self.set_job_param_kodomo(params)
        
        # 60 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()
        
        # 61 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_61")
        
        # 62 実行結果管理画面: 「No.1」ボタン押下
        # self.click_button_by_label("1")
        # self.wait_page_loaded()
        # 63 結果確認画面: 表示
        
        # 64 結果確認画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        
        # 65 納品物管理画面: 表示
        self.screen_shot("納品物管理画面_65")
        
        # 66 納品物管理画面: 「ダウンロード」ボタンを押下
        # 67 ファイルダウンロード画面: 「No.1」ボタン押下
        # 68 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 69 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_69")
        # 70 帳票（PDF）: ×ボタン押下でPDFを閉じる
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_2"),  case_data.get("サブシステム名_2"), case_data.get("処理名_2"))
        self.pdf_download("QAPP317000_延長保育料決定（変更）通知書.pdf","")
        self.click_button_by_label("閉じる")
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 71 対象画面名: 操作内容
        # Assert: アサート内容
        # self.screen_shot("対象画面名_71")
        
        # 72 メインメニュー画面: 表示
        # self.screen_shot("メインメニュー画面_72")
        
        # 73 メインメニュー画面: 「子ども子育て支援」ボタン押下

        # 74 メインメニュー画面: 「入所管理」ボタン押下

        # 75 メインメニュー画面: 「児童検索」ボタンダブルクリック
        
        # 76 児童検索画面: 表示
        # self.screen_shot("児童検索画面_76")
        
        # 77 児童検索画面: 住民コード：
         
        # 78 児童検索画面: 「検索」ボタン押下

        
        # 79 児童台帳画面: 表示
        # self.screen_shot("児童台帳画面_79")
        
        # 80 児童台帳画面: 「入所管理」タブをクリック
        
        # 81 児童台帳画面: 「入所管理」ボタン押下
        
        # 82 入所管理（申込）画面: 表示
        # self.screen_shot("入所管理（申込）画面_82")
        
        # 83 入所管理（申込）画面: 「印刷」ボタン押下
        
        # 84 印刷指示画面: 表示
        # self.screen_shot("印刷指示画面_84")
        
        # 85 印刷指示画面: 「延長保育料決定通知書」をチェック
        
        # 86 印刷指示画面: 「延長保育料決定通知書」タブをクリック
        
        # 87 印刷指示画面: ※パラメータ不明
        
        # 88 印刷指示画面: 「印刷」ボタン押下
        
        # 89 印刷一覧画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # self.screen_shot("印刷一覧画面_89")
        
        # 90 印刷一覧画面: 「No.1」ボタン押下
        
        # 91 印刷一覧画面: 「ファイルを開く(O)」ボタンを押下
        
        # 92 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_92")
        
        # 93 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
        # 94 対象画面名: 操作内容
        # Assert: アサート内容
        # self.screen_shot("対象画面名_94")
        
        # 95 メインメニュー画面: 表示
        # self.screen_shot("メインメニュー画面_95")
        
        # 96 メインメニュー画面: 「子ども子育て支援」ボタン押下
        # 97 メインメニュー画面: 「入所管理」ボタン押下
        # 98 メインメニュー画面: 「児童検索」ボタンダブルクリック
        
        # 99 児童検索画面: 表示
        # self.screen_shot("児童検索画面_99")
        
        # 100 児童検索画面: 住民コード：
         
        # 101 児童検索画面: 「検索」ボタン押下

        
        # 102 児童台帳画面: 表示
        # self.screen_shot("児童台帳画面_102")
        
        # 103 児童台帳画面: 「賦課」ボタン押下
        
        # 104 児童賦課情報画面: 表示
        # self.screen_shot("児童賦課情報画面_104")
        
        # 105 児童賦課情報画面: 「印刷」ボタン押下

        
        # 106 印刷指示画面: 表示
        # self.screen_shot("印刷指示画面_106")
        
        # 107 印刷指示画面: 「延長保育料決定（変更）通知」をチェック
        
        # 108 印刷指示画面: 「延長保育料決定（変更）通知」タブをクリック
        
        # 109 印刷指示画面: ※パラメータ不明
        
        # 110 印刷指示画面: 「印刷」ボタン押下
        
        # 111 印刷一覧画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # self.screen_shot("印刷一覧画面_111")
        
        # 112 印刷一覧画面: 「No.1」ボタン押下
        
        # 113 印刷一覧画面: 「ファイルを開く(O)」ボタンを押下
        
        # 114 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_114")
        
        # 115 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
