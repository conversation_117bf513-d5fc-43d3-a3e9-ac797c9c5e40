
from base.kodomo_case import KodomoSiteTestCaseBase
from selenium.webdriver.support.ui import Select

class TestQAP010_28110105(KodomoSiteTestCaseBase):
    """TestQAP010_28110105"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params_list = case_data.get("sql_params")
        self.exec_sqlfile("test_QAP010_28110105_実施前スクリプト.sql", params=sql_params_list)
        super().setUp()
    
    # 支払実績が登録できることを確認する。
    def test_QAP010_28110105(self):
        """支払実績登録"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「認可外申請検索」ボタンダブルクリック
        self.goto_menu(["認可外申請管理","認可外申請検索"])
        
        # 4 検索条件入力画面: 住民コード：<パラメータ>入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAZF100001_txtJuminCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAZF100001_txtJuminCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAZF100001_txtJuminCD_textboxInput").send_keys(case_data.get("AtenaCD"))
        
        self.screen_shot("検索条件入力画面_5")

        # 5 検索条件入力画面: 「検索（Enter）」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        
        # 6 世帯履歴画面: 表示
        self.screen_shot("世帯履歴画面_6")
        
        # 7 世帯履歴画面: 「No1」ボタン押下
        self.click_button_by_label("1")
        
        # 8 世帯台帳画面: 表示
        self.screen_shot("世帯台帳画面_8")
        
        # 9 世帯履歴画面: 「No1」ボタン押下
        self.click_button_by_label("1")
        
        # 10 利用者申請管理画面: 表示
        self.screen_shot("利用者申請管理画面_10")
        
        # 11 利用者申請管理画面: 「支払管理」ボタン押下
        self.click_button_by_label("支払管理")
        
        # 12 補助金申請管理画面: 表示
        self.screen_shot("補助金申請管理画面_12")
        
        # 13 補助金申請管理画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 14 補助金申請管理画面: 利用年月：「カレンダー」ボタンクリック
        self.find_element_by_id(u"tab01_ZZZ000000_txtRiyoYM_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtRiyoYM_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtRiyoYM_textboxInput").send_keys(case_data.get("利用年月"))
        self.find_element_by_id(u"tab01_ZZZ000000_txtHrakiSyoNissu_textboxInput").click()

        # 15 補助金申請管理画面: 請求年月日：初期表示のまま（当日）
        
        # 16 補助金申請管理画面: 「虫めがね」ボタン押下
        self.find_element_by_xpath("//div[@id='tab01_ZZZ000000_btnShisetuKensaku']/button").click()
        self.wait_page_loaded()

        # 17 補助金申請管理画面: 利用施設：１つ目の施設を選択
        self.find_element_by_id(u"tab01_ZZZ000000_selRiyouServiceInf_select").click()
        Select(self.find_element_by_id(u"tab01_ZZZ000000_selRiyouServiceInf_select")).select_by_visible_text(case_data.get("利用施設"))
        
        # 18 補助金申請管理画面: 納付額：該当納付額
        self.find_element_by_id(u"tab01_ZZZ000000_txtNoufuGaku_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtNoufuGaku_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtNoufuGaku_textboxInput").send_keys(case_data.get("納付額"))
        
        # 19 補助金申請管理画面: 利用日数：
        self.find_element_by_id(u"tab01_ZZZ000000_txtRiyoNissu_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtRiyoNissu_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtRiyoNissu_textboxInput").send_keys(case_data.get("利用日数"))
        
        # 20 補助金申請管理画面: 開所日数：空白のまま
        
        # 21 補助金申請管理画面: 支払方法：規定値のまま（償還）
        self.find_element_by_id(u"tab01_ZZZ000000_selSiharaiHouhou_select").click()
        Select(self.find_element_by_id(u"tab01_ZZZ000000_selSiharaiHouhou_select")).select_by_visible_text(case_data.get("支払方法"))
        
        # 22 補助金申請管理画面: 入園料年額：空白のまま
        
        # 23 補助金申請管理画面: 年度内在園月数：空白のまま
        
        # 24 補助金申請管理画面: 決定結果：初期値のまま（申請）
        
        # 25 補助金申請管理画面: 却下理由：空白のまま
        
        # 26 補助金申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        
        # 27 補助金申請管理画面: 「登録してよろしいですか？」：「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        
        # 28 補助金申請管理画面: 「認可外申請検索」タブの×押下
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()
        
        # 30 メインメニュー画面: バッチ管理クリック
        # 31 メインメニュー画面: 即時実行クリック
        # 32 メインメニュー画面: 「スケジュール個別追加」ボタンダブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])

        # 33 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_33")
        
        # 34 スケジュール個別追加画面: 業務名：子ども子育て支援 サブシステム名：認可外支払管理 処理名：補助金支払締め・解除処理 処理区分：
        # 35 スケジュール個別追加: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM = case_data.get("ShoriNM")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM)

        self.screen_shot("スケジュール個別追加_35")
        
        # 36 スケジュール個別追加画面: 「(QPNBN01900) 補助金支払締め・解除」のNoボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()
        
        # 37 実行指示画面: 表示
        self.screen_shot("実行指示画面_37")
        
        # 38 実行指示画面: 処理状況：空白のまま
        # 39 実行指示画面: 処理区分：初期値のまま（締め処理）
        
        # 40 実行指示画面: 「実行」ボタン押下
        self.screen_shot("実行指示画面_40")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        self.click_button_by_label("実行")
        
        # 41 実行結果管理画面: 「検索」ボタン押下
        #self.click_button_by_label("検索")
        
        # 42 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_42")
        
        # 43 メインメニュー画面: ×ボタン押下
        
