from base.kodomo_case import KodomoSiteTestCaseBase
from selenium.webdriver.support.ui import Select

class TestQAP010_28130106(KodomoSiteTestCaseBase):
    """TestQAP010_28130106"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params_list = case_data.get("sql_params")
        self.exec_sqlfile("test_QAP010_28130106_実施前スクリプト.sql", params=sql_params_list)
        super().setUp()
    
    # 延長保育の解除登録ができることを確認する。
    def test_QAP010_28130106(self):
        """解除登録"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「子ども子育て支援」ボタン押下
        # 4 メインメニュー画面: 「入所管理」ボタン押下
        # 5 メインメニュー画面: 「児童検索」ボタン押下
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        self.wait_page_loaded()
        # 6 児童検索画面: 表示
        self.screen_shot("児童検索画面_6")
        
        # 7 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("児童_宛名C"))


        # 8 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")

        # 9 児童台帳画面：支給認定登録・履歴: 表示
        self.screen_shot("児童台帳画面：支給認定登録・履歴_9")
        
        # 10 児童台帳画面：支給認定登録・履歴: 「入所管理」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()

        # 11 児童台帳画面：入所管理タブ: 表示
        self.screen_shot("児童台帳画面：入所管理タブ_11")
        
        # 12 児童台帳画面：入所管理タブ: 「特別保育」ボタン押下
        self.click_button_by_label("特別保育")

        # 13 特別保育実績管理画面: 表示
        self.screen_shot("特別保育実績管理画面_13")
        
        # 14 特別保育実績管理画面: 「特別保育申請」ボタン押下
        self.click_button_by_label("特別保育申請")

        # 15 特別保育申請管理画面: 表示
        self.screen_shot("特別保育申請管理画面_15")
        
        # 16 特別保育申請管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 17 特別保育申請管理画面: 表示
        self.screen_shot("特別保育申請管理画面_17")
        
        # 18 特別保育申請管理画面: 申請種別：変更認定年月(終了)：
        self.find_element_by_id(u"tab01_QAPF101900_txtNinteiYMEnd_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF101900_txtNinteiYMEnd_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF101900_txtNinteiYMEnd_textboxInput").send_keys(case_data.get("認定年月(終了)"))

        Select(self.find_element_by_id(u"tab01_QAPF101900_selShinseiShubetsu_select")).select_by_visible_text(case_data.get("申請種別"))
        
        self.click_button_by_label("入力チェック")

        # 19 特別保育申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")


        # 20 特別保育申請管理画面: 「はい」ボタン押下
        self.wait_page_loaded()
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()

        # 21 特別保育申請管理画面: 登録確認
        # Assert: 更新しました。を確認する。
        self.assert_message_area("tab01_QAPF101900_msg_span","更新しました。")
        self.screen_shot("特別保育申請管理画面_21")
        
        # 22 特別保育申請管理画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # Assert: 各項目が入力可能になることを確認する。
        self.screen_shot("特別保育申請管理画面_22")
        
        # 23 特別保育申請管理画面: 年度：　　　　　　　　　　　　　　　　　　を選択サービス区分：　　　　　　　　 　　　　を選択申請年月日：　　　　　　　　　 　　　　を入力申請種別：実施解除決定年月日：　　　　　　　　　 　　　　を入力結果：承諾認定年月(開始)：　　　　　　　　　　　を入力認定年月(終了)：　　　　　　　　　　　を入力サービス種類：　　　　　　　　 　　　　を選択希望実施期間(開始)：　　　　　　　　を入力希望実施期間(終了)：　　　　　　　　を入力補足情報１：　　　　　　　　　　　　　　を入力補足情報２：　　　　　　　　　　　　　　を入力補足情報３：　　　　　　　　　　　　　　を入力補足情報４：　　　　　　　　　　　　　　を入力
        self.find_element_by_id(u"tab01_QAPF101900_selTokubetsuHoikuShinseiNendo_select").send_keys(case_data.get("年度"))

        self.find_element_by_id(u"tab01_QAPF101900_selServiceKbn_select").send_keys(case_data.get("サービス区分"))


        self.find_element_by_id(u"tab01_QAPF101900_txtShinseiYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF101900_txtShinseiYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF101900_txtShinseiYMD_textboxInput").send_keys(case_data.get("申請年月日"))

        self.find_element_by_id(u"tab01_QAPF101900_selShinseiShubetsu_select").send_keys(case_data.get("追加_申請種別"))

        self.find_element_by_id(u"tab01_QAPF101900_txtKetteiYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF101900_txtKetteiYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF101900_txtKetteiYMD_textboxInput").send_keys(case_data.get("決定年月日"))

        self.find_element_by_id(u"tab01_QAPF101900_selKekka_select").send_keys(case_data.get("結果"))

        self.find_element_by_id(u"tab01_QAPF101900_txtNinteiYMStart_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF101900_txtNinteiYMStart_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF101900_txtNinteiYMStart_textboxInput").send_keys(case_data.get("認定年月(開始)"))

        self.find_element_by_id(u"tab01_QAPF101900_txtNinteiYMEnd_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF101900_txtNinteiYMEnd_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF101900_txtNinteiYMEnd_textboxInput").send_keys(case_data.get("認定年月(終了)"))

        self.find_element_by_id(u"tab01_QAPF101900_selShurui_select").send_keys(case_data.get("サービス種類"))

        self.find_element_by_id(u"tab01_QAPF101900_txtKiboJisshiKikanStart_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF101900_txtKiboJisshiKikanStart_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF101900_txtKiboJisshiKikanStart_textboxInput").send_keys(case_data.get("希望実施期間(開始)"))

        self.find_element_by_id(u"tab01_QAPF101900_txtKiboJisshiKikanEnd_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF101900_txtKiboJisshiKikanEnd_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF101900_txtKiboJisshiKikanEnd_textboxInput").send_keys(case_data.get("希望実施期間(終了)"))

        self.find_element_by_id(u"tab01_QAPF101900_txtHosokuJoho1_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF101900_txtHosokuJoho1_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF101900_txtHosokuJoho1_textboxInput").send_keys(case_data.get("補足情報１"))

        self.find_element_by_id(u"tab01_QAPF101900_txtHosokuJoho2_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF101900_txtHosokuJoho2_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF101900_txtHosokuJoho2_textboxInput").send_keys(case_data.get("補足情報２"))


        self.find_element_by_id(u"tab01_QAPF101900_txtHosokuJoho3_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF101900_txtHosokuJoho3_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF101900_txtHosokuJoho3_textboxInput").send_keys(case_data.get("補足情報３"))


        self.find_element_by_id(u"tab01_QAPF101900_txtHosokuJoho4_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF101900_txtHosokuJoho4_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF101900_txtHosokuJoho4_textboxInput").send_keys(case_data.get("補足情報４"))
        self.screen_shot("特別保育申請管理画面_23")
        
        # 24 特別保育申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")

        # 25 特別保育申請管理画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()

        # 26 特別保育申請管理画面: 登録確認
        # Assert: 登録しました。を確認する。
        self.assert_message_area("tab01_QAPF101900_msg_span","登録しました。")
        self.screen_shot("特別保育申請管理画面_26")
        
        # 27 特別保育申請管理画面: 「児童台帳」ボタン押下
        self.find_element_by_xpath("//div[@id='tab01_QAPF101900_navi']/li[2]/a").click()
        
        # 28 児童台帳: 表示
        self.screen_shot("児童台帳_28")
        
        # 29 児童台帳: 「賦課」ボタン押下
        self.click_button_by_label("賦課")

        # 30 児童賦課情報: 表示
        self.screen_shot("児童賦課情報_30")
        
        # 31 児童賦課情報: 賦課年度：賦課年月：
        self.find_element_by_id(u"tab01_QAPF107000_selFukaYear_select").send_keys(case_data.get("賦課年度"))
        self.find_element_by_id(u"tab01_QAPF107000_selFukaNendo_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF107000_selFukaNendo_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF107000_selFukaNendo_textboxInput").send_keys(case_data.get("賦課年月"))

        # 32 児童賦課情報: 「賦課計算」ボタン押下
        self.click_button_by_label("賦課計算")

        # 33 児童賦課情報: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()

        # 34 児童賦課情報: 結果確認
        # Assert: 個別賦課計算処理が正常に完了しました。を確認する。
        self.screen_shot("児童賦課情報_34")
        self.assert_message_area("tab01_QAPF107000_msg_span","個別賦課計算処理が正常に完了しました。")
        
        # 35 児童賦課情報: 「特別保育利用料」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF107000_hituyoriyu_li']/a").click()
        
        # 36 児童賦課情報：特別保育利用料: 表示
        # Assert: 実施解除した期間の賦課情報が削除されていることを確認する。
        self.screen_shot("児童賦課情報：特別保育利用料_36")
        
