import time
import unittest
from datetime import datetime, date, timedelta
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.select import Select
class TestQAP010_28010102(KodomoSiteTestCaseBase):
    """TestQAP010_28010102"""

    def setUp(self):
        settings = self.common_test_data
        self.test_data = settings.get("test_qap010_28010102")
        super().setUp()
    
    # 教育・保育給付認定申請にかかわる各種帳票の作成ができることを確認する。
    def test_QAP010_28010102(self):
        """各種帳票作成"""
        
        driver = None

        self.do_login()
        # 1 メインメニュー　画面: 表示
        self.save_screenshot_migrate(driver, "メインメニュー 画面_1" , True)
        
        # 2 メインメニュー　画面: 「バッチ管理」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        time.sleep(1)

        # 3 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 4 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)
        
        # 5 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定証_通知書_一覧出力処理']").click()

        # 6 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 7 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_7" , True)
        
        # 9 スケジュール個別追加　画面: 「№1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()

        # 9 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_9" , True)
        
        # 10 実行指示　画面: 発行年月日、支給決定開始日、支給決定終了日、支給認定証番号　を入力
        # Assert: パラメータ化
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").send_keys(formatted_date)
        # 支給決定開始日
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").send_keys(self.test_data.get("case_qap001_shikyuu_kettei_kaishibi"))
        # 支給決定終了日
        self.find_element_by_id("tab01_ZEAF002200_Paratest08_textboxInput").click()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest08_textboxInput").send_keys(formatted_date)
        # 支給認定証番号
        self.find_element_by_id("tab01_ZEAF002200_Paratest11_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest11_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest11_textboxInput").send_keys(self.test_data.get("case_qap001_shikyuu_ninteishou_bangou"))

        # 11 実行指示　画面: 申請事由、認定区分、並び順、再発行区分　を選択
        # Assert: パラメータ化
        # 申請事由
        self.find_element_by_id("tab01_ZEAF002200_Paratest10_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest10_select").send_keys(self.test_data.get("case_qap001_shinsei_jiyuu"))
        # 認定区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest09_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest09_select").send_keys(self.test_data.get("case_qap001_nintei_kubun"))
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabijun"))
        # 再発行区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").send_keys(self.test_data.get("case_qap001_saihakkou_kubun"))
        # 郵便区内特別有無
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").send_keys(self.test_data.get("case_qap001_yubin_kunai_tokubetsu_umu"))

        # 12 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_12" , True)
        
        # 13 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()

        # 14 実行結果管理　画面: 表示
        # Assert: 「（QP1BN00100）支給認定証データ抽出」の状態が正常終了することを確認
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_14" , True)
        
        # 15 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()

        # 16 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_16" , True)
        
        # 17 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()
        
        # 18 ファイルダウンロード: 「1」ボタン押下
        # 19 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 20 ログ: 表示
        # 21 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_20" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 22 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # 23 納品物管理　画面: QAPP106300_支給認定証交付一覧表.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # 納品物管理画面: 表示
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：選考処理名：支給認定証_通知書_一覧出力処理
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_1 = self.test_data.get("ShoriNM_1")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_1)

        # 24 ファイルダウンロード: 「1」ボタン押下
        # 25 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 26 帳票（PDF）: 表示
        # 27 帳票（PDF）: 閉じる
        self.pdf_download("QAPP106300_支給認定証交付一覧表.pdf", "帳票（PDF）_26(1)")
        self.click_button_by_label("閉じる")

        # 23 納品物管理　画面: QAPP106310_支給認定証交付一覧表_要支援措置対象.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_2_9_button").click()

        # 24 ファイルダウンロード: 「1」ボタン押下
        # 25 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 26 帳票（PDF）: 表示
        # 27 帳票（PDF）: 閉じる
        self.pdf_download("QAPP106310_支給認定証交付一覧表_要支援措置対象.pdf", "帳票（PDF）_26(2)")
        self.click_button_by_label("閉じる")

        # 23 納品物管理　画面: 支給認定証交付一覧表.CSV　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_3_9_button").click()

        # 24 ファイルダウンロード: 「1」ボタン押下
        # 25 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 26 帳票（CSV）: 表示
        # 27 帳票（CSV）: 閉じる
        self.pdf_download("支給認定証交付一覧表.csv", "帳票（CSV）_26(3)")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 28 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_28" , True)
        
        # 29 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # 30 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 31 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 32 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定証_通知書_一覧出力処理']").click()

        # 33 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 34 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_34" , True)
        
        # 35 スケジュール個別追加　画面: №「2」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_2_4_button").click()

        # 36 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_36" , True)
        
        # 37 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_37" , True)
        
        # 38 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        
        # 39 実行結果管理　画面: 表示
        # Assert: 「（QP1BN00200）支給認定証出力」の状態が正常終了することを確認
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_39" , True)
        
        # 40 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()

        # 41 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_41" , True)
        
        # 42 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()
        
        # 43 ファイルダウンロード: 「1」ボタン押下
        # 44 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 45 ログ: 表示
        # 46 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_45" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)

        # 納品物管理画面: 表示
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：選考処理名：支給認定証_通知書_一覧出力処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_1)
        
        # 47 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()
        
        # 48 納品物管理　画面: QAPP105900_支給認定証.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # 49 ファイルダウンロード: 「1」ボタン押下
        # 50 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 51 帳票（PDF）: 表示
        # 52 帳票（PDF）: 閉じる
        self.pdf_download("QAPP105900_支給認定証.pdf", "帳票（PDF）_51")
        self.click_button_by_label("閉じる")
        
        # 53 納品物管理　画面: QAPP106000_教育・保育給付認定決定通知書.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_2_9_button").click()

        # 54 ファイルダウンロード: 「1」ボタン押下
        # 55 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 56 帳票（PDF）: 表示
        # 57 帳票（PDF）: 閉じる
        self.pdf_download("QAPP106000_教育・保育給付認定決定通知書.pdf", "帳票（PDF）_56(1)")
        self.click_button_by_label("閉じる")

        # 53 納品物管理　画面: QAPP105910_支給認定証_要支援措置対象.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_3_9_button").click()

        # 54 ファイルダウンロード: 「1」ボタン押下
        # 55 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 56 帳票（PDF）: 表示
        # 57 帳票（PDF）: 閉じる
        self.pdf_download("QAPP105910_支給認定証_要支援措置対象.pdf", "帳票（PDF）_56(2)")
        self.click_button_by_label("閉じる")

        # 53 納品物管理　画面: QAPP106010_支給認定証交付通知書_要支援措置対象.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_4_9_button").click()

        # 54 ファイルダウンロード: 「1」ボタン押下
        # 55 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 56 帳票（PDF）: 表示
        # 57 帳票（PDF）: 閉じる
        self.pdf_download("QAPP106010_教育・保育給付認定決定通知書_要支援措置対象.pdf", "帳票（PDF）_56(3)")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 58 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_58" , True)
        
        # 59 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # 60 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 61 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)
        
        # 62 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//*[@id='tab01_ZEAF000400_SelKensakuShoriNM_select']/option[4]").click()
        
        # 63 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()
        
        # 64 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_64" , True)
        
        # 65 スケジュール個別追加　画面: 「№1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()

        # 66 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_66" , True)
        
        # 67 実行指示　画面: 対象期間開始日、対象期間終了日、支給認定証番号、発行年月日　を入力
        # Assert: パラメータ化
        # 対象期間開始日
        self.find_element_by_id("tab01_ZEAF002200_QApara0056_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_QApara0056_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_QApara0056_textboxInput").send_keys(self.test_data.get("case_qap001_shikyuu_kettei_kaishibi1"))
        # 対象期間終了日
        self.find_element_by_id("tab01_ZEAF002200_QApara0057_textboxInput").click()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_QApara0057_textboxInput").send_keys(formatted_date)
        # 支給認定証番号
        self.find_element_by_id("tab01_ZEAF002200_Paratest11_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest11_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest11_textboxInput").send_keys(self.test_data.get("case_qap001_shikyuu_ninteishou_bangou1"))
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").send_keys(formatted_date)

        # 68 実行指示　画面: 再発行区分、申請事由、抽出区分、認定区分、並び順、　を選択
        # Assert: パラメータ化
        # 再発行区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").send_keys(self.test_data.get("case_qap001_saihakkou_kubun1"))
        # 申請事由
        self.find_element_by_id("tab01_ZEAF002200_QAPara0101_select").click()
        self.find_element_by_id("tab01_ZEAF002200_QAPara0101_select").send_keys(self.test_data.get("case_qap001_shinsei_jiyuu1"))
        # 抽出区分
        self.find_element_by_id("tab01_ZEAF002200_QApara0102_select").click()
        self.find_element_by_id("tab01_ZEAF002200_QApara0102_select").send_keys(self.test_data.get("case_qap001_chushutsu_kubun"))
        # 認定区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest09_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest09_select").send_keys(self.test_data.get("case_qap001_nintei_kubun1"))
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabijun1"))
        # 郵便区内特別有無
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").send_keys(self.test_data.get("case_qap001_yubin_kunai_tokubetsu_umu1"))
        
        # 69 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_69" , True)
        
        # 70 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        
        # 71 実行結果管理　画面: 表示
        # Assert: 「（QP1BN00400）支給認定証取下(却下）データ抽出」の状態が正常終了することを確認
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_71" , True)
        
        # 72 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()

        # 73 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_73" , True)
        
        # 74 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()
        
        # 75 ファイルダウンロード: 「1」ボタン押下
        # 76 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 77 ログ: 表示
        # 78 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_77" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 納品物管理画面: 表示
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：選考処理名：支給認定証取下(却下）通知書_一覧出力処理
        # 納品物確認画面: 「検索」ボタン押下
        shoriNM_2 = self.test_data.get("ShoriNM_2")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_2)

        # 79 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # 80 納品物管理　画面: QAPP106350_支給認定証取下一覧表.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # 81 納品物管理　画面: QAPP106350_支給認定証取下一覧表.pdf　「ダウンロード」ボタン押下
        
        # 82 ファイルダウンロード: 「1」ボタン押下
        # 83 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 84 帳票（PDF）: 表示
        # 85 帳票（PDF）: 閉じる
        self.pdf_download("QAPP106350_支給認定証取下一覧表.pdf", "帳票（PDF）_83")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # 86 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_85" , True)
        
        # 87 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # 88 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 89 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 90 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//*[@id='tab01_ZEAF000400_SelKensakuShoriNM_select']/option[4]").click()

        # 91 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 92 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_91" , True)
        
        # 93 スケジュール個別追加　画面: №「2」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_2_4_button").click()
        
        # 94 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_93" , True)
        
        # 95 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_94" , True)
        
        # 96 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        
        # 97 実行結果管理　画面: 表示
        # Assert: 「（QP1BN00500）支給認定証取下(却下）通知書出力」の状態が正常終了することを確認
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_96" , True)
        
        # 98 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()

        # 99 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_98" , True)
        
        # 100 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()

        # 101 ファイルダウンロード: 「1」ボタン押下
        # 102 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 103 ログ: 表示
        # 104 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_102" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)

        # 納品物管理画面: 表示
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：選考処理名：支給認定証取下(却下)通知書_一覧出力処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_2)
        
        # 105 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # 106 納品物管理　画面: QAPP106100支給認定証取下通知書.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # 107 納品物管理　画面: QAPP106100_支給認定証取下通知書.pdf　「ダウンロード」ボタン押下
        
        # 108 ファイルダウンロード: 「1」ボタン押下
        # 109 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 110 帳票（PDF）: 表示
        # 111 帳票（PDF）: 閉じる
        self.pdf_download("QAPP106100_支給認定証取下通知書.pdf", "帳票（PDF）_108")
        self.click_button_by_label("閉じる")
        
        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # 112 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_110" , True)
        
        # 113 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # 114 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 115 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 116 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定証_交付通知書_一覧(代理申請)出力処理']").click()

        # 117 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 118 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_116" , True)
        
        # 119 スケジュール個別追加　画面: 「№1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()

        # 120 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_118" , True)
        
        # 121 実行指示　画面: 発行年月日、支給決定開始日、支給決定終了日、事業所番号、支給認定証番号、クラス年齢算出基準年度　を入力
        # Assert: パラメータ化
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").send_keys(formatted_date)
        # 支給決定開始日
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").send_keys(self.test_data.get("case_qap001_shikyuu_kettei_kaishibi2"))
        # 支給決定終了日
        self.find_element_by_id("tab01_ZEAF002200_Paratest08_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest08_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest08_textboxInput").send_keys(self.test_data.get("case_qap001_shikyuu_kettei_shuryoubi"))
        # 事業所番号
        self.find_element_by_id("tab01_ZEAF002200_Paratest22_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest22_textboxInput").send_keys(self.test_data.get("case_qap001_jigyousho_bangou"))
        # 支給認定証番号
        self.find_element_by_id("tab01_ZEAF002200_Paratest11_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest11_textboxInput").send_keys(self.test_data.get("case_qap001_shikyuu_ninteishou_bangou2"))
        # クラス年齢算出基準年度
        self.find_element_by_id("tab01_ZEAF002200_QApara0186_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_QApara0186_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_QApara0186_textboxInput").send_keys(self.test_data.get("case_qap001_class_nenrei_sanshutsu_kijun_nendo"))

        # 122 実行指示　画面: 申請事由、認定区分、再発行区分、並び順、　を選択
        # Assert: パラメータ化
        # 申請事由
        self.find_element_by_id("tab01_ZEAF002200_Paratest10_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest10_select").send_keys(self.test_data.get("case_qap001_shinsei_jiyuu2"))
        # 認定区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest09_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest09_select").send_keys(self.test_data.get("case_qap001_nintei_kubun2"))
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabijun2"))
        # 再発行区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").send_keys(self.test_data.get("case_qap001_saihakkou_kubun2"))
        # 郵便区内特別有無
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").send_keys(self.test_data.get("case_qap001_yubin_kunai_tokubetsu_umu2"))

        # 123 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_121" , True)
        
        # 124 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()

        # 125 実行結果管理　画面: 表示
        # Assert: 「（QP1BN01000）支給認定証データ（代理申請）抽出」の状態が正常終了することを確認
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_123" , True)
        
        # 126 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()

        # 127 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_125" , True)
        
        # 128 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()

        # 129 ファイルダウンロード: 「1」ボタン押下
        # 130 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 131 ログ: 表示
        # 132 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_129" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 133 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # 134 納品物管理　画面: QAPP110400_支給認定証交付一覧表（代理申請）.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # 納品物管理画面: 表示
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：選考処理名：支給認定証_交付通知書_一覧(代理申請)出力処理
        # 納品物確認画面: 「検索」ボタン押下
        shoriNM_3 = self.test_data.get("ShoriNM_3")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_3)

        # 135 ファイルダウンロード: 「1」ボタン押下
        # 136 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 137 帳票（PDF）: 表示
        # 138 帳票（PDF）: 閉じる
        self.pdf_download("QAPP110400_支給認定証交付一覧表（代理申請）.pdf", "帳票（PDF）_135")
        self.click_button_by_label("閉じる")
        
        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 139 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_138" , True)
        
        # 140 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # 141 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 142 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 143 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定証_交付通知書_一覧(代理申請)出力処理']").click()

        # 144 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 145 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_144" , True)
        
        # 146 スケジュール個別追加　画面: №「2」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_2_4_button").click()

        # 147 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_146" , True)
        
        # 148 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_147" , True)
        
        # 149 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()

        # 150 実行結果管理　画面: 表示
        # Assert: 「（QP1BN01100）支給認定証(代理申請)出力」の状態が正常終了することを確認
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_149" , True)
        
        # 151 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()

        # 152 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_151" , True)
        
        # 153 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()

        # 154 ファイルダウンロード: 「1」ボタン押下
        # 155 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 156 ログ: 表示
        # 157 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_155" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 158 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # 159 納品物管理　画面: QAPP110300_支給認定証交付通知書（代理申請）.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_2_9_button").click()

        # 納品物管理画面: 表示
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：選考処理名：支給認定証_交付通知書_一覧(代理申請)出力処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_3)

        # 160 ファイルダウンロード: 「1」ボタン押下
        # 161 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 162 帳票（PDF）: 表示
        # 163 帳票（PDF）: 閉じる
        self.pdf_download("QAPP110300_支給認定証交付通知書（代理申請）.pdf", "帳票（PDF）_161(1)")
        self.click_button_by_label("閉じる")
        
        # 159 納品物管理　画面: QAPP105900_支給認定証.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # 160 ファイルダウンロード: 「1」ボタン押下
        # 161 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 162 帳票（PDF）: 表示
        # 163 帳票（PDF）: 閉じる
        self.pdf_download("QAPP105900_支給認定証.pdf", "帳票（PDF）_161(2)")
        self.click_button_by_label("閉じる")

        # 159 納品物管理　画面: QAPP110200_送付票.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_3_9_button").click()

        # 160 ファイルダウンロード: 「1」ボタン押下
        # 161 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 162 帳票（PDF）: 表示
        # 163 帳票（PDF）: 閉じる
        self.pdf_download("QAPP110200_送付票.pdf", "帳票（PDF）_161(3)")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 164 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_163" , True)
        
        # 165 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # 166 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 167 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 168 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定未処理一覧出力処理']").click()

        # 169 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 170 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_169" , True)
        
        # 171 スケジュール個別追加　画面: 「№1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()

        # 172 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_171" , True)
        
        # 173 実行指示　画面: 発行年月日、申請日開始、申請日終了　を入力
        # Assert: パラメータ化
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").send_keys(formatted_date)
        # 申請日開始
        self.find_element_by_id("tab01_ZEAF002200_Paratest19_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest19_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest19_textboxInput").send_keys(self.test_data.get("case_qap001_shinseibi_kaishi"))
        # 申請日終了
        self.find_element_by_id("tab01_ZEAF002200_Paratest20_textboxInput").click()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest20_textboxInput").send_keys(formatted_date)
        
        # 174 実行指示　画面: 並び順、　を選択
        # Assert: パラメータ化
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabijun3"))

        # 175 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_174" , True)
        
        # 176 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()

        # 177 実行結果管理　画面: 表示
        # Assert: 「（QP1BN01300）支給認定未処理一覧　出力」の状態が正常終了することを確認
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_176" , True)
        
        # 178 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()

        # 179 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_178" , True)
        
        # 180 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()

        # 181 ファイルダウンロード: 「1」ボタン押下
        # 182 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 183 ログ: 表示
        # 184 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_182" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)

        # 納品物管理画面: 表示
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：選考処理名：支給認定未処理一覧出力処理
        # 納品物確認画面: 「検索」ボタン押下
        shoriNM_4 = self.test_data.get("ShoriNM_4")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_4)
        
        # 185 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # 186 納品物管理　画面: QAPP105800_支給認定未処理一覧.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # 187 ファイルダウンロード: 「1」ボタン押下
        # 188 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 189 帳票（PDF）: 表示
        # 190 帳票（PDF）: 閉じる
        self.pdf_download("QAPP105800_支給認定未処理一覧.pdf", "帳票（PDF）_188")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 191 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_190" , True)
        
        # 192 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # 193 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 194 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 195 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定証_交付延期通知書_一覧出力処理']").click()

        # 196 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 197 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_196" , True)
        
        # 198 スケジュール個別追加　画面: 「№1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()

        # 199 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_198" , True)

        # 200 実行指示　画面: 発行年月日、延期登録日開始、延期登録日終了、申請日開始、申請日終了、児童宛名コード　を入力
        # Assert: パラメータ化
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").send_keys(formatted_date)
        # 延期登録日開始
        self.find_element_by_id("tab01_ZEAF002200_Paratest56_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest56_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest56_textboxInput").send_keys(self.test_data.get("case_qap001_enki_tourokubi_kaishi"))
        # 延期登録日終了
        self.find_element_by_id("tab01_ZEAF002200_Paratest57_textboxInput").click()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest57_textboxInput").send_keys(formatted_date)
        # 申請日開始
        self.find_element_by_id("tab01_ZEAF002200_Paratest101_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest101_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest101_textboxInput").send_keys(self.test_data.get("case_qap001_shinseibi_kaishi1"))
        # 申請日終了
        self.find_element_by_id("tab01_ZEAF002200_Paratest20_textboxInput").click()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest20_textboxInput").send_keys(formatted_date)
        # 児童宛名コード
        self.find_element_by_id("tab01_ZEAF002200_Paratest58_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest58_textboxInput").send_keys(self.test_data.get("case_qap001_jidou_atena_code"))

        # 201 実行指示　画面: 再発行区分　を選択
        # Assert: パラメータ化
        # 再発行区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").send_keys(self.test_data.get("case_qap001_saihakkou_kubun3"))
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabijun4"))

        # 202 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_201" , True)
        
        # 203 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()

        # 204 実行結果管理　画面: 表示
        # Assert: 「（QP1BN01500）支給認定証延期通知書　抽出」の状態が正常終了することを確認
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_203" , True)
        
        # 205 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()

        # 206 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_205" , True)
        
        # 207 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()

        # 208 ファイルダウンロード: 「1」ボタン押下
        # 209 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 210 ログ: 表示
        # 211 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_209" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)

        # 納品物管理画面: 表示
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：選考処理名：支給認定証_交付延期通知書_一覧出力処理
        # 納品物確認画面: 「検索」ボタン押下
        shoriNM_5 = self.test_data.get("ShoriNM_5")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_5)
        
        # 212 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # 213 納品物管理　画面: QAPP110100_支給認定延期通知一覧.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # 214 ファイルダウンロード: 「1」ボタン押下
        # 215 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 216 帳票（PDF）: 表示
        # 217 帳票（PDF）: 閉じる
        self.pdf_download("QAPP110100_支給認定延期通知一覧.pdf", "帳票（PDF）_215")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # 218 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_217" , True)
        
        # 219 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # 220 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 221 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 222 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定証_交付延期通知書_一覧出力処理']").click()

        # 223 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 224 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_223" , True)
        
        # 225 スケジュール個別追加　画面: №「2」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_2_4_button").click()
        
        # 226 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_225" , True)
        
        # 227 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_226" , True)
        
        # 228 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        
        # 229 実行結果管理　画面: 表示
        # Assert: 「（QP1BN01600）支給認定証交付延期通知書出力」の状態が正常終了することを確認
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_228" , True)
        
        # 230 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 231 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_230" , True)
        
        # 232 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()

        # 233 ファイルダウンロード: 「1」ボタン押下
        # 234 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 235 ログ: 表示
        # 236 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_234" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)

        # 納品物管理画面: 表示
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：選考処理名：支給認定証_交付延期通知書_一覧出力処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_5)
        
        # 237 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()
        
        # 238 納品物管理　画面: QAPP109300_支給認定証交付延期通知書.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # 239 ファイルダウンロード: 「1」ボタン押下
        # 240 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 241 帳票（PDF）: 表示
        # 242 帳票（PDF）: 閉じる
        self.pdf_download("QAPP109300_支給認定証交付延期通知書.pdf", "帳票（PDF）_240")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 243 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_242" , True)
        
        # 244 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # 245 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 246 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 247 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '受給証明書出力処理']").click()

        # 248 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 249 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_248" , True)
        
        # 250 スケジュール個別追加　画面: 「№1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()
        
        # 251 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_250" , True)
        
        # 252 実行指示　画面: 支給決定開始日、支給決定終了日、証明書有効期限、支給認定証番号、発行年月日　を入力
        # Assert: パラメータ化
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").send_keys(formatted_date)
        # 証明書有効期間
        self.find_element_by_id("tab01_ZEAF002200_Paratest76_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest76_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest76_textboxInput").send_keys(formatted_date)
        # 支給決定開始日
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").send_keys(self.test_data.get("case_qap001_shikyuu_kettei_kaishibi3"))
        # 支給決定終了日
        self.find_element_by_id("tab01_ZEAF002200_Paratest08_textboxInput").click()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest08_textboxInput").send_keys(formatted_date)
        # 支給認定証番号
        self.find_element_by_id("tab01_ZEAF002200_Paratest11_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest11_textboxInput").send_keys(self.test_data.get("case_qap001_shikyuu_ninteishou_bangou3"))

        # 253 実行指示　画面: 再発行区分、申請事由、認定区分、並び順、郵便区内特別有無　を選択
        # Assert: パラメータ化
        # 再発行区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").send_keys(self.test_data.get("case_qap001_saihakkou_kubun4"))
        # 申請事由
        self.find_element_by_id("tab01_ZEAF002200_Paratest10_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest10_select").send_keys(self.test_data.get("case_qap001_shinsei_jiyuu3"))
        # 認定区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest09_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest09_select").send_keys(self.test_data.get("case_qap001_nintei_kubun3"))
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabijun5"))
        # 郵便区内特別有無
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").send_keys(self.test_data.get("case_qap001_yubin_kunai_tokubetsu_umu3"))

        # 254 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_253" , True)
        
        # 255 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()

        # 256 実行結果管理　画面: 表示
        # Assert: 「（QP1BN02000）受給証明書 抽出」の状態が正常終了することを確認
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_255" , True)
        
        # 257 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()

        # 258 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_257" , True)
        
        # 259 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()

        # 260 ファイルダウンロード: 「1」ボタン押下
        # 261 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 262 ログ: 表示
        # 263 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_261" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)

        # 納品物管理画面: 表示
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：選考処理名：受給証明書出力処理
        # 納品物確認画面: 「検索」ボタン押下
        shoriNM_6 = self.test_data.get("ShoriNM_6")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_6)
        
        # 264 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # 265 納品物管理　画面: QAPP106300_受給証明書一覧.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # 266 ファイルダウンロード: 「1」ボタン押下
        # 267 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 268 帳票（PDF）: 表示
        # 269 帳票（PDF）: 閉じる
        self.pdf_download("QAPP106300_受給証明書一覧.pdf", "帳票（PDF）_267")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 270 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_269" , True)
        
        # 271 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # 272 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 273 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 274 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '受給証明書出力処理']").click()

        # 275 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 276 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_275" , True)
        
        # 277 スケジュール個別追加　画面: №「2」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_2_4_button").click()

        # 278 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_277" , True)
        
        # 279 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_278" , True)
        
        # 280 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()

        # 281 実行結果管理　画面: 表示
        # Assert: 「（QP1BN02100）受給証明書 出力」の状態が正常終了することを確認
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_280" , True)
        
        # 282 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()

        # 283 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_282" , True)
        
        # 284 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()

        # 285 ファイルダウンロード: 「1」ボタン押下
        # 286 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 287 ログ: 表示
        # 288 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_286" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)

        # 納品物管理画面: 表示
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：選考処理名：受給証明書出力処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_6)
        
        # 289 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # 290 納品物管理　画面: QAPP106600_受給証明書.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # 291 ファイルダウンロード: 「1」ボタン押下
        # 292 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 293 帳票（PDF）: 表示
        # 294 帳票（PDF）: 閉じる
        self.pdf_download("QAPP106600_受給証明書.pdf", "帳票（PDF）_292")
        self.click_button_by_label("閉じる")