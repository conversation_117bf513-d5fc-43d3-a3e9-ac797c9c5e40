from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010_28040101(KodomoSiteTestCaseBase):
    """TestQAP010_28040101"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params_list = case_data.get("sql_params")
        self.exec_sqlfile("test_QAP010_28040101_実施前スクリプト.sql", params=sql_params_list)
        super().setUp()
    
    # 支給認定証発行対象者の抽出が行えることを確認する。
    def test_QAP010_28040101(self):
        """支給認定証発行対象者抽出"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("スケジュール個別追加画面_2")

        # 3 メインメニュー画面: 「バッチ管理」クリック
        # 4 メインメニュー画面: 「即時実行」クリック
        # 5 メインメニュー画面: 「スケジュール個別追加」ボタンダブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        

        # 6 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_6")
        
        # 7 スケジュール個別追加: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_通知書_一覧出力処理 処理区分：
        # 8 スケジュール個別追加: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("スケジュール_業務名_1"), case_data.get("スケジュール_サブシステム名_1"), case_data.get("スケジュール_処理名_1"))

        self.screen_shot("スケジュール個別追加_8")
        
        # 9 スケジュール個別追加: 「(QP1BN00100) 支給認定証データ抽出 」のNoボタン押下
        self.click_button_by_label("1")

        # 10 実行支持画面: 表示
        self.screen_shot("実行支持画面_10")
        # Assert: パラメータ化
        params = [
            {"title":"再発行区分", "type": "select", "value": case_data.get("再発行区分")}
        ]
        self.set_job_param_kodomo(params)
        # 11 実行支持画面: 所管区：空白のまま
        
        # 12 実行支持画面: 発行年月日：初期値のまま（当日）
        
        # 13 実行支持画面: 処理年月日：初期値のまま（当日）
        
        # 14 実行支持画面: 支給決定開始日：初期値のまま（当日）
        
        # 15 実行支持画面: 支給決定終了日：空白のまま
        
        # 16 実行支持画面: 申請事由：空白のまま
        
        # 17 実行支持画面: 認定区分：空白のまま
        
        # 18 実行支持画面: 支給認定証番号：空白のまま
        
        # 19 実行支持画面: 施設種類：空白のまま
        
        # 20 実行支持画面: 施設コード：空白のまま
        
        # 21 実行支持画面: 申込区分：空白のまま
        
        # 22 実行支持画面: 入所基準日：初期値のまま（当日）
        
        # 23 実行支持画面: 郵便区内特別有無：初期値のまま（無）
        
        # 24 実行支持画面: 並び順：初期値のまま
        
        # 25 実行支持画面: 再発行区分：初期値のまま（無）
        
        # 26 実行支持画面: 入所状態区分：空白のまま
        
        # 27 実行支持画面: 第一希望施設コード：空白のまま
        

        # 28 実行支持画面: 「実行」ボタン押下
        self.screen_shot("実行支持画面_28")
        self.click_button_by_label("実行")
        
        # 29 実行結果管理画面: 「検索」ボタン押下
        # 30 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_30")
        
        # 31 メインメニュー画面: 「結果管理」クリック
        # 32 メインメニュー画面: 「納品物確認」ダブルクリック
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 33 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_通知書_一覧出力処理 処理区分：
        # 34 納品物管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("納品物_業務名_1"), case_data.get("納品物_サブシステム名_1"), case_data.get("納品物_処理名_1"))
        self.screen_shot("納品物管理画面_34")
        
        # 35 納品物管理画面: 「QAPP106300_支給認定証交付一覧表.pdf」の「ダウンロード」ボタン押下
        # 36 ファイルダウンロード画面: 表示
        # 37 ファイルダウンロード画面: 「QAPP106300_支給認定証交付一覧表.pdf」のNoボタン押下
        self.pdf_download("QAPP106300_支給認定証交付一覧表.pdf","ファイルダウンロード画面_36")
        
        # 38 ファイルダウンロード画面: 「ファイルを開く」ボタン押下
        
        # 39 ファイルダウンロード画面: ×ボタン押下
        
        # 40 ファイルダウンロード画面: 閉じるボタン押下
        self.click_button_by_label("閉じる")

        # 41 納品物管理画面: 「QAPP106310_支給認定証交付一覧表_要支援措置対象.pdf」の「ダウンロード」ボタン押下
        # 42 ファイルダウンロード画面: 表示
        # 43 ファイルダウンロード画面: 「QAPP106310_支給認定証交付一覧表_要支援措置対象.pdf」のNoボタン押下
        self.pdf_download("QAPP106310_支給認定証交付一覧表_要支援措置対象.pdf","ファイルダウンロード画面_42")
        
        # 44 ファイルダウンロード画面: 保存ボタン押下
        
        # 45 ファイルダウンロード画面: ×ボタン押下
        
        # 46 ファイルダウンロード画面: 閉じるボタン押下
        self.click_button_by_label("閉じる")

        # 47 納品物管理画面: 「納品物管理」タブ×ボタン押下
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        self.wait_page_loaded()

        # 49 実行結果管理画面: パンくずの「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()

        # 50 スケジュール個別追加画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証取下(却下）通知書_一覧出力処理  処理区分：
        # 51 スケジュール個別追加: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("スケジュール_業務名_2"), case_data.get("スケジュール_サブシステム名_2"), case_data.get("スケジュール_処理名_2"))
        self.screen_shot("スケジュール個別追加_51")
        
        # 52 スケジュール個別追加画面: 「(QP1BN00400) 支給認定証取下(却下）データ抽出」のNoボタン押下
        self.click_button_by_label("1")

        # 53 実行支持画面: 表示
        self.screen_shot("実行支持画面_53")
        
        # 54 実行支持画面: 所管区：空白のまま
        
        # 55 実行支持画面: 発行年月日：初期値のまま（当日）
        
        # 56 実行支持画面: 処理年月日：初期値のまま（当日）
        
        # 57 実行支持画面: 対象期間開始日：初期値のまま（当日）
        
        # 58 実行支持画面: 対象期間終了日：初期値のまま（当日）
        
        # 59 実行支持画面: 抽出区分：初期値のまま（取下）
  
        # 60 実行支持画面: 申請事由：空白のまま
        
        # 61 実行支持画面: 認定区分：空白のまま
        
        # 62 実行支持画面: 支給認定証番号：空白のまま
        
        # 63 実行支持画面: 郵便区内特別有無：初期値のまま（無）
        
        # 64 実行支持画面: 並び順：初期値のまま
        
        # 65 実行支持画面: 再発行区分：初期値のまま（無）
        params = [
            {"title":"再発行区分", "type": "select", "value": case_data.get("再発行区分")},
        ]
        self.set_job_param_kodomo(params)

        # 66 実行支持画面: 「実行」ボタン押下
        self.screen_shot("実行支持画面_66")
        self.click_button_by_label("実行")
        
        # 67 実行結果管理画面: 「検索」ボタン押下
        # 68 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_68")
        
        # 69 メインメニュー画面: 「結果管理」クリック
        # 70 メインメニュー画面: 「納品物確認」ダブルクリック
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 71 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証取下(却下）通知書_一覧出力処理  処理区分：
        # 72 納品物管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("納品物_業務名_2"), case_data.get("納品物_サブシステム名_2"), case_data.get("納品物_処理名_2"))
        self.screen_shot("納品物管理画面_72")
        
        # 73 納品物管理画面: 「QAPP106350_支給認定証取下一覧表.pdf」の「ダウンロード」ボタン押下
        # 74 ファイルダウンロード画面: 表示
        # 75 ファイルダウンロード画面: 「QAPP106350_支給認定証取下一覧表.pdf」のNoボタン押下
        self.pdf_download("QAPP106350_支給認定証取下一覧表.pdf","ファイルダウンロード画面_74")
        
        # 76 ファイルダウンロード画面: 保存ボタン押下
        
        # 77 ファイルダウンロード画面: ×ボタン押下
        
        # 78 ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()
        self.wait_page_loaded()

        # 80 納品物管理画面: 「納品物管理」タブ×ボタン押下
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()

        # 81 実行結果管理画面: パンくずの「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()

        # 82 スケジュール個別追加画面: 「(QP1BN00500) 支給認定証取下(却下）通知書出力」のNoボタン押下
        self.click_button_by_label("2")

        # 83 実行支持画面: 表示
        self.screen_shot("実行支持画面_82")
        
        # 84 実行支持画面: 所管区：空白のまま
        
        # 86 実行支持画面: 「実行」ボタン押下
        self.screen_shot("実行支持画面_85")
        self.click_button_by_label("実行")
        
        # 87 実行結果管理画面: 「検索」ボタン押下
        # 88 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_87")
        
        # 89 メインメニュー画面: 「納品物確認」タブクリック
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 90 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証取下(却下）通知書_一覧出力処理  処理区分：
        # 91 納品物管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("納品物_業務名_2"), case_data.get("納品物_サブシステム名_2"), case_data.get("納品物_処理名_2"))
        self.screen_shot("納品物管理画面_90")
        
        # 92 納品物管理画面: 「QAPP106100_支給認定証取下通知書.pdf」の「ダウンロード」ボタン押下
        # 93 ファイルダウンロード画面: 表示
        # 94 ファイルダウンロード画面: 「QAPP106200_支給認定証取下通知書.pdf」のNoボタン押下
        self.pdf_download("QAPP106100_支給認定証取下通知書.pdf","ファイルダウンロード画面_92")
        
        # 95 ファイルダウンロード画面: 保存ボタン押下
        
        # 96 ファイルダウンロード画面: ×ボタン押下
        
        # 97 ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        # 98 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()
        
        # 100 実行結果管理画面: パンくずの「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()

        # 101 スケジュール個別追加画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_交付通知書_一覧(代理申請)出力処理 処理区分：
        # 102 スケジュール個別追加: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("スケジュール_業務名_3"), case_data.get("スケジュール_サブシステム名_3"), case_data.get("スケジュール_処理名_3"))
        self.screen_shot("スケジュール個別追加_101")
        
        # 103 スケジュール個別追加画面: 「(QP1BN01000) 支給認定証データ（代理申請）抽出」のNoボタン押下
        self.click_button_by_label("1")

        # 104 実行支持画面: 表示
        self.screen_shot("実行支持画面_103")
        
        # 105 実行支持画面: 所管区：空白のまま
        
        # 106 実行支持画面: 発行年月日：初期値のまま（当日）
        
        # 107 実行支持画面: 処理年月日：初期値のまま（当日）
        
        # 108 実行支持画面: 支給決定開始日：初期値のまま（当日）
        
        # 109 実行支持画面: 対象期間終了日：空白のまま
        
        # 110 実行支持画面: 事業所番号：空白のまま
        
        # 111 実行支持画面: 申請事由：空白のまま
        
        # 112 実行支持画面: 認定区分：空白のまま
        
        # 113 実行支持画面: 支給認定証番号：空白のまま
        
        # 114 実行支持画面: クラス年齢算出基準年度：初期値のまま（当年度）
        
        # 115 実行支持画面: 施設種類：空白のまま
        
        # 116 実行支持画面: 施設コード：空白のまま
        
        # 117 実行支持画面: 申込区分：空白のまま
        
        # 118 実行支持画面: 入所基準日：初期値のまま（当日）
        
        # 119 実行支持画面: 郵便区内特別有無：初期値のまま（無）
        
        # 120 実行支持画面: 並び順：初期値のまま
        
        # 121 実行支持画面: 再発行区分：初期値のまま（無）
        
        # 122 実行支持画面: 「実行」ボタン押下
        self.screen_shot("実行支持画面_121")
        self.click_button_by_label("実行")
        
        # 123 実行結果管理画面: 「検索」ボタン押下
        # 124 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_123")
        
        # 125 メインメニュー画面: 「納品物確認」タブクリック
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 126 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_交付通知書_一覧(代理申請)出力処理 処理区分：
        # 127 納品物管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("納品物_業務名_3"), case_data.get("納品物_サブシステム名_3"), case_data.get("納品物_処理名_3"))
        self.screen_shot("納品物管理画面_126")
        
        # 128 納品物管理画面: 「QAPP110400_支給認定証交付一覧表（代理申請）.pdf」の「ダウンロード」ボタン押下
        # 129 ファイルダウンロード画面: 表示
        # 130 ファイルダウンロード画面: 「QAPP110400_支給認定証交付一覧表（代理申請）.pdfのNoボタン押下
        self.pdf_download("QAPP110400_支給認定証交付一覧表（代理申請）.pdf","ファイルダウンロード画面_128")

        # 131 ファイルダウンロード画面: 保存ボタン押下
        
        # 132 ファイルダウンロード画面: ×ボタン押下
        
        # 133 ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        # 134 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()
        
        # 136 実行結果管理画面: パンくずの「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()

        # 137 スケジュール個別追加画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_交付通知書_一覧(代理申請)出力処理 処理区分：
        # 138 スケジュール個別追加: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("スケジュール_業務名_3"), case_data.get("スケジュール_サブシステム名_3"), case_data.get("スケジュール_処理名_3"))
        self.screen_shot("スケジュール個別追加_137")
        
        # 139 スケジュール個別追加画面: 「(QP1BN01100) 支給認定証(代理申請)出力」のNoボタン押下
        self.click_button_by_label("2")

        # 140 実行支持画面: 表示
        self.screen_shot("実行支持画面_139")
        
        # 141 実行支持画面: 所管区：空白のまま
        
        # 142 実行支持画面: 文書番号：初期値のまま
        
        # 143 実行支持画面: 「実行」ボタン押下
        self.screen_shot("実行支持画面_142")
        self.click_button_by_label("実行")
        
        # 144 実行結果管理画面: 「検索」ボタン押下
        # 145 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_144")
        
        # 146 メインメニュー画面: 「納品物確認」タブクリック
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 147 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_交付通知書_一覧(代理申請)出力処理 処理区分：
        # 148 納品物管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("納品物_業務名_3"), case_data.get("納品物_サブシステム名_3"), case_data.get("納品物_処理名_3"))
        self.screen_shot("納品物管理画面_147")
        
        # 149 納品物管理画面: 「QAPP110300_支給認定証交付通知書（代理申請）.pdf」の「ダウンロード」ボタン押下
        # 150 ファイルダウンロード画面: 表示
        # 151 ファイルダウンロード画面: 「QAPP110300_支給認定証交付通知書（代理申請）.pdf」のNoボタン押下
        self.pdf_download("QAPP110300_支給認定証交付通知書（代理申請）.pdf","ファイルダウンロード画面_149")
        
        # 152 ファイルダウンロード画面: 保存ボタン押下
        
        # 153 ファイルダウンロード画面: ×ボタン押下
        
        # 154 ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        # 155 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()
        
        # 157 実行結果管理画面: パンくずの「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()

        # 158 スケジュール個別追加画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_交付延期通知書_一覧出力処理 処理区分：
        # 159 スケジュール個別追加: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("スケジュール_業務名_4"), case_data.get("スケジュール_サブシステム名_4"), case_data.get("スケジュール_処理名_4"))
        self.screen_shot("スケジュール個別追加_158")
        
        # 160 スケジュール個別追加画面: 「(QP1BN01500) 支給認定証延期通知書 抽出」のNoボタン押下
        self.click_button_by_label("1")
        
        # 161 実行支持画面: 表示
        self.screen_shot("実行支持画面_160")
        
        # 162 実行支持画面: 所管区：空白のまま
        
        # 163 実行支持画面: 発行年月日：初期値のまま
        
        # 164 実行支持画面: 処理年月日：初期値のまま
        
        # 165 実行支持画面: 延期登録日開始：当日
        
        # 166 実行支持画面: 延期登録日終了：空白のまま
        
        # 167 実行支持画面: 申請日開始：空白のまま
        
        # 168 実行支持画面: 申請日終了：空白のまま
        
        # 169 実行支持画面: 児童宛名コード：空白のまま
        
        # 170 実行支持画面: 並び順：初期値のまま
        
        # 171 実行支持画面: 再発行区分：初期値のまま（無）
        
        # 172 実行支持画面: 「実行」ボタン押下
        self.screen_shot("実行支持画面_171")
        self.click_button_by_label("実行")
        
        # 173 実行結果管理画面: 「検索」ボタン押下
        # 174 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_173")
        
        # 175 メインメニュー画面: 「結果管理」クリック
        # 176 メインメニュー画面: 「納品物確認」ボタンダブルクリック
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 177 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_交付延期通知書_一覧出力処理 処理区分：
        # 178 納品物管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("納品物_業務名_4"), case_data.get("納品物_サブシステム名_4"), case_data.get("納品物_処理名_4"))
        self.screen_shot("納品物管理画面_177")
        
        # 179 納品物管理画面: 「QAPP110100_支給認定延期通知一覧.pdf」の「ダウンロード」ボタン押下
        # 180 ファイルダウンロード画面: 表示
        # 181 ファイルダウンロード画面: 「QAPP110100_支給認定延期通知一覧.pdf」のNoボタン押下
        self.pdf_download("QAPP110100_支給認定延期通知一覧.pdf","ファイルダウンロード画面_179")
        
        # 182 ファイルダウンロード画面: 保存ボタン押下
        
        # 183 ファイルダウンロード画面: ×ボタン押下
        
        # 184 ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        # 185 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()

        # 187 実行結果管理画面: パンくずの「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        # 188 スケジュール個別追加画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_交付延期通知書_一覧出力処理 処理区分：
        self.kennsakujyoukenn_nyuuryoku(case_data.get("スケジュール_業務名_4"), case_data.get("スケジュール_サブシステム名_4"), case_data.get("スケジュール_処理名_4"))
        
        # 189 スケジュール個別追加: 「検索」ボタン押下
        self.screen_shot("スケジュール個別追加_188")
        
        # 190 スケジュール個別追加画面: 「(QP1BN01600) 支給認定証交付延期通知書出力」のNoボタン押下
        self.click_button_by_label("2")

        # 191 実行支持画面: 表示
        self.screen_shot("実行支持画面_190")
        
        # 192 実行支持画面: 所管区：空白のまま
        
        # 193 実行支持画面: 「実行」ボタン押下
        self.screen_shot("実行支持画面_192")
        self.click_button_by_label("実行")
        
        # 194 実行結果管理画面: 「検索」ボタン押下        
        # 195 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_194")

        # 196 メインメニュー画面: 「結果管理」クリック
        # 197 メインメニュー画面: 「納品物確認」ダブルクリック
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        
        # 198 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_交付延期通知書_一覧出力処理 処理区分：
        # 199 納品物管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("納品物_業務名_4"), case_data.get("納品物_サブシステム名_4"), case_data.get("納品物_処理名_4"))
        self.screen_shot("納品物管理画面_198")
        
        # 200 納品物管理画面: 「QAPP109300_支給認定証交付延期通知書.pdf」の「ダウンロード」ボタン押下
        # 201 ファイルダウンロード画面: 表示
        # 202 ファイルダウンロード画面: 「QAPP109300_支給認定証交付延期通知書.pdf」のNoボタン押下
        self.pdf_download("QAPP109300_支給認定証交付延期通知書.pdf","ファイルダウンロード画面_200")
        
        # 203 ファイルダウンロード画面: 保存ボタン押下
        
        # 204 ファイルダウンロード画面: ×ボタン押下
        
        # 205 ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        # 206 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()

        # 207 スケジュール個別追加画面: 「スケジュール個別追加」タブ×ボタンクリック
        
        # 208 メインメニュー画面: ×ボタン押下
        
