import time
from base.kodomo_case import KodomoSiteTestCaseBase
from selenium.webdriver.support.ui import Select

class TestQAP010_28030402(KodomoSiteTestCaseBase):
    """TestQAP010_28030402"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params_list = case_data.get("sql_params")
        self.exec_sqlfile("test_QAP010_28030402_実施前スクリプト.sql", params=sql_params_list)
        super().setUp()
    
    # 利用者負担額切替に必要な新年度税情報の登録、確認ができることを確認する。
    def test_QAP010_28030402(self):
        """新年度税情報確認"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「子ども子育て支援」ボタン押下
        # 4 メインメニュー画面: 「入所管理」ボタン押下
        # 5 メインメニュー画面: 「児童検索」ボタン押下
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])

        # 6 検索条件入力画面: 表示
        self.screen_shot("検索条件入力画面_6")
        
        # 7 検索条件入力画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("AtenaCD"))
        
        # 8 検索条件入力画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()

        # 児童台帳画面: 「世帯台帳」ボタン押下
        self.click_button_by_label("世帯台帳")
        self.wait_page_loaded()

        # 9 世帯台帳画面: 表示
        self.screen_shot("世帯台帳画面_9")
        
        # 10 世帯台帳画面: 「認定情報」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF100300_ninteijoho_li']/a/span").click()
        
        # 11 世帯台帳画面: 表示
        self.screen_shot("世帯台帳画面_11")
        
        # 12 世帯台帳画面: 「課税認定」ボタン押下
        self.click_button_by_label("課税認定")
        self.wait_page_loaded()
        
        # 13 課税認定画面: 表示
        self.screen_shot("課税認定画面_13")
        
        # 14 課税認定画面: 以下を入力年度：
        self.find_element_by_id(u"tab01_QAPF101000_selKensakuJokenNendo_select").click()
        Select(self.find_element_by_id(u"tab01_QAPF101000_selKensakuJokenNendo_select")).select_by_visible_text(case_data.get("年度"))
        
        # 15 課税認定画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 16 課税認定画面: 「追加」ボタン押下
        self.click_button_by_label("追加")
        self.wait_page_loaded()

        # 17 課税認定画面: 以下を修正申請年月日：決定年月日：認定開始年月：●●年９月
        self.find_element_by_id(u"tab01_ZZZ000000_txtKazeiNinteiINFOInputShinseiYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtKazeiNinteiINFOInputShinseiYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtKazeiNinteiINFOInputShinseiYMD_textboxInput").send_keys(case_data.get("申請年月日"))
        self.find_element_by_id(u"tab01_ZZZ000000_txtKazeiNinteiINFOInputKetteiYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtKazeiNinteiINFOInputKetteiYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtKazeiNinteiINFOInputKetteiYMD_textboxInput").send_keys(case_data.get("決定年月日"))
        self.find_element_by_id(u"tab01_ZZZ000000_txtKazeiNinteiINFOInputNinteiYMStart_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtKazeiNinteiINFOInputNinteiYMStart_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtKazeiNinteiINFOInputNinteiYMStart_textboxInput").send_keys(case_data.get("認定開始年月"))
        
        # 18 課税認定画面: 合算区分「要」の世帯員の「対象」チェックボックスをクリック
        #    課税認定画面: 合算区分「要」の世帯員の「入力区分」を連携に選択
        checkbox = self.find_element_by_id("tab01_ZZZ000000_chkZeiINFOTaisho_1_2chk0")
        if checkbox.is_selected() == False:
            self.wait_page_loaded()
            self.find_element_by_id("tab01_ZZZ000000_chkZeiINFOTaisho_1_2chk0").click()
            
        self.find_element_by_id(u"tab01_ZZZ000000_selZeiINFOInputKbn_1_9_select").click()
        Select(self.find_element_by_id(u"tab01_ZZZ000000_selZeiINFOInputKbn_1_9_select")).select_by_visible_text(case_data.get("入力区分"))

        # 19 課税認定画面: 「税額取得」ボタン押下
        self.click_button_by_label("税額取得")

        # 20 課税認定画面: 表示
        # Assert: 入力区分の右枠に当年度連携税が表記されていることを確認
        self.screen_shot("課税認定画面_20")
        
        # 21 課税認定画面: 「合算値代入」ボタン押下
        self.click_button_by_label("合算値代入")

        # 22 課税認定画面: 「登録」ボタン押下
        self.click_button_by_label("登録")

        # 23 課税認定画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        
        # 24 課税認定画面: 登録確認
        # Assert: 登録しました。を確認する。
        self.assert_message_area("tab01_QAPF101000_msg_span","登録しました。")
        self.screen_shot("課税認定画面_24")
        
        # 25 課税認定画面: 「税履歴」タブをクリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF101000_detaillist_li']/a/span").click()
        
        # 26 課税認定画面: 表示
        # Assert: 4月度の履歴と今回登録した9月度の履歴が表示されていることを確認する。
        self.screen_shot("課税認定画面_26")
        
        # 27 課税認定画面: パンくずリスト「世帯台帳」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF101000_navi']/li[3]/a").click()

        # 28 世帯台帳画面: 表示
        self.screen_shot("世帯台帳画面_28")
        
        # 児童検索画面を閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 30 メインメニュー画面: 表示   
        # 31 メインメニュー画面: 「バッチ管理」ボタン押下
        # 32 メインメニュー画面: 「即時実行」ボタン押下
        # 33 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        # 34 スケジュール個別追加画面: 表示
        # 35 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：共通処理名：税異動リスト出力処理
        # 36 スケジュール個別追加画面: 「検索」ボタン押下
        # gyomuNM = case_data.get("GyomuNM")
        # subSystemNM = case_data.get("SubSystemNM")
        # shoriNM_1 = case_data.get("ShoriNM_1")
        # self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM_1)

        # # 37 スケジュール個別追加画面: 「No.1」ボタン押下
        # self.click_button_by_label("1")
        # self.wait_page_loaded()

        # # 38 実行指示画面: 表示
        # self.screen_shot("実行指示画面_40")
        
        # # 39 実行指示画面: 以下を入力処理コメント：テスト対象期間開始：対象期間終了：
        # # Assert: パラメータ化
        # params = [
        #     {"title":"処理コメント", "type": "text", "value": case_data.get("処理コメント")},
        #     {"title":"対象期間開始", "type": "text", "value": case_data.get("対象期間開始")},
        #     {"title":"対象期間終了", "type": "text", "value": case_data.get("対象期間終了")}
        # ]
        # self.set_job_param_kodomo(params)

        # # 40 実行指示画面: 「実行」ボタン押下
        # self.click_button_by_label("実行")

        # # 41 実行結果管理画面: 表示
        # # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # # 処理が終わるまで待機する
        # self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        # self.screen_shot("実行結果管理画面_43")
        
        # # 42 実行結果管理画面: 「No.1」ボタン押下
        # self.click_button_by_label("1")

        # # 43 結果確認画面: 表示
        # self.screen_shot("結果確認画面_44")

        # # 44 ファイルダウンロード画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        # self.find_element_by_xpath("//div[@id='tab01_ZEAF002400_navi']/li[1]/a").click()

        # # 45 スケジュール個別追加画面: 「No.2」ボタン押下
        # self.click_button_by_label("2")
        # self.wait_page_loaded()

        # # 46 実行指示画面: 表示
        # self.screen_shot("実行指示画面_48")
        
        # # 47 実行指示画面: 以下を入力開始連番：終了連番：
        # params = [
        #     {"title":"開始連番", "type": "select", "value": case_data.get("開始連番")},
        #     {"title":"終了連番", "type": "select", "value": case_data.get("終了連番")}
        # ]
        # self.set_job_param_kodomo(params)

        # # 48 実行指示画面: 「実行」ボタン押下
        # self.click_button_by_label("実行")

        # # 49 実行結果管理画面: 表示
        # # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # # 処理が終わるまで待機する
        # self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        # self.screen_shot("実行結果管理画面_51")
        
        # # 50 結果確認画面: 「納品物確認」ボタン押下
        # # 51 納品物管理画面: 表示
        # # self.screen_shot("納品物管理画面_52")
        # # 52 納品物管理画面: 「ダウンロード」ボタンを押下
        # # 53 ファイルダウンロード画面: 表示
        # # self.screen_shot("ファイルダウンロード画面_54")
        # # 54 ファイルダウンロード画面: 「No.1」ボタン押下
        # # 55 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # # 56 PDF: 表示
        # # Assert: 納品物の内容を確認する。
        # # self.screen_shot("PDF_57")
        # # 57 PDF: ×ボタン押下で閉じる

        # # メインメニュー画面: 「バッチ管理」ボタン押下
        # # メインメニュー画面: 「結果管理」ボタン押下
        # # メインメニュー画面: 「納品物確認」ボタン押下
        # self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：共通処理名：税異動リスト出力処理
        # # 納品物確認画面: 「検索」ボタン押下
        # self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_1)
        
        # # 納品物確認画面: 「ダウンロード」ボタン押下
        # self.pdf_download("QAZP000400_税_異動リスト.pdf", "ファイルダウンロード画面_55")

        # # 納品物確認画面: 「閉じる」ボタン押下
        # self.click_button_by_label("閉じる")

        # # 納品物確認タブを閉じる
        # self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # # パンくずリスト「スケジュール個別追加」ボタンをクリック
        # self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()

        # # 59 メインメニュー画面: 表示
        # self.screen_shot("メインメニュー画面_59")
        
        # 60 メインメニュー画面: 「バッチ管理」ボタン押下
        
        # 61 メインメニュー画面: 「即時実行」ボタン押下
        
        # 62 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        
        # 63 スケジュール個別追加画面: 表示
        # self.screen_shot("スケジュール個別追加画面_63")
        
        # 64 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：共通処理名：税異動確認処理
        
        # 65 スケジュール個別追加画面: 「検索」ボタン押下
        
        # 66 スケジュール個別追加画面: 「No.1」ボタン押下
        
        # 67 実行指示画面: 表示
        # self.screen_shot("実行指示画面_67")
        
        # 68 実行指示画面: 以下を入力対象年月：〇〇年9月
        # Assert: パラメータ化
        
        # 69 実行指示画面: 「実行」ボタン押下
        
        # 70 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # self.screen_shot("実行結果管理画面_70")
        
        # 71 実行結果管理画面: 「No.1」ボタン押下
        
        # 72 結果確認画面: 表示
        # self.screen_shot("結果確認画面_72")
        
        # 73 結果確認画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        
        # 74 スケジュール個別追加画面: 「No.2」ボタン押下
        
        # 75 実行指示画面: 表示
        # self.screen_shot("実行指示画面_75")
        
        # 76 実行指示画面: 以下を入力対象年月：〇〇年9月
        # Assert: パラメータ化
        
        # 77 実行指示画面: 「実行」ボタン押下
        
        # 78 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # self.screen_shot("実行結果管理画面_78")
        
        # 79 実行結果管理画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        
        # 80 スケジュール個別追加画面: 「No.4」ボタン押下
        
        # 81 実行指示画面: 表示
        # self.screen_shot("実行指示画面_81")
        
        # 82 実行指示画面: 「実行」ボタン押下
        
        # 83 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # self.screen_shot("実行結果管理画面_83")
        
        # 84 結果確認画面: 「納品物確認」ボタン押下
        
        # 85 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_85")
        
        # 86 納品物管理画面: 「ダウンロード」ボタンを押下
        
        # 87 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_87")
        
        # 88 ファイルダウンロード画面: 「No.1」ボタン押下
        
        # 89 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        
        # 90 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_90")
        
        # 91 PDF: ×ボタン押下で閉じる

        # 93 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_93")
        
        # 94 メインメニュー画面: 「バッチ管理」ボタン押下
        # 95 メインメニュー画面: 「即時実行」ボタン押下
        # 96 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])

        # 97 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_97")
        
        # 98 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：共通処理名：税一括更新　世帯課税認定一括追加
        # 99 スケジュール個別追加画面: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM_3 = case_data.get("ShoriNM_3")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM_3)
          
        # 100 スケジュール個別追加画面: 「No.1」ボタン押下
        self.click_button_by_label("1")
        
        # 101 実行指示画面: 表示
        self.screen_shot("実行指示画面_101")
        
        # 102 実行指示画面: 以下を入力対象年月：〇〇年9月
        # Assert: パラメータ化
        params = [
            {"title":"対象年月", "type": "select", "value": case_data.get("対象年月_text_2")}
        ]
        self.set_job_param_kodomo(params)

        # 103 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")

        # 104 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # 処理が終わるまで待機する
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_104")
        
        # 105 実行結果管理画面: 「No.1」ボタン押下
        # 106 結果確認画面: 表示
        # self.screen_shot("結果確認画面_106")
        # 107 結果確認画面: 「納品物確認」ボタン押下
        # 108 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_108")
        # 109 納品物管理画面: 「ダウンロード」ボタンを押下
        # 110 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_110")
        # 111 ファイルダウンロード画面: 「No.1」ボタン押下
        # 112 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 113 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_113")
        # 114 PDF: ×ボタン押下で閉じる

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：共通処理名：税一括更新　世帯課税認定一括追加
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_3)
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP201900_課税情報更新対象世帯員.pdf", "ファイルダウンロード画面_110")

        # 納品物確認画面: 「閉じる」ボタン押下
        self.click_button_by_label("閉じる")
        
        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # 115 納品物管理画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()

        # 116 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        
        # 117 実行指示画面: 表示
        self.screen_shot("実行指示画面_117")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 118 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")

        # 119 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # 処理が終わるまで待機する
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_119")
        
        # 120 実行結果管理画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        
        # 121 スケジュール個別追加画面: 「No.3」ボタン押下
        self.click_button_by_label("3")
        
        # 122 実行指示画面: 表示
        self.screen_shot("実行指示画面_122")
        
        # 123 実行指示画面: 以下を入力対象年月：〇〇年9月
        # Assert: パラメータ化
        params = [
            {"title":"対象年月", "type": "text", "value": case_data.get("対象年月_text_3")}
        ]
        self.set_job_param_kodomo(params)

        # 124 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")

        # 125 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # 処理が終わるまで待機する
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_125")
        
        # 126 結果確認画面: 「納品物確認」ボタン押下
        # 127 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_129")
        # 128 納品物管理画面: 「ダウンロード」ボタンを押下
        # 129 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_131")
        # 130 ファイルダウンロード画面: 「No.1」ボタン押下
        # 131 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 132 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_134")
        # 133 PDF: ×ボタン押下で閉じる

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：共通処理名：税一括更新　世帯課税認定一括追加
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_3)
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP201920_課税情報更新対象世帯員.pdf", "ファイルダウンロード画面_129")

        # 納品物確認画面: 「閉じる」ボタン押下
        self.click_button_by_label("閉じる")
        
        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # 134 納品物管理画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        
        self.driver.set_window_size(1450,2000)
        # 135 スケジュール個別追加画面: 「No.4」ボタン押下
        self.click_button_by_label("4")
        
        # 136 実行指示画面: 表示
        self.screen_shot("実行指示画面_136")
        
        # 137 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")

        # 138 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # 処理が終わるまで待機する
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_138")
        
        # 139 実行結果管理画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        
        # 140 スケジュール個別追加画面: 「No.5」ボタン押下
        self.click_button_by_label("5")
        
        # 141 実行指示画面: 表示
        self.screen_shot("実行指示画面_141")
        
        # 142 実行指示画面: 以下を入力対象年月：〇〇年9月
        # Assert: パラメータ化
        params = [
            {"title":"対象年月", "type": "text", "value": case_data.get("対象年月_text_4")}
        ]
        self.set_job_param_kodomo(params)

        # 143 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")

        # 144 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # 処理が終わるまで待機する
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_144")
        
        # 145 結果確認画面: 「納品物確認」ボタン押下
        # 146 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_146")
        # 147 納品物管理画面: 「ダウンロード」ボタンを押下
        # 148 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_148")
        # 149 ファイルダウンロード画面: 「No.1」ボタン押下
        # 150 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 151 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_153")
        # 152 PDF: ×ボタン押下で閉じる

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：共通処理名：税一括更新　世帯課税認定一括追加
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_3)
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP201930_課税情報更新対象世帯員.pdf", "ファイルダウンロード画面_148")

        # 納品物確認画面: 「閉じる」ボタン押下
        self.click_button_by_label("閉じる")
        
        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # 153 納品物管理画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        time.sleep(2)
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 154 スケジュール個別追加画面: 「No.6」ボタン押下
        self.click_button_by_label("6")
        
        # 155 実行指示画面: 表示
        self.screen_shot("実行指示画面_155")
        
        # 156 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")

        # 157 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # 処理が終わるまで待機する
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_157")
        
        # パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()

        # 159 メインメニュー画面: 表示
        # self.screen_shot("メインメニュー画面_159")
        
        # 160 メインメニュー画面: 「バッチ管理」ボタン押下
        # 161 メインメニュー画面: 「即時実行」ボタン押下
        # 162 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        
        # 163 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_163")
        
        # 164 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：共通処理名：税一括更新　税額変更反映
        # 165 スケジュール個別追加画面: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM_4 = case_data.get("ShoriNM_4")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM_4)
        
        # 166 スケジュール個別追加画面: 「No.1」ボタン押下
        self.click_button_by_label("1")
        
        # 167 実行指示画面: 表示
        self.screen_shot("実行指示画面_167")
        
        # 168 実行指示画面: 以下を入力対象年月：〇〇年9月
        # Assert: パラメータ化
        params = [
            {"title":"対象年月", "type": "text", "value": case_data.get("対象年月_text_5")}
        ]
        self.set_job_param_kodomo(params)

        # 169 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")

        # 170実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # 処理が終わるまで待機する
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_170")
        
        # 171 実行結果管理画面: 「No.1」ボタン押下
        # 172 結果確認画面: 表示
        # self.screen_shot("結果確認画面_174")
        # 173 結果確認画面: 「納品物確認」ボタン押下
        # 174 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_176")
        # 175 納品物管理画面: 「ダウンロード」ボタンを押下
        # 176 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_178")
        # 177 ファイルダウンロード画面: 「No.1」ボタン押下
        # 178 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 179 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_181")
        # 180 PDF: ×ボタン押下で閉じる
        
        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：共通処理名：税一括更新　税額変更反映
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_4)

        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP201950_課税情報更新対象世帯員（税額変更）.pdf", "ファイルダウンロード画面_176")
        
        # 納品物確認画面: 「閉じる」ボタン押下
        self.click_button_by_label("閉じる")

        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP201960_世帯員の入力課税情報（税情報有）.pdf", "ファイルダウンロード画面_176_2")
        
        # 納品物確認画面: 「閉じる」ボタン押下
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # 181 納品物管理画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        
        # 182 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        
        # 183 実行指示画面: 表示
        self.screen_shot("実行指示画面_183")
        
        # 184 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")

        # 185 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # 処理が終わるまで待機する
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_185")
        
        # 186 実行結果管理画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        
        # 187 スケジュール個別追加画面: 「No.3」ボタン押下
        self.click_button_by_label("3")
        
        # 188 実行指示画面: 表示
        self.screen_shot("実行指示画面_188")
        
        # 189 実行指示画面: 以下を入力対象年月：〇〇年9月
        # Assert: パラメータ化
        params = [
            {"title":"対象年月", "type": "text", "value": case_data.get("対象年月_text_6")}
        ]
        self.set_job_param_kodomo(params)

        # 190 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")

        # 191 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # 処理が終わるまで待機する
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_191")
        
        # 192 結果確認画面: 「納品物確認」ボタン押下
        # 193 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_195")
        # 194 納品物管理画面: 「ダウンロード」ボタンを押下
        # 195 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_197")
        # 196 ファイルダウンロード画面: 「No.1」ボタン押下
        # 197 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 198 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_199")
        # 199 PDF: ×ボタン押下で閉じる

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：共通処理名：税一括更新　税額変更反映
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_4)
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP201970_課税情報更新対象世帯員（税額変更）.pdf", "ファイルダウンロード画面_195")
        
        # 納品物確認画面: 「閉じる」ボタン押下
        self.click_button_by_label("閉じる")

        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP201980_世帯員の入力課税情報（税情報有）.pdf", "ファイルダウンロード画面_195_2")
        
        # 納品物確認画面: 「閉じる」ボタン押下
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
    
        # 200 納品物管理画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        
        # 201 スケジュール個別追加画面: 「No.4」ボタン押下
        self.click_button_by_label("4")
        
        # 202 実行指示画面: 表示
        self.screen_shot("実行指示画面_202")
        
        # 203 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")

        # 204 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # 処理が終わるまで待機する
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_204")
        
        # 205 実行結果管理画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        
