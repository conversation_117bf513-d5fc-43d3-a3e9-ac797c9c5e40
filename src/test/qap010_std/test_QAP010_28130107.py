import time
import unittest
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAP010_28130107(KodomoSiteTestCaseBase):
    """TestQAP010_28130107"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()
    
    # 延長保育解除通知書が作成できることを確認する。
    def test_QAP010_28130107(self):
        """延長保育解除通知書作成"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「バッチ管理」ボタン押下
        
        # 4 メインメニュー画面: 「即時実行」ボタン押下
        
        # 5 メインメニュー画面: 「スケジュール個別追加」ボタンをダブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        # 6 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_6")
        
        # 7 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：入所処理名：延長保育実施解除通知書_一覧出力処理
        # 8 スケジュール個別追加画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名"), case_data.get("サブシステム名"), case_data.get("処理名"))
        
        # 9 スケジュール個別追加画面: 「No.1」ボタン押下
        self.click_button_by_label("1")

        # 10 実行指示画面: サービス区分：延長対象年度：対象年月開始：対象年月終了：発行年月日：
        params = [
            {"title":"サービス区分", "type": "select", "value": case_data.get("サービス区分")},
            {"title":"対象年度", "type": "text", "value": case_data.get("対象年度")},
            {"title":"対象年月開始", "type": "text", "value": case_data.get("対象年月開始")},
            {"title":"対象年月終了", "type": "text", "value": case_data.get("対象年月終了")},
            {"title":"発行年月日", "type": "text", "value": case_data.get("発行年月日")},
            {"title":"再発行区分", "type": "select", "value": case_data.get("再発行区分")}
        ]
        self.set_job_param_kodomo(params)
        # 11 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        # 12 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_12")
        
        # 13 実行結果管理画面: 「No.1」ボタン押下
        # self.click_button_by_label("1")
        # self.wait_page_loaded()
        # 14 結果確認画面: 表示
        # self.screen_shot("結果確認画面_14")
        
        # 15 結果確認画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        # 16 納品物管理画面: 表示
        self.screen_shot("納品物管理画面_16")
        
        # 17 納品物管理画面: 「ダウンロード」ボタンを押下
        # 18 ファイルダウンロード画面: 「No.1」ボタン押下
        
        # 19 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        
        # 20 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_20")
        
        # 21 帳票（PDF）: ×ボタン押下でPDFを閉じる
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名"),  case_data.get("サブシステム名"), case_data.get("処理名"))
        self.pdf_download("QAPP111800_延長保育実施解除対象者一覧.pdf", "")
        self.click_button_by_label("閉じる")
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        # 22 メインメニュー画面: 「バッチ管理」ボタン押下
        
        # 23 メインメニュー画面: 「即時実行」ボタン押下
        
        # 24 メインメニュー画面: 「スケジュール個別追加」ボタンをダブルクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        # 25 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_25")
        
        # 26 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：入所処理名：延長保育実施解除通知書_一覧出力処理
        
        # 27 スケジュール個別追加画面: 「検索」ボタン押下

        # 28 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        time.sleep(2) 
        # 29 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        # 30 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_30")
        
        # 31 実行結果管理画面: 「No.1」ボタン押下
        # self.click_button_by_label("1")
        # self.wait_page_loaded()
        # 32 結果確認画面: 表示
        # self.screen_shot("結果確認画面_32")
        
        # 33 結果確認画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        # 34 納品物管理画面: 表示
        self.screen_shot("納品物管理画面_34")
        
        # 35 納品物管理画面: 「ダウンロード」ボタンを押下
        
        # 36 ファイルダウンロード画面: 「No.1」ボタン押下
        
        # 37 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        
        # 38 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_38")
        
        # 39 帳票（PDF）: ×ボタン押下でPDFを閉じる
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名"),  case_data.get("サブシステム名"), case_data.get("処理名"))
        self.pdf_download("QAPP111900_延長保育実施解除通知書.pdf", "")
        self.click_button_by_label("閉じる")
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        self.find_element_by_xpath("//ul[@id='work_tab']/li[1]/span[1]").click()
        # 40 対象画面名: 操作内容
        # Assert: アサート内容
        # self.screen_shot("対象画面名_40")
 
        # 41 メインメニュー画面: 表示
        # self.screen_shot("メインメニュー画面_41")
        
        # 42 メインメニュー画面: 「子ども子育て支援」ボタン押下
        
        # 43 メインメニュー画面: 「入所管理」ボタン押下
        
        # 44 メインメニュー画面: 「児童検索」ボタンダブルクリック
    

        # 45 児童検索画面: 表示
        # self.screen_shot("児童検索画面_45")
        
        # 46 児童検索画面: 住民コード：
       
        
        # 47 児童検索画面: 「検索」ボタン押下
      

        # 48 児童台帳画面: 表示
        # self.screen_shot("児童台帳画面_48")
        
        # 49 児童台帳画面: 「入所管理」タブをクリック
  
        # 50 児童台帳画面: 「入所管理」ボタン押下
       
        
        # 51 入所管理（申込）画面: 表示
        # self.screen_shot("入所管理（申込）画面_51")
        
        # 52 入所管理（申込）画面: 「印刷」ボタン押下
       
        # 53 印刷指示画面: 表示
        # self.screen_shot("印刷指示画面_53")
        
        # 54 印刷指示画面: 「延長保育実施解除通知書」をチェック
        
        # 55 印刷指示画面: 「延長保育実施解除通知書」タブをクリック


        # 56 印刷指示画面: ※パラメータ不明
        
        # 57 印刷指示画面: 「印刷」ボタン押下
    
        
        # 58 印刷一覧画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # self.screen_shot("印刷一覧画面_58")
        
        # 59 印刷一覧画面: 「No.1」ボタン押下
        
        # 60 印刷一覧画面: 「ファイルを開く(O)」ボタンを押下
        
        # 61 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_61")
        
        # 62 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
