from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010_28090104(KodomoSiteTestCaseBase):
    """TestQAP010_28090104"""

    def setUp(self):
        super().setUp()
    
    # 認定結果に関わる各種通知書の作成ができることを確認する。
    def test_QAP010_28090104(self):
        """各種通知書作成"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        self.do_login()
        # 2 メインメニュー　画面: 表示
        self.screen_shot("メインメニュー　画面_2")
        
        # 3 メインメニュー　画面: 「バッチ管理」ボタン押下
        
        # 4 メインメニュー　画面: 「即時実行」ボタンを押下
        
        # 5 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])

        # 6 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        
        # 7 スケジュール個別追加　画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_1"), case_data.get("サブシステム名_1"), case_data.get("処理名_1"))
       
        # 8 スケジュール個別追加　画面: 表示
        self.screen_shot("スケジュール個別追加　画面_8")
        
        # 9 スケジュール個別追加　画面: 「№1」ボタン押下
        self.click_button_by_label("1")

        # 10 実行指示　画面: 表示
        self.screen_shot("実行指示　画面_10")
        
        # 11 実行指示　画面: 施設等利用給付決定開始日、施設等利用給付終了日、支給認定番号、発行年月日、児童宛名コード　を入力
        # Assert: パラメータ化
        
        # 12 実行指示　画面: 認定区分、代理施設有無、並び順、再発行区分、郵便区内特別有無　を選択
        # Assert: パラメータ化
        params = [
            {"title":"施設等利用給付決定開始日", "type": "text", "value": case_data.get("施設等利用給付決定開始日")},
            {"title":"施設等利用給付終了日", "type": "text", "value": case_data.get("施設等利用給付終了日")},
            {"title":"支給認定番号", "type": "text", "value": case_data.get("支給認定番号")},
            {"title":"発行年月日", "type": "text", "value": case_data.get("発行年月日")},
            {"title":"児童宛名コード", "type": "text", "value": case_data.get("児童宛名コード")},
            {"title":"認定区分", "type": "select", "value": case_data.get("認定区分")},
            {"title":"代理施設有無", "type": "select", "value": case_data.get("代理施設有無")},
            {"title":"並び順", "type": "select", "value": case_data.get("並び順")},
            {"title":"再発行区分", "type": "select", "value": case_data.get("再発行区分")},
            {"title":"郵便区内特別有無", "type": "select", "value": case_data.get("郵便区内特別有無")},
        ]
        self.set_job_param_kodomo(params)
        # 13 実行指示　画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.screen_shot("実行指示　画面_13")
        
        # 14 実行指示　画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()

        # 15 実行結果管理　画面: 表示
        # Assert: 「（QPNBN00300）施設等利用給付認定一覧」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理　画面_15")
        
        # 16 実行結果管理　画面: 「№」ボタン押下
        # self.click_button_by_label("1")

        # 17 結果確認　画面: 表示
        # self.screen_shot("結果確認　画面_17")
        
        # 18 結果確認　画面: 「ダウンロード」ボタン押下
        # self.click_button_by_label("ダウンロード")
        
        # 19 ファイルダウンロード: 「1」ボタン押下
        # self.driver.switch_to.frame(1)
        # self.find_element_by_xpath('//*[@id="FILE_LIST"]/table/tbody/tr/td[1]/input').click()
        # self.driver.switch_to.default_content()
        # self.click_button_by_label("閉じる")

        # 20 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 21 ログ: 表示
        # self.screen_shot("ログ_21")
        
        # 22 ログ: 閉じる
        
        # 23 結果確認　画面: 「納品物確認」ボタン押下
        
        # 24 納品物管理　画面: QAPP700100_施設等利用給付認定一覧.pdf　「ダウンロード」ボタン押下
        
        # 25 ファイルダウンロード: 「1」ボタン押下
        
        # 26 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 27 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_27")
        
        # 28 帳票（PDF）: 閉じる
        
        # 29 納品物管理　画面: 施設等利用給付認定一覧.csv　「ダウンロード」ボタン押下
        
        # 30 ファイルダウンロード: 「1」ボタン押下
        
        # 31 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 32 帳票（CSV）: 表示
        # self.screen_shot("帳票（CSV）_32")
        
        # 33 帳票（CSV）: 閉じる
        
        # 34 対象画面名: 操作内容
        # Assert: アサート内容
        # self.screen_shot("対象画面名_34")

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援、サブシステム名：認可外申請管理、処理名：認可外申請データ取込処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_1"), case_data.get("サブシステム名_1"), case_data.get("処理名_1"))
        
        # 納品物管理画面: 「QAPP700100_施設等利用給付認定一覧.pdf」の「ダウンロード」ボタン押下
        #ファイルダウンロード画面: 表示
        #ファイルダウンロード画面: 「QAPP700100_施設等利用給付認定一覧.pdf」のNoボタン押下
        self.pdf_download("QAPP700100_施設等利用給付認定一覧.pdf","")

        # ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()
        self.wait_page_loaded()

        # 納品物管理画面: 「施設等利用給付認定一覧.csv」の「ダウンロード」ボタン押下
        #ファイルダウンロード画面: 表示
        #ファイルダウンロード画面: 「施設等利用給付認定一覧.csv」のNoボタン押下
        self.pdf_download("施設等利用給付認定一覧.csv","")

        # ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        # 納品物管理画面: 「納品物管理」タブ×ボタン押下
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()

         # スケジュール個別追加画面: 「スケジュール個別追加」タブ×ボタン押下
        self.find_element_by_xpath("//li[@id='01']/span[2]").click()
        self.wait_page_loaded()

        # 35 メインメニュー　画面: 表示
        self.screen_shot("メインメニュー　画面_35")
        
        # 36 メインメニュー　画面: 「バッチ管理」ボタン押下
        
        # 37 メインメニュー　画面: 「即時実行」ボタンを押下
        
        # 38 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])

        # 39 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        # 40 スケジュール個別追加　画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_1"), case_data.get("サブシステム名_1"), case_data.get("処理名_1"))
        
        # 41 スケジュール個別追加　画面: 表示
        self.screen_shot("スケジュール個別追加　画面_41")
        
        # 42 スケジュール個別追加　画面: №「2」ボタン押下
        self.click_button_by_label("2")

        # 43 実行指示　画面: 表示
        self.screen_shot("実行指示　画面_43")
        
        # 44 実行指示　画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.screen_shot("実行指示　画面_44")
        
        # 45 実行指示　画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()

        # 46 実行結果管理　画面: 表示
        # Assert: 「（QPNBN00400）施設等利用給付認定通知書」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理　画面_46")
        
        # 47 実行結果管理　画面: 「№」ボタン押下
        # self.click_button_by_label("1")

        # 48 結果確認　画面: 表示
        # self.screen_shot("結果確認　画面_48")
        
        # 49 結果確認　画面: 「ダウンロード」ボタン押下
        # self.click_button_by_label("ダウンロード")

        # 50 ファイルダウンロード: 「1」ボタン押下
        # self.driver.switch_to.frame(1)
        # self.find_element_by_xpath('//*[@id="FILE_LIST"]/table/tbody/tr/td[1]/input').click()
        # self.driver.switch_to.default_content()
        # self.click_button_by_label("閉じる")

        # 51 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 52 ログ: 表示
        # self.screen_shot("ログ_52")
        
        # 53 ログ: 閉じる
        
        # 54 結果確認　画面: 「納品物確認」ボタン押下
        
        # 55 納品物管理　画面: QAPP700200_施設等利用給付認定通知書.pdf　「ダウンロード」ボタン押下
        
        # 56 ファイルダウンロード: 「1」ボタン押下
        
        # 57 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 58 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_58")
        
        # 59 帳票（PDF）: 閉じる
        
        # 60 対象画面名: 操作内容
        # Assert: アサート内容
        # self.screen_shot("対象画面名_60")

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        # 納品物確認画面: 業務名：子ども子育て支援、サブシステム名：認可外申請管理、処理名：施設等利用給付認定通知書
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_1"), case_data.get("サブシステム名_1"), case_data.get("処理名_1"))
        
        # 納品物管理画面: 「QQAPP700200_施設等利用給付認定通知書.pdf」の「ダウンロード」ボタン押下
        #ファイルダウンロード画面: 表示
        #ファイルダウンロード画面: 「QAPP700200_施設等利用給付認定通知書.pdf」のNoボタン押下
        self.pdf_download("QAPP700200_施設等利用給付認定通知書.pdf","")
        self.wait_page_loaded()

        # ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        # 納品物管理画面: 「納品物管理」タブ×ボタン押下
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()

         # スケジュール個別追加画面: 「スケジュール個別追加」タブ×ボタン押下
        self.find_element_by_xpath("//li[@id='01']/span[2]").click()
        self.wait_page_loaded()
      
        # 61 メインメニュー　画面: 表示
        self.screen_shot("メインメニュー　画面_61")
        
        # 62 メインメニュー　画面: 「バッチ管理」ボタン押下
        # 63 メインメニュー　画面: 「即時実行」ボタンを押下
        # 64 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])

        # 65 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        # 66 スケジュール個別追加　画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_2"), case_data.get("サブシステム名_2"), case_data.get("処理名_2"))
        
        # 67 スケジュール個別追加　画面: 表示
        self.screen_shot("スケジュール個別追加　画面_67")
        
        # 68 スケジュール個別追加　画面: 「№1」ボタン押下
        self.click_button_by_label("1")

        # 69 実行指示　画面: 表示
        self.screen_shot("実行指示　画面_69")
        
        # 70 実行指示　画面: 施設等利用給付決定開始日、施設等利用給付終了日、支給認定番号、発行年月日、児童宛名コード　を入力
        # Assert: パラメータ化
        
        # 71 実行指示　画面: 認定区分、代理施設有無、並び順、再発行区分、郵便区内特別有無　を選択
        # Assert: パラメータ化
        params = [
            {"title":"施設等利用給付決定開始日", "type": "text", "value": case_data.get("却下_施設等利用給付決定開始日")},
            {"title":"施設等利用給付終了日", "type": "text", "value": case_data.get("却下_施設等利用給付終了日")},
            {"title":"支給認定番号", "type": "text", "value": case_data.get("却下_支給認定番号")},
            {"title":"発行年月日", "type": "text", "value": case_data.get("却下_発行年月日")},
            {"title":"児童宛名コード", "type": "text", "value": case_data.get("却下_児童宛名コード")},
            {"title":"認定区分", "type": "select", "value": case_data.get("却下_認定区分")},
            {"title":"代理施設有無", "type": "select", "value": case_data.get("却下_代理施設有無")},
            {"title":"並び順", "type": "select", "value": case_data.get("却下_並び順")},
            {"title":"再発行区分", "type": "select", "value": case_data.get("却下_再発行区分")},
            {"title":"郵便区内特別有無", "type": "select", "value": case_data.get("却下_郵便区内特別有無")},
        ]
        self.set_job_param_kodomo(params)
        # 72 実行指示　画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.screen_shot("実行指示　画面_72")
        
        # 73 実行指示　画面: 「実行」ボタン押下
        self.click_button_by_label("実行")

        # 74 実行結果管理　画面: 表示
        # Assert: 「（QPNBN02200）施設等利用給付認定申請却下一覧」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理　画面_74")
        
        # 75 実行結果管理　画面: 「№」ボタン押下
        # self.click_button_by_label("1")

        # 76 結果確認　画面: 表示
        # self.screen_shot("結果確認　画面_76")
        
        # 77 結果確認　画面: 「ダウンロード」ボタン押下
        # self.click_button_by_label("ダウンロード")

        # 78 ファイルダウンロード: 「1」ボタン押下
        # self.driver.switch_to.frame(1)
        # self.find_element_by_xpath('//*[@id="FILE_LIST"]/table/tbody/tr/td[1]/input').click()
        # self.driver.switch_to.default_content()
        # self.click_button_by_label("閉じる")

        # 79 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 80 ログ: 表示
        # self.screen_shot("ログ_80")
        
        # 81 ログ: 閉じる
        
        # 82 結果確認　画面: 「納品物確認」ボタン押下
        
        # 83 納品物管理　画面: QAPP702000_施設等利用給付認定申請却下一覧.pdf　「ダウンロード」ボタン押下
        
        # 84 ファイルダウンロード: 「1」ボタン押下
        
        # 85 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 86 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_86")
        
        # 87 帳票（PDF）: 閉じる
        
        # 88 納品物管理　画面: 施設等利用給付認定申請却下一覧.csv　「ダウンロード」ボタン押下
        
        # 89 ファイルダウンロード: 「1」ボタン押下
        
        # 90 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 91 帳票（CSV）: 表示
        # self.screen_shot("帳票（CSV）_91")
        
        # 92 帳票（CSV）: 閉じる
        
        # 93 対象画面名: 操作内容
        # Assert: アサート内容
        # self.screen_shot("対象画面名_93")

        # 納品物確認画面: 業務名：子ども子育て支援、サブシステム名：認可外申請管理、処理名：認可外申請データ取込処理
        # 納品物確認画面: 「検索」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_2"), case_data.get("サブシステム名_2"), case_data.get("処理名_2"))
        self.wait_page_loaded()

        # 納品物管理画面: 「QAPP702000_施設等利用給付認定申請却下一覧.pdf」の「ダウンロード」ボタン押下
        #ファイルダウンロード画面: 表示
        #ファイルダウンロード画面: 「QAPP702000_施設等利用給付認定申請却下一覧.pdf」のNoボタン押下
        self.pdf_download("QAPP702000_施設等利用給付認定申請却下一覧.pdf","")
        self.wait_page_loaded()

        # ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()
        self.wait_page_loaded()

        # 納品物管理画面: 「施設等利用給付認定申請却下一覧.csv」の「ダウンロード」ボタン押下
        #ファイルダウンロード画面: 表示
        #ファイルダウンロード画面: 「施設等利用給付認定申請却下一覧.csv」のNoボタン押下
        self.pdf_download("施設等利用給付認定申請却下一覧.csv","")
        self.wait_page_loaded()

        # ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()
        self.wait_page_loaded()

        # 納品物管理画面: 「納品物管理」タブ×ボタン押下
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()

         # スケジュール個別追加画面: 「スケジュール個別追加」タブ×ボタン押下
        self.find_element_by_xpath("//li[@id='01']/span[2]").click()
        self.wait_page_loaded()

        # 94 メインメニュー　画面: 表示
        self.screen_shot("メインメニュー　画面_94")
        
        # 95 メインメニュー　画面: 「バッチ管理」ボタン押下
        
        # 96 メインメニュー　画面: 「即時実行」ボタンを押下
        
        # 97 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])

        # 98 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        # 99 スケジュール個別追加　画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_2"), case_data.get("サブシステム名_2"), case_data.get("処理名_2"))
        self.wait_page_loaded()

        # 100 スケジュール個別追加　画面: 表示
        self.screen_shot("スケジュール個別追加　画面_100")
        
        # 101 スケジュール個別追加　画面: №「2」ボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()

        # 102 実行指示　画面: 表示
        self.screen_shot("実行指示　画面_102")
        
        # 103 実行指示　画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.screen_shot("実行指示　画面_103")
        self.wait_page_loaded()
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 104 実行指示　画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()

        # 105 実行結果管理　画面: 表示
        # Assert: 「（QPNBN02300）施設等利用給付認定申請却下通知書」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理　画面_105")
        
        # 106 実行結果管理　画面: 「№」ボタン押下
        
        # 107 結果確認　画面: 表示
        # self.screen_shot("結果確認　画面_107")
        
        # 108 結果確認　画面: 「ダウンロード」ボタン押下
        
        # 109 ファイルダウンロード: 「1」ボタン押下
        
        # 110 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 111 ログ: 表示
        # self.screen_shot("ログ_111")
        
        # 112 ログ: 閉じる
        
        # 113 結果確認　画面: 「納品物確認」ボタン押下
        
        # 114 納品物管理　画面: QAPP702100_施設等利用給付認定申請却下通知書.pdf　「ダウンロード」ボタン押下
        
        # 115 ファイルダウンロード: 「1」ボタン押下
        
        # 116 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 117 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_117")
        
        # 118 帳票（PDF）: 閉じる
        
        # 119 対象画面名: 操作内容
        # Assert: アサート内容
        # self.screen_shot("対象画面名_119")

        # 納品物確認画面: 業務名：子ども子育て支援、サブシステム名：認可外申請管理、処理名：認可外申請データ取込処理
        # 納品物確認画面: 「検索」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_2"), case_data.get("サブシステム名_2"), case_data.get("処理名_2"))
        self.wait_page_loaded()

        # 納品物管理画面: 「QAPP702100_施設等利用給付認定申請却下通知書.pdf」の「ダウンロード」ボタン押下
        #ファイルダウンロード画面: 表示
        #ファイルダウンロード画面: 「QAPP702100_施設等利用給付認定申請却下通知書.pdf」のNoボタン押下
        self.pdf_download("QAPP702100_施設等利用給付認定申請却下通知書.pdf","")
        self.wait_page_loaded()

        # ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        # 納品物管理画面: 「納品物管理」タブ×ボタン押下
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()

        # スケジュール個別追加画面: 「スケジュール個別追加」タブ×ボタン押下
        self.find_element_by_xpath("//li[@id='01']/span[2]").click()
        self.wait_page_loaded()

        # 120 メインメニュー　画面: 表示
        self.screen_shot("メインメニュー　画面_120")
        
        # 121 メインメニュー　画面: 「バッチ管理」ボタン押下
        # 122 メインメニュー　画面: 「即時実行」ボタンを押下
        # 123 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])

        # 124 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        # 125 スケジュール個別追加　画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_3"), case_data.get("サブシステム名_3"), case_data.get("処理名_3"))
        
        # 126 スケジュール個別追加　画面: 表示
        self.screen_shot("スケジュール個別追加　画面_126")
        
        # 127 スケジュール個別追加　画面: 「№1」ボタン押下
        self.click_button_by_label("1")

        # 128 実行指示　画面: 表示
        self.screen_shot("実行指示　画面_128")
        
        # 129 実行指示　画面: 施設等利用給付決定開始日、施設等利用給付終了日、支給認定番号、発行年月日、児童宛名コード　を入力
        # Assert: パラメータ化
        
        # 130 実行指示　画面: 認定区分、代理施設有無、並び順、再発行区分、郵便区内特別有無　を選択
        # Assert: パラメータ化

        params = [
            {"title":"施設等利用給付決定開始日", "type": "text", "value": case_data.get("みなし認定_施設等利用給付決定開始日")},
            {"title":"施設等利用給付終了日", "type": "text", "value": case_data.get("みなし認定_施設等利用給付終了日")},
            {"title":"支給認定番号", "type": "text", "value": case_data.get("みなし認定_支給認定番号")},
            {"title":"発行年月日", "type": "text", "value": case_data.get("みなし認定_発行年月日")},
            {"title":"児童宛名コード", "type": "text", "value": case_data.get("みなし認定_児童宛名コード")},
            {"title":"認定区分", "type": "select", "value": case_data.get("みなし認定_認定区分")},
            {"title":"代理施設有無", "type": "select", "value": case_data.get("みなし認定_代理施設有無")},
            {"title":"並び順", "type": "select", "value": case_data.get("みなし認定_並び順")},
            {"title":"再発行区分", "type": "select", "value": case_data.get("みなし認定_再発行区分")},
            {"title":"郵便区内特別有無", "type": "select", "value": case_data.get("みなし認定_郵便区内特別有無")},
        ]
        self.set_job_param_kodomo(params)

        # 131 実行指示　画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.screen_shot("実行指示　画面_131")
        
        # 132 実行指示　画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()

        # 133 実行結果管理　画面: 表示
        # Assert: 「（QPNBN02600）（みなし認定）施設等利用給付認定一覧出力」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理　画面_133")
        
        # 134 実行結果管理　画面: 「№」ボタン押下
        # self.click_button_by_label("1")

        # 135 結果確認　画面: 表示
        # self.screen_shot("結果確認　画面_135")
        
        # 136 結果確認　画面: 「ダウンロード」ボタン押下
        # self.click_button_by_label("ダウンロード")

        # 137 ファイルダウンロード: 「1」ボタン押下
        # self.driver.switch_to.frame(1)
        # self.find_element_by_xpath('//*[@id="FILE_LIST"]/table/tbody/tr/td[1]/input').click()
        # self.driver.switch_to.default_content()
        # self.click_button_by_label("閉じる")

        # 138 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 139 ログ: 表示
        # self.screen_shot("ログ_139")
        
        # 140 ログ: 閉じる
        
        # 141 結果確認　画面: 「納品物確認」ボタン押下
        
        # 142 納品物管理　画面: QAPP702400_（みなし認定）施設等利用給付認定一覧.pdf　「ダウンロード」ボタン押下
        
        # 143 ファイルダウンロード: 「1」ボタン押下
        
        # 144 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 145 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_145")
        
        # 146 帳票（PDF）: 閉じる
        
        # 147 納品物管理　画面: （みなし認定）施設等利用給付認定一覧.csv　「ダウンロード」ボタン押下
        
        # 148 ファイルダウンロード: 「1」ボタン押下
        
        # 149 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 150 帳票（CSV）: 表示
        # self.screen_shot("帳票（CSV）_150")
        
        # 151 帳票（CSV）: 閉じる
        
        # 152 対象画面名: 操作内容
        # Assert: アサート内容
        # self.screen_shot("対象画面名_152")

        # 納品物確認画面: 業務名：子ども子育て支援、サブシステム名：認可外申請管理、処理名：認可外申請データ取込処理
        # 納品物確認画面: 「検索」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_3"), case_data.get("サブシステム名_3"), case_data.get("処理名_3"))
        
        # 納品物管理画面: 「QAPP702400_（みなし認定）施設等利用給付認定一覧.pdf」の「ダウンロード」ボタン押下
        #ファイルダウンロード画面: 「QAPP702400_（みなし認定）施設等利用給付認定一覧.pdf」のNoボタン押下
        self.pdf_download("QAPP702400_（みなし認定）施設等利用給付認定一覧.pdf","")
        self.wait_page_loaded()

        # ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        #ファイルダウンロード画面: 「（みなし認定）施設等利用給付認定一覧.csv」のNoボタン押下
        self.pdf_download("（みなし認定）施設等利用給付認定一覧.csv","")
        self.wait_page_loaded()
        # ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        # 納品物管理画面: 「納品物管理」タブ×ボタン押下
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()

        # スケジュール個別追加画面: 「スケジュール個別追加」タブ×ボタン押下
        self.find_element_by_xpath("//li[@id='01']/span[2]").click()
        self.wait_page_loaded()

        # 153 メインメニュー　画面: 表示
        self.screen_shot("メインメニュー　画面_153")
        
        # 154 メインメニュー　画面: 「バッチ管理」ボタン押下
        # 155 メインメニュー　画面: 「即時実行」ボタンを押下
        # 156 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])

        # 157 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        # 158 スケジュール個別追加　画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_3"), case_data.get("サブシステム名_3"), case_data.get("処理名_3"))
        
        # 159 スケジュール個別追加　画面: 表示
        self.screen_shot("スケジュール個別追加　画面_159")
        
        # 160 スケジュール個別追加　画面: №「2」ボタン押下
        self.click_button_by_label("2")

        # 161 実行指示　画面: 表示
        self.screen_shot("実行指示　画面_161")
        
        # 162 実行指示　画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.screen_shot("実行指示　画面_162")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 163 実行指示　画面: 「実行」ボタン押下
        self.click_button_by_label("実行")

        # 164 実行結果管理　画面: 表示
        # Assert: 「（QPNBN02700）（みなし認定）施設等利用給付認定通知書出力」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理　画面_164")
        
        # 165 実行結果管理　画面: 「№」ボタン押下
        # self.click_button_by_label("1")

        # 166 結果確認　画面: 表示
        # self.screen_shot("結果確認　画面_166")
        
        # 167 結果確認　画面: 「ダウンロード」ボタン押下
        # self.click_button_by_label("ダウンロード")

        # 168 ファイルダウンロード: 「1」ボタン押下
        # self.driver.switch_to.frame(1)
        # self.find_element_by_xpath('//*[@id="FILE_LIST"]/table/tbody/tr/td[1]/input').click()
        # self.driver.switch_to.default_content()
        # self.click_button_by_label("閉じる")
        
        # 169 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 170 ログ: 表示
        # self.screen_shot("ログ_170")
        
        # 171 ログ: 閉じる
        
        # 172 結果確認　画面: 「納品物確認」ボタン押下
        
        # 173 納品物管理　画面: （みなし認定）施設等利用給付認定通知書.pdf　「ダウンロード」ボタン押下
        
        # 174 ファイルダウンロード: 「1」ボタン押下
        
        # 175 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 176 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_176")
        
        # 177 帳票（PDF）: 閉じる
        # 納品物確認画面: 業務名：子ども子育て支援、サブシステム名：認可外申請管理、処理名：認可外申請データ取込処理
        # 納品物確認画面: 「検索」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_3"), case_data.get("サブシステム名_3"), case_data.get("処理名_3"))
        
        # 納品物管理画面: 「QAPP702500_（みなし認定）施設等利用給付認定通知書.pdf」の「ダウンロード」ボタン押下
        #ファイルダウンロード画面: 「QAPP702500_（みなし認定）施設等利用給付認定通知書.pdf」のNoボタン押下
        self.pdf_download("QAPP702500_（みなし認定）施設等利用給付認定通知書.pdf","")
        self.wait_page_loaded()

        # ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        
