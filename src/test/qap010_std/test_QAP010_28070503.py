import time
import unittest
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAP010_28070503(KodomoSiteTestCaseBase):
    """TestQAP010_28070503"""

    def setUp(self):
        super().setUp()
    
    # 入金消込おいてエラーとなったものを修正することができることを確認する。
    def test_QAP010_28070503(self):
        """入金消込データ修正"""
        
        driver = None
        case_data = self.test_data["case28070503"]

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.save_screenshot_migrate(driver, "メインメニュー画面_1" , True)
        
        # 2 メインメニュー画面: 「収納」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[3]/span").click()
        time.sleep(1)
        # 3 メインメニュー画面: 「個人別収納状況」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[3]/ul/li[5]/span").click()
        time.sleep(1)
        # 4 メインメニュー画面: 「通番明細」ダブルクリック
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[3]/ul/li[5]/ul/li[4]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)
        # 5 （個人・法人）検索条件入力画面: 表示
        self.save_screenshot_migrate(driver, "（個人・法人）検索条件入力画面_5" , True)
        
        # 6 （個人・法人）検索条件入力画面：科目別収納状況検索エリア: 科目：（パラメータ化）　を入力年度：（パラメータ化）　を入力年分：（パラメータ化）　を入力通知書番号：（パラメータ化）　を入力
        self.form_input_by_id("tab01_JAAF400100_cond_title_0_select" , text="科目別収納状況検索")
        time.sleep(1)
        kamoku = case_data.get("kamoku")
        nendo = case_data.get("nendo")
        nenbun = case_data.get("nenbun")
        tsuchishoNo = case_data.get("tsuchishoNo")
        self.form_input_by_id("tab01_JAAF400100_selKamoku_select" , text=kamoku)
        self.form_input_by_id("tab01_JAAF400100_selChoteiHasseiNendo_select" , text=nendo)
        self.form_input_by_id("tab01_JAAF400100_selKazeiKonkyoNendo_select" , text=nenbun)
        self.find_element_by_id("tab01_JAAF400100_txtTsuchishoNo_textboxInput"). send_keys(tsuchishoNo)
        # 7 （個人・法人）検索条件入力画面：科目別収納状況検索エリア: 「検索（Enter）」ボタンクリック
        self.find_element_by_id("tab01_JAAF400100_WrCmnBtn05_button").click()
        # 8 通番明細画面: 表示
        self.save_screenshot_migrate(driver, "通番明細画面_8" , True)
        
        # 9 通番明細画面: No「(パラメータ化)」ボタンクリック
        # 10 収納状況照会画面: 表示
        self.save_screenshot_migrate(driver, "収納状況照会画面_10" , True)
        
        # 11 収納状況照会画面: 「済通履歴」ボタンクリック
        self.find_element_by_id("tab01_JAAF403000_btnZumitsuRireki_button").click()
        # 12 済通履歴照会画面: 表示
        self.save_screenshot_migrate(driver, "済通履歴照会画面_12" , True)
        
        # 13 済通履歴照会画面: No「(パラメータ化)」ボタンクリック
        self.find_element_by_id("tab01_ZZZ000000_btnNoButton_1_1_button").click()
        # 14 納付書管理画面: 表示
        self.save_screenshot_migrate(driver, "納付書管理画面_14" , True)
        
        # 15 納付書管理画面: 「修正」ボタンクリック
        self.find_element_by_id("tab01_JAAF300500_btnEditChg_button").click()
        # 16 納付書管理画面: 対象科目：初期値年度：初期値年分：初期値通知書番号：初期値期別：初期値枝番：初期値納付区分：初期値会計年度：初期値
        # 17 納付書管理画面: 日計日：（パラメータ化）　を入力納付日：（パラメータ化）　を入力
        nikkeibi = case_data.get("nikkeibi")
        nohubi = case_data.get("nohubi")
        self.find_element_by_id("tab01_JAAF300500_txtNikkeibi_textboxInput").click()
        self.find_element_by_id("tab01_JAAF300500_txtNikkeibi_textboxInput").clear()
        self.find_element_by_id("tab01_JAAF300500_txtNikkeibi_textboxInput"). send_keys(nikkeibi)
        self.find_element_by_id("tab01_JAAF300500_txtNofubi_textboxInput").click()
        self.find_element_by_id("tab01_JAAF300500_txtNofubi_textboxInput").clear()
        self.find_element_by_id("tab01_JAAF300500_txtNofubi_textboxInput"). send_keys(nohubi)
        # 18 納付書管理画面: 納付額：（パラメータ化）　を入力延滞金：（パラメータ化）　を入力督促手数料：（パラメータ化）　を入力
        nofugaku = case_data.get("nofugaku")
        entaikin = case_data.get("entaikin")
        tokusokuryo = case_data.get("tokusokuryo")
        self.find_element_by_id("tab01_JAAF300500_txtNofugaku_textboxInput").clear()
        self.find_element_by_id("tab01_JAAF300500_txtNofugaku_textboxInput"). send_keys(nofugaku)
        self.find_element_by_id("tab01_JAAF300500_txtNofuEntaikin_textboxInput").clear()
        self.find_element_by_id("tab01_JAAF300500_txtNofuEntaikin_textboxInput"). send_keys(entaikin)
        self.find_element_by_id("tab01_JAAF300500_txtTokusokuTesuryo_textboxInput").clear()
        self.find_element_by_id("tab01_JAAF300500_txtTokusokuTesuryo_textboxInput"). send_keys(tokusokuryo)
        
        # 19 納付書管理画面: 納付書番号：1234567890　を入力整理番号：123456　を入力管理番号：1234567890　を入力分割納付回数：10　を入力
        nofushoNo = case_data.get("nofushoNo")
        seiriNo = case_data.get("seiriNo")
        kanriNo = case_data.get("kanriNo")
        bunkatsukaisu = case_data.get("bunkatsukaisu")
        self.find_element_by_id("tab01_JAAF300500_txtNofushoNo_textboxInput").clear()
        self.find_element_by_id("tab01_JAAF300500_txtNofushoNo_textboxInput"). send_keys(nofushoNo)
        self.find_element_by_id("tab01_JAAF300500_txtSeiriNo_textboxInput").clear()
        self.find_element_by_id("tab01_JAAF300500_txtSeiriNo_textboxInput"). send_keys(seiriNo)
        self.find_element_by_id("tab01_JAAF300500_txtKanriNo_textboxInput").clear()
        self.find_element_by_id("tab01_JAAF300500_txtKanriNo_textboxInput"). send_keys(kanriNo)
        self.find_element_by_id("tab01_JAAF300500_txtBunkatsuNofuKaisu_textboxInput").clear()
        self.find_element_by_id("tab01_JAAF300500_txtBunkatsuNofuKaisu_textboxInput"). send_keys(bunkatsukaisu)
        # 20 納付書管理画面: 「入力チェック」ボタンクリック
        self.find_element_by_id("tab01_JAAF300500_checkbtn_button").click()
        time.sleep(1)
        self.save_screenshot_migrate(driver, "納付書管理画面_20" , True)
        
        # 21 納付書管理画面: 「登録」ボタンクリック
        self.find_element_by_id("tab01_JAAF300500_regbtn_button").click()
        # 22 確認ダイアログ: 表示
        self.save_screenshot_migrate(driver, "確認ダイアログ_22" , True)
        
        # 23 確認ダイアログ: 「はい」ボタンクリック
        self.find_elements_by_css_selector(".popupButton")[0].click() 
        # 24 納付書管理画面: 表示
        self.save_screenshot_migrate(driver, "納付書管理画面_24" , True)
        
        # 25 メインメニュー画面: 「バッチ管理」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[2]/span").click()
        time.sleep(1)
        # 26 メインメニュー画面: 「即時実行」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[2]/ul/li[3]/span").click()
        time.sleep(1)
        # 27 メインメニュー画面: 「スケジュール個別追加」ダブルクリック
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[2]/ul/li[3]/ul/li[1]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)
        # 28 スケジュール個別追加画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加画面_28" , True)
        
        # 29 スケジュール個別追加画面: 業務名：収納・滞納 サブシステム名：日次 処理名：日次消込処理 処理区分：
        self.form_input_by_id("tab02_ZEAF000400_SelKensakuGyomuNM_select" , text="収納・滞納")
        self.form_input_by_id("tab02_ZEAF000400_SelKensakuSubSystemNM_select" , text="児童")
        self.form_input_by_id("tab02_ZEAF000400_SelKensakuShoriNM_select" , text="日次消込処理")
        # 30 スケジュール個別追加画面: 「検索」ボタンクリック
        self.find_element_by_id("tab02_ZEAF000400_BtnKensaku_button").click()
        # 31 スケジュール個別追加画面: 「(JAABN06400) 日次消込本処理」Noボタンクリック
        self.find_element_by_id("tab02_ZZZ000000_BtnMode32_3_4_button").click()
        # 32 実行指示画面: 表示
        self.save_screenshot_migrate(driver, "実行指示画面_32" , True)
        
        # 33 実行指示画面: 取扱業務：（パラメータ化）
        toriatsukai_gyomu =   case_data.get("toriatsukai_gyomu")
        self.form_input_by_id("tab02_ZEAF002200_groupingKBN_select" , text=toriatsukai_gyomu)
        self.save_screenshot_migrate(driver, "実行指示画面_33" , True)
        
        # 34 実行指示画面: 「実行」ボタンクリック
        self.find_element_by_id("tab02_ZEAF002200_executebtn_button").click()
        # 35 実行結果管理画面: 表示
        self.save_screenshot_migrate(driver, "実行結果管理画面_35" , True)
        
        # 36 実行結果管理画面: 正常終了まで5分置きに「実行」ボタンクリック
        status = self.find_element_by_xpath("//div[@id='tab02_ZZZ000000_Lblstatus_1_8']/span").text.strip()
        while status == "実行中" or status == "実行待ち":
            time.sleep(300)
            self.find_element_by_id("tab02_ZEAF002300_BtnKensaku_button").click()
            status = self.find_element_by_xpath("//div[@id='tab02_ZZZ000000_Lblstatus_1_8']/span").text.strip()
        self.save_screenshot_migrate(driver, "実行結果管理画面_36" , True)
        
        # 37 メインメニュー画面: 表示
        self.save_screenshot_migrate(driver, "メインメニュー画面_37" , True)
        
        # 38 メインメニュー画面: 「収納」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[3]/span").click()
        time.sleep(1)
        # 39 メインメニュー画面: 「個人別収納状況」クリック
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[3]/ul/li[5]/span").click()
        #time.sleep(1)
        # 40 メインメニュー画面: 「通番明細」ダブルクリック
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[3]/ul/li[5]/ul/li[4]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)
        # 41 （個人・法人）検索条件入力画面: 表示
        self.save_screenshot_migrate(driver, "（個人・法人）検索条件入力画面_41" , True)
        
        # 42 （個人・法人）検索条件入力画面: 住民コード：（パラメータ化）　を入力
        juminCd = case_data.get("juminCd")
        self.find_element_by_id("tab03_JAAF400100_txtJuminCD_textboxInput"). send_keys(juminCd)
        # 43 （個人・法人）検索条件入力画面: 「検索（Enter）」ボタンクリック
        self.find_element_by_id("tab03_JAAF400100_WrCmnBtn05_button").click()
        # 44 通番明細画面: 表示
        self.save_screenshot_migrate(driver, "通番明細画面_44" , True)
        
        # 45 通番明細画面: No「1」ボタンクリック
        self.find_element_by_id("tab03_ZZZ000000_btnNo_1_1_button").click()
        # 46 収納状況照会画面: 表示
        self.save_screenshot_migrate(driver, "収納状況照会画面_46" , True)
        
