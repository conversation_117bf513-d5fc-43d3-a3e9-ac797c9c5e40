
from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010_28030404(KodomoSiteTestCaseBase):
    """TestQAP010_28030404"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()
    
    # 利用者負担額（切替後）決定に関わる各種帳票の出力ができることを確認する。
    def test_QAP010_28030404(self):
        """各種帳票作成"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「バッチ管理」ボタン押下
        # 4 メインメニュー画面: 「即時実行」ボタン押下
        # 5 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # 6 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_6")
        
        # 7 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：入所処理名：保育所徴収金減免決定(却下)通知書出力処理
        # 8 スケジュール個別追加画面: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM = case_data.get("ShoriNM")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM)
        
        # 9 スケジュール個別追加画面: 「No.1」ボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()

        # 10 実行指示画面: 表示
        self.screen_shot("実行指示画面_10")
        
        # 11 実行指示画面: 以下を入力対象年月：〇〇年9月
        # Assert: パラメータ化
        params = [
            {"title":"対象年月", "type": "text", "value": case_data.get("対象年月")},
            {"title":"再発行区分", "type": "select", "value": case_data.get("再発行区分")}
        ]
        self.set_job_param_kodomo(params)
        
        # 12 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 13 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # 処理が終わるまで待機する
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_13")
        
        # 14 実行結果管理画面: 「No.1」ボタン押下
        # 15 結果確認画面: 表示
        # self.screen_shot("結果確認画面_15")
        # 16 結果確認画面: 「納品物確認」ボタン押下
        # 17 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_17")
        # 18 納品物管理画面: 「ダウンロード」ボタンを押下
        # 19 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_19")
        # 20 ファイルダウンロード画面: 「No.1」ボタン押下
        # 21 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 22 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_22")
        # 23 PDF: ×ボタン押下で閉じる

        # スケジュール個別追加タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：入所処理名：保育所徴収金減免決定(却下)通知書出力処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("1",gyomuNM, subSystemNM, shoriNM)
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        #self.pdf_download("QAPP120000_保育料（利用料）減免決定通知書.pdf", "ファイルダウンロード画面_19")
        self.pdf_download("QAPP120000_利用者負担額減免通知書.pdf", "ファイルダウンロード画面_19")
        
