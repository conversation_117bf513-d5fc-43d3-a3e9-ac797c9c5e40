import time
from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010_28030502(KodomoSiteTestCaseBase):
    """TestQAP010_28030502"""

    def setUp(self):
        super().setUp()
    
    # 副食費免除、非免除にかかわる対象者の抽出を行えることを確認する。
    def test_QAP010_28030502(self):
        """対象者抽出"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        self.do_login()
        # 2 メインメニュー 画面: 表示
        self.screen_shot("メインメニュー画面_2")

        # 3 メインメニュー 画面: 「バッチ管理」ボタン押下
        # 4 メインメニュー 画面: 「即時実行」ボタンを押下
        # 5 メインメニュー 画面: 「スケジュール個別追加」ボタン タブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])

        # 6 スケジュール個別追加 画面: 業務名、サブシステム名、処理名 を選択
        # 7 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_1"), case_data.get("サブシステム名_1"), case_data.get("処理名_1"))
        self.wait_page_loaded()

        # 8 スケジュール個別追加 画面: 表示
        self.screen_shot("スケジュール個別追加 画面_8")
        
        # 9 スケジュール個別追加 画面: 「№2」ボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()
        
        # 10 実行指示 画面: 表示
        self.screen_shot("実行指示 画面_10")
        
        # 11 実行指示 画面: 対象年月、対象年度、基準年月 を入力
        # Assert: パラメータ化
        params = [
            {"title":"対象年月", "type": "text", "value": case_data.get("賦課_対象年月")},
            {"title":"対象年度", "type": "text", "value": case_data.get("賦課_対象年度")},
            {"title":"基準年月", "type": "text", "value": case_data.get("賦課_基準年月")}
        ]
        self.set_job_param_kodomo(params)

        # 12 実行指示 画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.wait_page_loaded()
        self.screen_shot("実行指示 画面_12")
        
        # 13 実行指示 画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()
        time.sleep(3)

        # 14 実行結果管理 画面: 表示
        # Assert: 「(QP7BN00210) 月次賦課計算処理」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理 画面_14")
        
        # 15 実行結果管理 画面: パンくず「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        self.wait_page_loaded()

        # 16 スケジュール個別追加 画面: 業務名、サブシステム名、処理名 を選択
        # 17 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_2"), case_data.get("サブシステム名_2"), case_data.get("処理名_2"))
        self.wait_page_loaded()

        # 18 スケジュール個別追加 画面: 表示
        self.screen_shot("スケジュール個別追加 画面_18")
        
        # 19 スケジュール個別追加 画面: 「№1」ボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()

        # 20 実行指示 画面: 表示
        self.screen_shot("実行指示 画面_20")
        
        # 21 実行指示 画面: 対象年月開始、対象年月終了 を入力
        # Assert: パラメータ化
        params = [
            {"title":"対象年月開始", "type": "text", "value": case_data.get("食材料費徴収者_対象年月開始")},
            {"title":"対象年月終了", "type": "text", "value": case_data.get("食材料費徴収者_対象年月終了")},
        ]
        self.set_job_param_kodomo(params)
        self.wait_page_loaded()

        # 22 実行指示 画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.wait_page_loaded()
        self.screen_shot("実行指示 画面_22")
        
        # 23 実行指示 画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()

        # 24 実行結果管理 画面: 表示
        # Assert: 「(QP2BN13100) 食材料費徴収者確認リストデータ作成」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理 画面_24")

        # 25 実行結果管理 画面: 「No.1」ボタン押下

        # 26 結果確認画面: 表示
        # self.screen_shot("結果確認画面_26")
        
        # 27 結果確認画面: 「納品物確認」ボタン押下

        # 28 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_28")
        
        # 29 納品物管理画面: 「ダウンロード」ボタンを押下
     

        # 30 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_30")
        
        # 31 ファイルダウンロード画面: 「No.1」ボタン押下

        # 32 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        
        # 33 CSV: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("CSV_33")
        
        # 34 CSV: ×ボタン押下で閉じる
        
        # 35 ファイルダウンロード画面: 「閉じる」ボタン押下
       
        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        self.screen_shot("納品物管理画面_28")
        # 納品物確認画面: 業務名、サブシステム名、処理名 を選択
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_2"), case_data.get("サブシステム名_2"), case_data.get("処理名_2"))
        
        # 納品物確認画面: 食材料費徴収者確認リスト.csv「ダウンロード」ボタン押下
        self.pdf_download("食材料費徴収者確認リスト.csv", "ファイルダウンロード画面_30")

        # ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()
        # 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()

        # 36 実行結果管理 画面: パンくず「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        self.wait_page_loaded()

        # 37 スケジュール個別追加 画面: 業務名、サブシステム名、処理名 を選択
        # 38 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_2"), case_data.get("サブシステム名_2"), case_data.get("処理名_2"))
        self.wait_page_loaded()

        # 39 スケジュール個別追加 画面: 表示
        self.screen_shot("スケジュール個別追加 画面_39")
        
        # 40 スケジュール個別追加 画面: 「№2」ボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()

        # 41 実行指示 画面: 表示
        self.screen_shot("実行指示 画面_41")
        
        # 42 実行指示 画面: 出力対象、出力対象者 を入力
        # Assert: パラメータ化
        params = [
            {"title":"出力対象", "type": "select", "value": case_data.get("食材料費徴収者_出力対象")},
            {"title":"出力対象者", "type": "select", "value": case_data.get("食材料費徴収者_出力対象者")},
        ]
        self.set_job_param_kodomo(params)
        self.wait_page_loaded()
        # 43 実行指示 画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.wait_page_loaded()
        self.screen_shot("実行指示 画面_43")
        
        # 44 実行指示 画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()

        # 45 実行結果管理 画面: 表示
        # Assert: 「(QP2BN13200) 食材料費徴収者確認リスト出力」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理 画面_45")
        
        # 46 実行結果管理 画面: 「No.1」ボタン押下
   
        # 47 結果確認画面: 表示
        # self.screen_shot("結果確認画面_47")
        
        # 48 結果確認画面: 「納品物確認」ボタン押下
       

        # 49 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_49")
        
        # 50 納品物管理画面: 「ダウンロード」ボタンを押下

        # 51 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_51")
        
        # 52 ファイルダウンロード画面: 「No.1」ボタン押下
        
        # 53 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        
        # 54 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_54")
        
        # 55 PDF: ×ボタン押下で閉じる
        
        # 56 ファイルダウンロード画面: 「閉じる」ボタン押下

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        self.screen_shot("納品物管理画面_49")
        # 納品物確認画面: 業務名、サブシステム名、処理名 を選択
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_2"), case_data.get("サブシステム名_2"), case_data.get("処理名_2"))
        
        # 納品物確認画面: QAPP113100_食材料費徴収者確認リスト.pdf「ダウンロード」ボタン押下
        self.pdf_download("QAPP113100_食材料費徴収者確認リスト.pdf", "ファイルダウンロード画面_51")

        # ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()
        # 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()

        # 57 実行結果管理 画面: パンくず「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        self.wait_page_loaded()

        # 58 スケジュール個別追加 画面: 業務名、サブシステム名、処理名 を選択
        # 59 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_3"), case_data.get("サブシステム名_3"), case_data.get("処理名_3"))
        self.wait_page_loaded()

        # 60 スケジュール個別追加 画面: 表示
        self.screen_shot("スケジュール個別追加 画面_60")
        
        # 61 スケジュール個別追加 画面: 「№1」ボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()

        # 62 実行指示 画面: 表示
        self.screen_shot("実行指示 画面_62")
        
        # 63 実行指示 画面: 対象年月開始、対象年月終了、対象施設 を入力
        # Assert: パラメータ化
        params = [
            {"title":"対象年月開始", "type": "text", "value": case_data.get("食材料費減免対象者_対象年月開始")},
            {"title":"対象年月終了", "type": "text", "value": case_data.get("食材料費減免対象者_対象年月終了")},
            {"title":"対象施設", "type": "select", "value": case_data.get("食材料費減免対象者_対象施設")},
        ]
        self.set_job_param_kodomo(params)
        # 64 実行指示 画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.wait_page_loaded()
        self.screen_shot("実行指示 画面_64")
        
        # 65 実行指示 画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()
        time.sleep(2)
        # 66 実行結果管理 画面: 表示
        # Assert: 「(QP2BN13300) 食材料費減免対象者リストデータ作成」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理 画面_66")
        
        # 67 実行結果管理 画面: 「No.1」ボタン押下
        # 68 結果確認画面: 表示
        # self.screen_shot("結果確認画面_68")
        
        # 69 結果確認画面: 「納品物確認」ボタン押下

        # 70 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_70")
        
        # 71 納品物管理画面: 「ダウンロード」ボタンを押下


        # 72 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_72")
        
        # 73 ファイルダウンロード画面: 「No.1」ボタン押下
        
        # 74 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        
        # 75 CSV: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("CSV_75")
        
        # 76 CSV: ×ボタン押下で閉じる
        
        # 77 ファイルダウンロード画面: 「閉じる」ボタン押下

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        self.screen_shot("納品物管理画面_70")

        # 納品物確認画面: 業務名、サブシステム名、処理名 を選択
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_3"), case_data.get("サブシステム名_3"), case_data.get("処理名_3"))
        
        # 納品物確認画面: 食材料費減免対象者リスト.csv「ダウンロード」ボタン押下
        self.pdf_download("食材料費減免対象者リスト.csv", "ファイルダウンロード画面_72")

        # ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        # 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()

        # 78 実行結果管理 画面: パンくず「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        self.wait_page_loaded()

        # 79 スケジュール個別追加 画面: 業務名、サブシステム名、処理名 を選択
        # 80 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_3"), case_data.get("サブシステム名_3"), case_data.get("処理名_3"))
        self.wait_page_loaded()

        # 81 スケジュール個別追加 画面: 表示
        self.screen_shot("スケジュール個別追加 画面_81")
        
        # 82 スケジュール個別追加 画面: 「№2」ボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()

        # 83 実行指示 画面: 表示
        self.screen_shot("実行指示 画面_83")
        
        # 84 実行指示 画面: 出力対象を入力
        # Assert: パラメータ化
        params = [
            {"title":"出力対象", "type": "select", "value": case_data.get("食材料費減免対象者_出力対象")},
        ]
        self.set_job_param_kodomo(params)

        # 85 実行指示 画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.wait_page_loaded()
        self.screen_shot("実行指示 画面_85")
        
        # 86 実行指示 画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()
        # 87 実行結果管理 画面: 表示
        # Assert: 「(QP2BN13400) 食材料費減免対象者リスト出力」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理 画面_87")
        
        # 88 実行結果管理 画面: 「No.1」ボタン押下


        # 89 結果確認画面: 表示
        # self.screen_shot("結果確認画面_89")
        
        # 90 結果確認画面: 「納品物確認」ボタン押下

        # 91 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_91")
        
        # 92 納品物管理画面: 「ダウンロード」ボタンを押下


        # 93 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_93")
        
        # 94 ファイルダウンロード画面: 「No.1」ボタン押下
        
        # 95 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        
        # 96 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_96")
        
        # 97 PDF: ×ボタン押下で閉じる
         # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        self.screen_shot("納品物管理画面_91")
        
        # 納品物確認画面:  業務名、サブシステム名、処理名 を選択
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_3"), case_data.get("サブシステム名_3"), case_data.get("処理名_3"))
        
        # 納品物確認画面: QAPP113300_食材料費減免対象者リスト.pdf「ダウンロード」ボタン押下
        self.pdf_download("QAPP113300_食材料費減免対象者リスト.pdf", "ファイルダウンロード画面_93")

        # ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()
        
        # 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()
        
