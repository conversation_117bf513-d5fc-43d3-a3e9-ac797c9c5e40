import time
from base.kodomo_case import KodomoSiteTestCaseBase
from selenium.webdriver.support.ui import Select

class TestQAP010_28070103(KodomoSiteTestCaseBase):
    """TestQAP010_28070103"""

    def setUp(self):
        super().setUp()
    
    # 調定金額に誤りがあった場合、算定済調定情報の取消が行ることを確認する。
    def test_QAP010_28070103(self):
        """算定済調定情報取消"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「子ども子育て支援」ボタン押下
        
        # 4 メインメニュー画面: 「入所管理」ボタン押下
        
        # 5 メインメニュー画面: 「児童検索」ボタンダブルクリック
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])

        # 6 児童検索画面: 表示
        self.screen_shot("児童検索画面_6")
        
        # 7 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("児童宛名C"))

        # 8 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()

        # 9 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_9")
        
        # 10 児童台帳画面: 「入所管理」タブをクリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()

        # 11 児童台帳画面: 「入所管理」ボタン押下
        self.click_button_by_label("入所管理") 

        # 12 入所管理（申込）画面: 表示
        self.screen_shot("入所管理（申込）画面_12")
        
        # 13 入所管理（申込）画面: 「修正（取下げ）」ボタン押下
        self.click_button_by_label("修正（取下げ）") 

        # 14 入所管理（申込）画面（取下げタブ）: 表示
        self.screen_shot("入所管理（申込）画面（取下げタブ）_14")
        
        # 15 入所管理（申込）画面（取下げタブ）: 取下年月日：更新区分：取下げ登録・変更理由：その他
        self.find_element_by_id(u"tab01_QAPF103700_txtTorisageYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF103700_txtTorisageYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF103700_txtTorisageYMD_textboxInput").send_keys(case_data.get("取下年月日"))

        self.find_element_by_id(u"tab01_QAPF103700_selTorisageKoshinKbn_select").click()
        Select(self.find_element_by_id(u"tab01_QAPF103700_selTorisageKoshinKbn_select")).select_by_visible_text(case_data.get("更新区分"))

        self.find_element_by_id(u"tab01_QAPF103700_selTorisageRiyu_select").click()
        Select(self.find_element_by_id(u"tab01_QAPF103700_selTorisageRiyu_select")).select_by_visible_text(case_data.get("理由"))
        
        # 16 入所管理（申込）画面（取下げタブ）: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.wait_page_loaded()

        # 17 入所管理（申込）画面（取下げタブ）: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()

        # 18 入所管理（申込）画面（取下げタブ）: 表示
        # Assert: 「更新しました。」のメッセージのチェック
        self.assert_message_area("tab01_QAPF103700_msg_span","更新しました。") 
        self.screen_shot("入所管理（申込）画面（取下げタブ）_18")
        self.find_element_by_xpath("//li[@id='01']/span[2]").click()

        # 19 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_19")
        
        # 20 メインメニュー画面: 「バッチ管理」ボタン押下
        
        # 21 メインメニュー画面: 「即時実行」ボタン押下
        
        # 22 メインメニュー画面: 「スケジュール個別追加」ボタンをダブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        # 23 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_23")
        
        # 24 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：賦課処理名：月次賦課処理
        
        # 25 スケジュール個別追加画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名"), case_data.get("サブシステム名"),case_data.get("処理名"))
        # 26 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()
        # 27 バッチ起動画面: 対象年月：                               　を入力対象年度：                               　を入力基準年月：                               　を入力
        # Assert: パラメータ化
        params = [
            {"title":"対象年月", "type": "text", "value": case_data.get("対象年月")},
            {"title":"対象年度", "type": "text", "value": case_data.get("対象年度")},
            {"title":"基準年月", "type": "text", "value": case_data.get("基準年月")}
        ]
        self.set_job_param_kodomo(params)
        self.wait_page_loaded()

        self.screen_shot("バッチ起動画面_27")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        self.click_button_by_label("実行")
        self.wait_page_loaded()
        # 28 実行結果画面: 表示
        # Assert: 「(QP7BN00210) 月次賦課計算処理」の状態が正常終了となることを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds) 
        self.screen_shot("実行結果画面_28")

        # 29 実行結果画面: 「検索」タブをクリック
        ##self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()
        # 30 入所管理（申込）: 「戻る」ボタン押下
        # self.return_click()
        
        self.find_element_by_xpath("//li[@id='01']/span[2]").click()
        self.wait_page_loaded()

        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        self.wait_page_loaded()
        
        # 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("児童宛名C"))
        self.wait_page_loaded()

        # 児童検索画面: 「検索」ボタン押下
        self.driver.set_window_size(1450,2000)
        self.click_button_by_label("検索(Enter)")
        time.sleep(2)

        # 31 児童台帳画面: 「賦課」ボタンを押下
        self.click_button_by_label("賦課") 
        self.wait_page_loaded()
        
        # 32 児童賦課情報画面: 表示
        self.screen_shot("児童賦課情報画面_32")
        
        # 33 児童賦課情報画面: 賦課年度：
        self.find_element_by_id(u"tab01_QAPF107000_selFukaYear_select").send_keys(case_data.get("賦課_対象年度"))
        # 34 児童賦課情報画面: 表示
        # Assert: 保育料タブに各月の賦課情報が表示されないこと
        self.screen_shot("児童賦課情報画面_34")
        
