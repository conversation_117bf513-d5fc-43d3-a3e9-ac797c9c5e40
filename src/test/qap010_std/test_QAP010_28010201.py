import time
import unittest
from datetime import datetime, date, timedelta
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.select import Select
class TestQAP010_28010201(KodomoSiteTestCaseBase):
    """TestQAP010_28010201"""

    def setUp(self):
        settings = self.common_test_data
        self.test_data = settings.get("test_qap010_28010201")
        atena_list = settings.get("test_qap010_28010201")
        self.exec_sqlfile("QAP010_28010201_削除スクリプト.sql", params=atena_list)
        super().setUp()
    
    # 教育・保育給付認定変更にかかわる対象者が抽出できることを確認する。
    def test_QAP010_28010201(self):
        """対象者抽出"""
        driver = None

        self.do_login()
        # 1 メインメニュー　画面: 表示
        self.save_screenshot_migrate(driver, "メインメニュー 画面_1" , True)
        
        # 2 メインメニュー　画面: 「バッチ管理」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        time.sleep(1)

        # 3 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 4 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 5 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '認定区分変更一覧表（３号→２号）出力処理']").click()

        # 6 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 7 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_7" , True)
        
        # 8 スケジュール個別追加　画面: 「№1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()

        # 9 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_9" , True)
        
        # 10 実行指示　画面: 支給決定開始日、支給決定終了日、施設コード、発行年月日　を入力
        # Assert: パラメータ化
        # 支給決定開始日
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").send_keys(self.test_data.get("case_qap001_shikyuu_kettei_kaishibi"))
        # 支給決定終了日
        self.find_element_by_id("tab01_ZEAF002200_Paratest08_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest08_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest08_textboxInput").send_keys(formatted_date)
        # 施設コード
        self.find_element_by_id("tab01_ZEAF002200_Paratest23_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest23_textboxInput").send_keys(self.test_data.get("case_qap001_shisetsu_code"))
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").send_keys(formatted_date)

        # 11 実行指示　画面: 施設種類、並び順、　を選択
        # Assert: パラメータ化
        # 施設種類
        self.find_element_by_id("tab01_ZEAF002200_Paratest43_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest43_select").send_keys(self.test_data.get("case_qap001_shisetsu_shurui"))
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabi_jun"))
        
        # 12 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_12" , True)
        
        # 13 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 14 実行結果管理　画面: 表示
        # Assert: 「（QP1BN02300）認定区分変更一覧表（３号→２号）　出力」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_14" , True)
        
        # 15 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 16 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_16" , True)
        
        # 17 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()
        
        # 18 ファイルダウンロード: 「1」ボタン押下
        # 19 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 20 ログ: 表示
        # 21 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_20" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 22 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()
        
        # 23 納品物管理　画面: QAPP106700_認定区分変更一覧表（３号→２号）.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：認定区分変更一覧表（３号→２号）出力処理 
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_1 = self.test_data.get("ShoriNM_1")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_1)

        # 24 ファイルダウンロード: 「1」ボタン押下
        # 25 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 26 帳票（PDF）: 表示
        # 27 帳票（PDF）: 閉じる
        self.pdf_download("QAPP106700_認定区分変更一覧表（３号→２号）.pdf", "帳票（PDF）_26")
        self.click_button_by_label("閉じる")
        time.sleep(2)       
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 28 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_28" , True)
        
        # 29 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # 30 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 31 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 32 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '３号→２号切替処理']").click()

        # 33 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 34 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_34" , True)
        
        # 35 スケジュール個別追加　画面: 「№1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()
        
        # 36 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_36" , True)
        
        # 37 実行指示　画面: 基準日、児童生年月日開始、児童生年月日終了、出産時認定可能日数、求職時認定可能日数　を入力
        # Assert: パラメータ化
        # 基準日
        self.find_element_by_id("tab01_ZEAF002200_QApara0177_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_QApara0177_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_QApara0177_textboxInput").send_keys(formatted_date)
        # 児童生年月日開始
        self.find_element_by_id("tab01_ZEAF002200_QApara0178_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_QApara0178_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_QApara0178_textboxInput").send_keys(self.test_data.get("case_qap001_jidou_seinengappi_kaishi"))
        # 児童生年月日終了
        self.find_element_by_id("tab01_ZEAF002200_QApara0179_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_QApara0179_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_QApara0179_textboxInput").send_keys(formatted_date)
        # 出産時認定可能日数
        self.find_element_by_id("tab01_ZEAF002200_QApara0180_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_QApara0180_textboxInput").send_keys(self.test_data.get("case_qap001_shussanji_nintei_kanou_nissuu"))
        # 求職時認定可能日数
        self.find_element_by_id("tab01_ZEAF002200_QApara0181_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_QApara0181_textboxInput").send_keys(self.test_data.get("case_qap001_kyuushokuji_nintei_kanou_nissuu"))

        # 38 実行指示　画面: 支給認定番号配番有、並び順　を選択
        # Assert: パラメータ化
        # 支給認定番号配番有無
        self.find_element_by_id("tab01_ZEAF002200_Paratest182_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest182_select").send_keys(self.test_data.get("case_qap001_shikyuu_nintei_bangou_haibanumu"))
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabijun"))

        # 39 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_39" , True)
        
        # 40 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 41 実行結果管理　画面: 表示
        # Assert: 「（QP1BN02800）３号→２号切替対象者抽出処理」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_41" , True)
        
        # 42 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 43 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_43" , True)
        
        # 44 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()
        
        # 45 ファイルダウンロード: 「1」ボタン押下
        # 46 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 47 ログ: 表示
        # 48 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_47" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 49 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()
        
        # 50 納品物管理　画面: QAPP160100_切替対象者一覧.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()
        
        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：３号→２号切替処理 
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_2 = self.test_data.get("ShoriNM_2")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_2)

        # 51 ファイルダウンロード: 「1」ボタン押下
        # 52 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 53 帳票（PDF）: 表示
        # 54 帳票（PDF）: 閉じる
        self.pdf_download("QAPP160100_支給認定３号→２号切替対象者一覧.pdf", "帳票（PDF）_53(1)")
        self.click_button_by_label("閉じる")
        time.sleep(2)       

        # 50 納品物管理　画面: 切替対象者確認一覧.CSV　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_2_9_button").click()

        # 51 ファイルダウンロード: 「1」ボタン押下
        # 52 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 53 帳票（CSV）: 表示
        # 54 帳票（CSV）: 閉じる
        self.pdf_download("切替対象者確認一覧.CSV", "帳票（CSV）_53(2)")
        self.click_button_by_label("閉じる")
        time.sleep(2)       
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # メインメニュー 画面: 表示
        time.sleep(1)
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        #self.save_screenshot_migrate(driver, "メインメニュー 画面_25" , True)
        
        # メインメニュー 画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # メインメニュー 画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # メインメニュー 画面: 「スケジュール個別追加」ボタン タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # スケジュール個別追加 画面: 業務名、サブシステム名、処理名 を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '３号→２号切替処理']").click()

        # スケジュール個別追加 画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()
        
        # スケジュール個別追加 画面: 表示
        #self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_31" , True)
        
        # スケジュール個別追加 画面: 「№2」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_2_4_button").click()
        
        # 実行指示 画面: 表示
        #self.save_screenshot_migrate(driver, "実行指示 画面_33" , True)
        
        # 実行指示 画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        #self.save_screenshot_migrate(driver, "実行指示 画面_34" , True)
        
        # 実行指示 画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 実行結果管理 画面: 表示
        # Assert: 「（QP1BN02900）３号→２号切替対象者更新処理」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        #self.save_screenshot_migrate(driver, "実行結果管理 画面_36" , True)

        # 55 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_55" , True)
                
        # 56 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # 57 メインメニュー　画面: 「即時実行」ボタンを押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        #time.sleep(1)

        # 58 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 59 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '３号→２号切替処理']").click()

        # 60 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 61 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_61" , True)
        
        # 62 スケジュール個別追加　画面: №「3」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_3_4_button").click()
        
        # 63 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_63" , True)
        
        # 64 実行指示　画面: 施設コード、児童宛名コード、基準日　を入力
        # Assert: パラメータ化
        # 施設コード
        self.find_element_by_id("tab01_ZEAF002200_Paratest23_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest23_textboxInput").send_keys(self.test_data.get("case_qap001_shisetsu_code1"))
        # 児童宛名コード
        self.find_element_by_id("tab01_ZEAF002200_Paratest183_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest183_textboxInput").send_keys(self.test_data.get("case_qap001_jidou_atena_code"))
        # 基準日
        self.find_element_by_id("tab01_ZEAF002200_Paratest36_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest36_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest36_textboxInput").send_keys(formatted_date)

        # 65 実行指示　画面: 入所状態区分、郵便区内特別有無、並び順　を選択
        # Assert: パラメータ化
        # 入所状態区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest184_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest184_select").send_keys(self.test_data.get("case_qap001_nyuusho_joutai_kubun"))
        # 郵便区内特別有無
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").send_keys(self.test_data.get("case_qap001_yuubin_kunai_tokubetsu_umu"))
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabi_jun1"))

        # 66 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_66" , True)
        
        # 67 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 68 実行結果管理　画面: 表示
        # Assert: 「（QP1BN03200）支給認定職権変更一覧表出力処理」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_68" , True)
        
        # 69 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 70 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_70" , True)
        
        # 71 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()
        
        # 72 ファイルダウンロード: 「1」ボタン押下
        # 73 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 74 ログ: 表示
        # 75 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_74" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 76 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()
        
        # 77 納品物管理　画面: QAPP160200_支給認定職権変更一覧表.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()
        
        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：３号→２号切替処理 
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_2 = self.test_data.get("ShoriNM_2")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_2)

        # 78 ファイルダウンロード: 「1」ボタン押下
        # 79 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 80 帳票（PDF）: 表示
        # 81 帳票（PDF）: 閉じる
        self.pdf_download("QAPP160200_支給認定職権変更一覧表.pdf", "帳票（PDF）_80")
        self.click_button_by_label("閉じる")
        time.sleep(2)       
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        """
        # 82 メインメニュー　画面: 表示
        self.screen_shot("メインメニュー 画面_82")
        
        # 83 メインメニュー　画面: 「バッチ管理」ボタン押下
        
        # 84 メインメニュー　画面: 「即時実行」ボタンを押下
        
        # 85 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        
        # 86 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        
        # 87 スケジュール個別追加　画面: 「検索」ボタン押下
        
        # 88 スケジュール個別追加　画面: 表示
        self.screen_shot("スケジュール個別追加 画面_88")
        
        # 89 スケジュール個別追加　画面: №「1」ボタン押下
        
        # 90 実行指示　画面: 表示
        self.screen_shot("実行指示 画面_90")
        
        # 91 実行指示　画面: 対象年月日開始、対象年月日終了、児童宛名コード　を入力
        # Assert: パラメータ化
        
        # 92 実行指示　画面: 並び順　を選択
        # Assert: パラメータ化
        
        # 93 実行指示　画面: 「入力チェック」ボタン押下
        self.screen_shot("実行指示 画面_93")
        
        # 94 実行指示　画面: 「実行」ボタン押下
        
        # 95 実行結果管理　画面: 表示
        # Assert: 「（QP1BN04400）給付認定区分変更対象者一覧出力」の状態が正常終了することを確認
        self.screen_shot("実行結果管理 画面_95")
        
        # 96 実行結果管理　画面: 「№」ボタン押下
        
        # 97 結果確認　画面: 表示
        self.screen_shot("結果確認 画面_97")
        
        # 98 結果確認　画面: 「ダウンロード」ボタン押下
        
        # 99 ファイルダウンロード: 「1」ボタン押下
        
        # 100 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 101 ログ: 表示
        self.screen_shot("ログ_101")
        
        # 102 ログ: 閉じる
        
        # 103 結果確認　画面: 「納品物確認」ボタン押下
        
        # 104 納品物管理　画面: 給付認定区分変更対象者一覧.CSV　「ダウンロード」ボタン押下
        
        # 105 ファイルダウンロード: 「1」ボタン押下
        
        # 106 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 107 帳票（CSV）: 表示
        self.screen_shot("帳票（CSV）_107")
        
        # 108 帳票（CSV）: 閉じる
        
        # 109 メインメニュー　画面: 表示
        self.screen_shot("メインメニュー 画面_109")
        
        # 110 メインメニュー　画面: 「バッチ管理」ボタン押下
        
        # 111 メインメニュー　画面: 「即時実行」ボタンを押下
        
        # 112 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        
        # 113 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        
        # 114 スケジュール個別追加　画面: 「検索」ボタン押下
        
        # 115 スケジュール個別追加　画面: 表示
        self.screen_shot("スケジュール個別追加 画面_115")
        
        # 116 スケジュール個別追加　画面: №「1」ボタン押下
        
        # 117 実行指示　画面: 表示
        self.screen_shot("実行指示 画面_117")
        
        # 118 実行指示　画面: 発行年月日、対象年月日開始、対象年月日終了、支給認定証番号　を入力
        # Assert: パラメータ化
        
        # 119 実行指示　画面: 申請種別、並び順、再発行区分、郵便区内特別有無、　を選択
        # Assert: パラメータ化
        
        # 120 実行指示　画面: 「入力チェック」ボタン押下
        self.screen_shot("実行指示 画面_120")
        
        # 121 実行指示　画面: 「実行」ボタン押下
        
        # 122 実行結果管理　画面: 表示
        # Assert: 「（QP1BN04500）教育・保育給付認定変更対象者一覧出力」の状態が正常終了することを確認
        self.screen_shot("実行結果管理 画面_122")
        
        # 123 実行結果管理　画面: 「№」ボタン押下
        
        # 124 結果確認　画面: 表示
        self.screen_shot("結果確認 画面_124")
        
        # 125 結果確認　画面: 「ダウンロード」ボタン押下
        
        # 126 ファイルダウンロード: 「1」ボタン押下
        
        # 127 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 128 ログ: 表示
        self.screen_shot("ログ_128")
        
        # 129 ログ: 閉じる
        
        # 130 結果確認　画面: 「納品物確認」ボタン押下
        
        # 131 納品物管理　画面: QAPP120500_教育・保育給付認定変更対象者一覧.pdf　「ダウンロード」ボタン押下
        
        # 132 ファイルダウンロード: 「1」ボタン押下
        
        # 133 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 134 帳票（PDF）: 表示
        self.screen_shot("帳票（PDF）_134")
        
        # 135 帳票（PDF）: 閉じる
        
        # 136 納品物管理　画面: 教育・保育給付認定変更対象者一覧.CSV　「ダウンロード」ボタン押下
        
        # 137 ファイルダウンロード: 「1」ボタン押下
        
        # 138 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 139 帳票（CSV）: 表示
        self.screen_shot("帳票（CSV）_139")
        
        # 140 帳票（CSV）: 閉じる
        """
        
        # 141 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_82" , True)
        
        # 142 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)
        
        # 143 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 144 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 145 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定変更決定通知書_一覧出力処理']").click()
        
        # 146 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 147 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_88" , True)
        
        # 148 スケジュール個別追加　画面: №「1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()

        # 149 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_90" , True)
        
        # 150 実行指示　画面: 発行年月日、基準日、支給決定開始日、支給決定終了日、支給認定証番号、児童宛名コード、施設コード　を入力
        # Assert: パラメータ化
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").send_keys(formatted_date)
        # 基準日
        self.find_element_by_id("tab01_ZEAF002200_Paratest36_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest36_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest36_textboxInput").send_keys(formatted_date)
        # 支給決定開始日
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Paratest07_textboxInput").send_keys(self.test_data.get("case_qap001_shikyuu_kettei_kaishibi1"))
        # 支給決定終了日
        self.find_element_by_id("tab01_ZEAF002200_Paratest08_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest08_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest08_textboxInput").send_keys(formatted_date)
        # 支給認定証番号
        self.find_element_by_id("tab01_ZEAF002200_Paratest11_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest11_textboxInput").send_keys(self.test_data.get("case_qap001_shikyuu_ninteishou_bangou"))
        # 児童宛名コード
        self.find_element_by_id("tab01_ZEAF002200_Paratest58_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest58_textboxInput").send_keys(self.test_data.get("case_qap001_jidou_atena_code1"))
        # 施設コード
        self.find_element_by_id("tab01_ZEAF002200_Paratest23_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest23_textboxInput").send_keys(self.test_data.get("case_qap001_shisetsu_code2"))
        
        # 151 実行指示　画面: 申請種別、入所状態区分、認定区分、並び順、再発行区分、郵便区内特別有無、　を選択
        # Assert: パラメータ化
        # 申請種別
        self.find_element_by_id("tab01_ZEAF002200_Paratest17_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest17_select").send_keys(self.test_data.get("case_qap001_shinsei_shubetsu"))
        # 入所状態区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest184_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest184_select").send_keys(self.test_data.get("case_qap001_nyuusho_joutai_kubun1"))
        # 認定区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest09_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest09_select").send_keys(self.test_data.get("case_qap001_nintei_kubun"))
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabijun2"))
        # 再発行区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").send_keys(self.test_data.get("case_qap001_saihakkou_kubun"))
        # 郵便区内特別有無
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").send_keys(self.test_data.get("case_qap001_yuubin_kunai_tokubetsu_umu1"))
        
        # 152 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_93" , True)
        
        # 153 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 154 実行結果管理　画面: 表示
        # Assert: 「（QP1BN03000）支給認定変更決定データ抽出」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_95" , True)
        
        # 155 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 156 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_97" , True)
        
        # 157 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()
        
        # 158 ファイルダウンロード: 「1」ボタン押下
        # 159 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 160 ログ: 表示
        # 161 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_101" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 162 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()

        # 163 納品物管理　画面: QAPP111300_支給認定変更決定一覧.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()
        
        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：３号→２号切替処理 
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_3 = self.test_data.get("ShoriNM_3")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_3)

        # 164 ファイルダウンロード: 「1」ボタン押下
        # 165 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 166 帳票（PDF）: 表示
        # 167 帳票（PDF）: 閉じる
        self.pdf_download("QAPP111300_支給認定変更決定一覧.pdf", "帳票（PDF）_107")
        self.click_button_by_label("閉じる")
        time.sleep(2)       
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

if __name__ == "__main__":
    unittest.main()     
