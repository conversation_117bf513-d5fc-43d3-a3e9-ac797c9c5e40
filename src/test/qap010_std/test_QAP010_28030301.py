
import datetime
import time
from base.kodomo_case import KodomoSiteTestCaseBase
from selenium.webdriver.support.ui import Select

class TestQAP010_28030301(KodomoSiteTestCaseBase):
    """TestQAP010_28030301"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params_list = case_data.get("sql_params")
        self.exec_sqlfile("test_QAP010_28030301_実施前スクリプト.sql", params=sql_params_list)
        super().setUp()
    
    # 利用者負担額決定に関わる情報の入力ができることを確認する。
    def test_QAP010_28030301(self):
        """利用情報入力"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        #システム日付を変数に設定
        date = datetime.date.today()
        sys_date = format(date, '%Y%m%d')

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")

        # 3 メインメニュー画面: 「子ども子育て支援」ボタン押下
        # 4 メインメニュー画面: 「入所管理」ボタン押下       
        # 5 メインメニュー画面: 「児童検索」ボタン押下
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        
        # 6 検索条件入力画面: 表示
        self.screen_shot("検索条件入力画面_6")
        
        # 7 検索条件入力画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("AtenaCD"))
        
        # 8 検索条件入力画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        self.click_button_by_label("世帯台帳")
        self.wait_page_loaded()
        
        # 9 世帯台帳画面: 表示
        self.screen_shot("世帯台帳画面_9")
        
        # 10 世帯台帳画面: 「認定情報」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF100300_ninteijoho_li']/a/span").click()
        
        # 11 世帯台帳画面: 表示
        self.screen_shot("世帯台帳画面_11")
        
        # 12 世帯台帳画面: 「保護者認定」ボタン押下
        self.click_button_by_label("保護者認定")
        self.wait_page_loaded()
        
        # 13 保護者認定画面: 表示
        self.screen_shot("保護者認定画面_13")
        
        # 14 保護者認定画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        self.screen_shot("保護者認定画面_14")
        
        # 15 保護者認定画面: 以下を修正保護者１氏名：（保護者２の世帯員）保護者２氏名：空白
        self.find_element_by_id(u"tab01_QAPF100800_selNinteiINFOInputHogosha1ShimeiSentaku_select").click()
        Select(self.find_element_by_id(u"tab01_QAPF100800_selNinteiINFOInputHogosha1ShimeiSentaku_select")).select_by_visible_text(case_data.get("保護者１氏名_変更後"))
        self.find_element_by_id(u"tab01_QAPF100800_selNinteiINFOInputHogosha2ShimeiSentaku_select").click()
        Select(self.find_element_by_id(u"tab01_QAPF100800_selNinteiINFOInputHogosha2ShimeiSentaku_select")).select_by_visible_text(case_data.get("保護者２氏名_変更後"))
        self.screen_shot("保護者認定画面_15")
        time.sleep(1)
        
        # 16 保護者認定画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(2)
        
        # 17 保護者認定画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        
        # 18 保護者認定画面: 登録確認
        # Assert: 更新しました。を確認する。
        self.screen_shot("保護者認定画面_18")
        self.assert_message_area("tab01_QAPF100800_msg_span","更新しました。")
        
        # 19 保護者認定画面: パンくずリスト「世帯台帳」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF100800_navi']/li[3]/a").click()
        
        # 20 世帯台帳画面: 表示
        self.screen_shot("世帯台帳画面_20")
        
        # 21 世帯台帳画面: 「納義務者認定」ボタン押下
        self.click_button_by_label("納義務者認定")
        self.wait_page_loaded()
        
        # 22 納付義務者認定画面: 表示
        self.screen_shot("納付義務者認定画面_22")
        
        # 23 納付義務者認定画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        self.screen_shot("納付義務者認定画面_23")
        
        # 24 納付義務者認定画面: 以下を修正納付義務者１氏名：（納付義務者２の世帯員）納付義務者２氏名：空白
        self.find_element_by_id(u"tab01_QAPF100900_selNinteiINFOInputNofuGimusha1ShimeiSentaku_select").click()
        Select(self.find_element_by_id(u"tab01_QAPF100900_selNinteiINFOInputNofuGimusha1ShimeiSentaku_select")).select_by_visible_text(case_data.get("納義務者１氏名_変更後"))
        self.find_element_by_id(u"tab01_QAPF100900_selNinteiINFOInputNofuGimusha2ShimeiSentaku_select").click()
        Select(self.find_element_by_id(u"tab01_QAPF100900_selNinteiINFOInputNofuGimusha2ShimeiSentaku_select")).select_by_visible_text(case_data.get("納義務者２氏名_変更後"))
        self.screen_shot("納付義務者認定画面_24")
        time.sleep(1)

        # 25 納付義務者認定画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(2)
        
        # 26 納付義務者認定画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        
        # 27 納付義務者認定画面: 登録確認
        # Assert: 更新しました。を確認する。
        self.screen_shot("納付義務者認定画面_27")
        self.assert_message_area("tab01_QAPF100900_msg_span","更新しました。")
        
        # 28 納付義務者認定画面: パンくずリスト「世帯台帳」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF100900_navi']/li[3]/a").click()
        
        # 29 世帯台帳画面: 表示
        self.screen_shot("世帯台帳画面_29")
        
        # 30 世帯台帳画面: 「課税認定」ボタン押下
        self.click_button_by_label("課税認定")
        self.wait_page_loaded()
        
        # 31 課税認定画面: 表示
        self.screen_shot("課税認定画面_31")
        
        # 32 課税認定画面: 以下を入力年度：
        self.find_element_by_id(u"tab01_QAPF101000_selKensakuJokenNendo_select").click()
        Select(self.find_element_by_id(u"tab01_QAPF101000_selKensakuJokenNendo_select")).select_by_visible_text(case_data.get("年度"))
        
        # 33 課税認定画面: 「検索」ボタン押下
        self.click_button_by_label("検索")
        
        # 34 課税認定画面: 「追加」ボタン押下
        self.click_button_by_label("追加")
        self.wait_page_loaded()
        
        self.find_element_by_id(u"tab01_ZZZ000000_txtKazeiNinteiINFOInputNinteiYMStart_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtKazeiNinteiINFOInputNinteiYMStart_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtKazeiNinteiINFOInputNinteiYMStart_textboxInput").send_keys(case_data.get("認定開始年月"))

        # 35 課税認定画面: 合算区分：入力区分：
        self.find_element_by_id(u"tab01_ZZZ000000_selZeiINFOGassanKbn_1_4_select").click()
        Select(self.find_element_by_id(u"tab01_ZZZ000000_selZeiINFOGassanKbn_1_4_select")).select_by_visible_text(case_data.get("合算区分"))
        self.find_element_by_id(u"tab01_ZZZ000000_selZeiINFOInputKbn_1_9_select").click()
        Select(self.find_element_by_id(u"tab01_ZZZ000000_selZeiINFOInputKbn_1_9_select")).select_by_visible_text(case_data.get("入力区分"))
        self.find_element_by_id(u"tab01_ZZZ000000_selZeiINFOGassanKbn_2_4_select").click()
        Select(self.find_element_by_id(u"tab01_ZZZ000000_selZeiINFOGassanKbn_2_4_select")).select_by_visible_text(case_data.get("合算区分"))
        self.find_element_by_id(u"tab01_ZZZ000000_selZeiINFOInputKbn_2_9_select").click()
        Select(self.find_element_by_id(u"tab01_ZZZ000000_selZeiINFOInputKbn_2_9_select")).select_by_visible_text(case_data.get("入力区分"))

        # 課税認定画面: 「合算値代入」ボタン押下
        self.click_button_by_label("合算値代入")

        # 36 課税認定画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.wait_page_loaded()
        
        # 37 課税認定画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        
        # 38 課税認定画面: 登録確認
        # Assert: 登録しました。を確認する。
        self.screen_shot("課税認定画面_38")
        self.assert_message_area("tab01_QAPF101000_msg_span","登録しました。")
        
        # 39 課税認定画面: パンくずリスト「世帯台帳」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF101000_navi']/li[3]/a").click()
        
        # 40 世帯台帳画面: 表示
        self.screen_shot("世帯台帳画面_40") 
        
        # 41 世帯台帳画面: 「児童一覧」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF100300_jidoiciran_li']/a/span").click()
        
        # 42 世帯台帳画面: 表示
        self.screen_shot("世帯台帳画面_42")
        
        # 43 世帯台帳画面: No「１」ボタンを押下
        self.click_button_by_label("1")
        self.wait_page_loaded()
        
        # 44 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_44")
        
        # 45 児童台帳画面: 「入所管理」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 46 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_46")
        
        # 47 児童台帳画面: 「契約情報」ボタン押下
        self.click_button_by_label("契約情報")
        self.wait_page_loaded()
        
        # 48 契約情報画面: 表示
        self.screen_shot("契約情報画面_48")
        
        # 49 契約情報画面: 「追加」ボタン押下
        #self.click_button_by_label("追加")
        #self.wait_page_loaded()
        
        # 50 契約情報画面: 以下を入力届出年月日：（システム日付）契約年月日：（システム日付）契約期間：認定区分：保育必要量
        #self.find_element_by_id(u"tab01_QAPF103800_txtTodokedeYMD_textboxInput").click()
        #self.find_element_by_id(u"tab01_QAPF103800_txtTodokedeYMD_textboxInput").clear()
        #self.find_element_by_id(u"tab01_QAPF103800_txtTodokedeYMD_textboxInput").send_keys(sys_date)
        #self.find_element_by_id(u"tab01_QAPF103800_txtKeiyakuYMD_textboxInput").click()
        #self.find_element_by_id(u"tab01_QAPF103800_txtKeiyakuYMD_textboxInput").clear()
        #self.find_element_by_id(u"tab01_QAPF103800_txtKeiyakuYMD_textboxInput").send_keys(sys_date)
        #self.find_element_by_id(u"tab01_QAPF103800_txtRiyoKikanStart_textboxInput").click()
        #self.find_element_by_id(u"tab01_QAPF103800_txtRiyoKikanStart_textboxInput").clear()
        #self.find_element_by_id(u"tab01_QAPF103800_txtRiyoKikanStart_textboxInput").send_keys(case_data.get("契約期間開始"))
        #self.find_element_by_id(u"tab01_QAPF103800_txtRiyoKikanEnd_textboxInput").click()
        #self.find_element_by_id(u"tab01_QAPF103800_txtRiyoKikanEnd_textboxInput").clear()
        #self.find_element_by_id(u"tab01_QAPF103800_txtRiyoKikanEnd_textboxInput").send_keys(case_data.get("契約期間終了"))
        #self.find_element_by_id(u"tab01_QAPF103800_selKeiyakuNinteiKbn_select").click()
        #Select(self.find_element_by_id(u"tab01_QAPF103800_selKeiyakuNinteiKbn_select")).select_by_visible_text(case_data.get("認定区分"))
        #self.find_element_by_id(u"tab01_QAPF103800_selKeiyakuHoikuHitsuyoryo_select").click()
        #Select(self.find_element_by_id(u"tab01_QAPF103800_selKeiyakuHoikuHitsuyoryo_select")).select_by_visible_text(case_data.get("保育必要量"))
        
        # 51 契約情報画面: 「登録」ボタン押下
        #self.click_button_by_label("登録")
        #self.wait_page_loaded()
        
        # 52 契約情報画面: 「はい」ボタン押下
        #self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()

        # 53 契約情報画面: 登録確認
        # Assert: 登録しました。を確認する。
        #self.screen_shot("契約情報画面_53")
        #self.assert_message_area("tab01_QAPF103800_msg_span","登録しました。")
        
        # 54 契約情報画面: パンくずリスト「児童台帳」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF103800_navi']/li[4]/a").click()
        
        # 55 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_55")
        
        # 児童検索画面を閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # # 57 メインメニュー画面: 表示
        # self.screen_shot("メインメニュー画面_57")
        
        # # 58 メインメニュー画面: 「バッチ管理」ボタン押下       
        # # 59 メインメニュー画面: 「即時実行」ボタン押下    
        # # 60 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        # self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # # 61 スケジュール個別追加画面: 表示
        # self.screen_shot("スケジュール個別追加画面_61")
        
        # # 62 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：請求審査処理名：契約情報一括作成処理
        # # 63 スケジュール個別追加画面: 「検索」ボタン押下
        # gyomuNM = case_data.get("GyomuNM")
        # subSystemNM = case_data.get("SubSystemNM")
        # shoriNM = case_data.get("ShoriNM")
        # self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM)
        
        # # 64 スケジュール個別追加画面: 「No.1」ボタン押下
        # self.click_button_by_label("1")
        # self.wait_page_loaded()
        
        # # 65 実行指示画面: 表示
        # self.screen_shot("実行指示画面_65")
        
        # # 66 実行指示画面: 以下を入力対象年月日開始：対象年月日終了：対象生年月日開始：対象生年月日終了：
        # # Assert: パラメータ化
        # params = [
        #     {"title":"対象年月日開始", "type": "text", "value": case_data.get("一括抽出_対象年月日開始")},
        #     {"title":"対象年月日終了", "type": "text", "value": case_data.get("一括抽出_対象年月日終了")},
        #     {"title":"対象生年月日開始日", "type": "text", "value": case_data.get("一括抽出_対象生年月日開始日")},
        #     {"title":"対象生年月日終了日", "type": "text", "value": case_data.get("一括抽出_対象生年月日終了日")}
        # ]
        # self.set_job_param_kodomo(params)
        # #self.driver.fullscreen_window()
        # self.driver.set_window_size(1450,2000)
        # # 67 実行指示画面: 「実行」ボタン押下
        # self.click_button_by_label("実行")
        
        # 68 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        # self.screen_shot("実行結果管理画面_68")
        
        # 69 実行結果管理画面: 「No.1」ボタン押下
        # 70 結果確認画面: 表示
        # self.screen_shot("結果確認画面_70")
        # 71 結果確認画面: 「納品物確認」ボタン押下
        # 72 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_72")
        # 73 納品物管理画面: 「ダウンロード」ボタンを押下
        # 74 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_74")
        # 75 ファイルダウンロード画面: 「No.1」ボタン押下
        # 76 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 77 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_77")
        # 78 PDF: ×ボタン押下で閉じる

        # # メインメニュー画面: 「バッチ管理」ボタン押下
        # # メインメニュー画面: 「結果管理」ボタン押下
        # # メインメニュー画面: 「納品物確認」ボタン押下
        # self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：請求審査処理名：契約情報一括作成処理
        # # 納品物確認画面: 「検索」ボタン押下
        # self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM)
        
        # # 納品物確認画面: 「ダウンロード」ボタン押下
        # self.pdf_download("QAPP170200_契約情報一括作成対象者一覧.pdf", "ファイルダウンロード画面_75")
        # self.click_button_by_label("閉じる")

        # # 納品物確認タブを閉じる
        # self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # # 79 ファイルダウンロード画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        # self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        # #self.driver.fullscreen_window()
        # self.driver.set_window_size(1450,2000)
        # # 80 スケジュール個別追加画面: 「No.3」ボタン押下
        # self.click_button_by_label("3")
        # self.wait_page_loaded()

        # # 81 実行指示画面: 表示
        # self.screen_shot("実行指示画面_81")
        
        # # 82 実行指示画面: 以下を入力契約新規更新：更新する退所更新：更新する３号⇒２号更新：更新する支給認定差異更新：更新する
        # params = [
        #     {"title":"契約新規更新", "type": "select", "value": case_data.get("契約新規更新")},
        #     {"title":"退所更新", "type": "select", "value": case_data.get("退所更新")},
        #     {"title":"３号⇒２号更新", "type": "select", "value": case_data.get("３号⇒２号更新")},
        #     {"title":"支給認定差異更新", "type": "select", "value": case_data.get("支給認定差異更新")}
        # ]
        # self.set_job_param_kodomo(params)
        # #self.driver.fullscreen_window()
        # self.driver.set_window_size(1450,2000)
        # # 83 実行指示画面: 「実行」ボタン押下
        # self.click_button_by_label("実行")
        # time.sleep(2)

        # # 84 実行結果管理画面: 表示
        # # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        # self.screen_shot("実行結果管理画面_84")
        
        # # スケジュール個別タブを閉じる
        # self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 85 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_85")
        
        # 86 メインメニュー画面: 「子ども子育て支援」ボタン押下        
        # 87 メインメニュー画面: 「入所管理」ボタン押下       
        # 88 メインメニュー画面: 「児童検索」ボタン押下
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        
        # 89 児童検索画面: 表示
        self.screen_shot("児童検索画面_89")
        
        # 90 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("宛名コード_契約新規"))
        
        # 91 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()

        # 92 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_92")
        
        # 93 児童台帳画面: 「入所管理」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 94 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_94")
        
        # 95 児童台帳画面: 「契約情報」ボタン押下
        self.click_button_by_label("契約情報") 
        self.wait_page_loaded()
        
        # 96 契約情報画面: 表示
        # Assert: バッチ処理で作成した契約情報が登録されていること。
        self.screen_shot("契約情報画面_96")
        
        # 97 契約情報画面: パンくずリスト「児童検索」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF103800_navi']/li[1]/a").click()
        
        # 98 児童検索画面: 表示
        self.screen_shot("児童検索画面_98")
        
        # 99 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("宛名コード_退所"))
        
        # 100 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        
        # 101 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_101")
        
        # 102 児童台帳画面: 「入所管理」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 103 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_103")
        
        # 104 児童台帳画面: 「契約情報」ボタン押下
        self.click_button_by_label("契約情報")
        self.wait_page_loaded()
        
        # 105 契約情報画面: 表示
        # Assert: バッチ処理で作成した契約情報が登録されていること。
        self.screen_shot("契約情報画面_105")
        
        # 106 契約情報画面: パンくずリスト「児童検索」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF103800_navi']/li[1]/a").click()
        
        # 107 児童検索画面: 表示
        self.screen_shot("児童検索画面_107")
        
        # 108 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("宛名コード_３号⇒２号"))
        
        # 109 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        
        # 110 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_110")
        
        # 111 児童台帳画面: 「入所管理」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 112 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_112")
        
        # 113 児童台帳画面: 「契約情報」ボタン押下
        self.click_button_by_label("契約情報")
        self.wait_page_loaded()
        
        # 114 契約情報画面: 表示
        # Assert: バッチ処理で作成した契約情報が登録されていること。
        self.screen_shot("契約情報画面_114")
        
        # 115 契約情報画面: パンくずリスト「児童検索」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF103800_navi']/li[1]/a").click()
        
        # 116 児童検索画面: 表示
        self.screen_shot("児童検索画面_116")
        
        # 117 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("宛名コード_支給認定差異"))
        
        # 118 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        
        # 119 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_119")
        
        # 120 児童台帳画面: 「入所管理」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 121 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_121")
        
        # 122 児童台帳画面: 「契約情報」ボタン押下
        self.click_button_by_label("契約情報")
        self.wait_page_loaded()
        
        # 123 契約情報画面: 表示
        # Assert: バッチ処理で作成した契約情報が登録されていること。
        self.screen_shot("契約情報画面_123")
        
        # 児童検索タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 125 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_125")
        
        # 126 メインメニュー画面: 「バッチ管理」ボタン押下       
        # 127 メインメニュー画面: 「即時実行」ボタン押下
        # 128 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # 129 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_129")
        
        # 130 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：請求審査処理名：契約情報一括作成処理
        # 131 スケジュール個別追加画面: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM = case_data.get("ShoriNM")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM)
        
        # 132 スケジュール個別追加画面: 「No.1」ボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()
        
        # 133 実行指示画面: 表示
        self.screen_shot("実行指示画面_133")
        
        # 134 実行指示画面: 以下を入力対象年月日開始：対象年月日終了：対象生年月日開始：対象生年月日終了：
        # Assert: パラメータ化
        params = [
            {"title":"対象年月日開始", "type": "text", "value": case_data.get("契約新規_対象年月日開始")},
            {"title":"対象年月日終了", "type": "text", "value": case_data.get("契約新規_対象年月日終了")},
            {"title":"対象生年月日開始日", "type": "text", "value": case_data.get("契約新規_対象生年月日開始日")},
            {"title":"対象生年月日終了日", "type": "text", "value": case_data.get("契約新規_対象生年月日終了日")}
        ]
        self.set_job_param_kodomo(params)
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 135 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 136 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_136")
        
        # 137 実行結果管理画面: 「No.1」ボタン押下
        # 138 結果確認画面: 表示
        # self.screen_shot("結果確認画面_138")
        # 139 結果確認画面: 「納品物確認」ボタン押下
        # 140 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_140")
        # 141 納品物管理画面: 「ダウンロード」ボタンを押下
        # 142 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_142")
        # 143 ファイルダウンロード画面: 「No.1」ボタン押下
        # 144 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 145 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_145")
        # 146 PDF: ×ボタン押下で閉じる

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：請求審査処理名：契約情報一括作成処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM)
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP170210_契約情報一括作成対象者一覧(契約新規).pdf", "ファイルダウンロード画面_142")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 147 ファイルダウンロード画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        time.sleep(2)
        # 148 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()
        
        # 149 実行指示画面: 表示
        self.screen_shot("実行指示画面_149")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 150 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 151 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_151")
        
        # スケジュール個別タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 152 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_152")
        
        # 153 メインメニュー画面: 「子ども子育て支援」ボタン押下      
        # 154 メインメニュー画面: 「入所管理」ボタン押下      
        # 155 メインメニュー画面: 「児童検索」ボタン押下
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        
        # 156 児童検索画面: 表示
        self.screen_shot("児童検索画面_156")
        
        # 157 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("宛名コード_契約新規"))
        
        # 158 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        
        # 159 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_159")
        
        # 160 児童台帳画面: 「入所管理」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 161 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_161")
        
        # 162 児童台帳画面: 「契約情報」ボタン押下
        self.click_button_by_label("契約情報")
        self.wait_page_loaded()
        
        # 163 契約情報画面: 表示
        # Assert: バッチ処理で作成した契約情報が登録されていること。
        self.screen_shot("契約情報画面_163")
        
        # 児童検索画面を閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 165 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_165")
        
        # 166 メインメニュー画面: 「バッチ管理」ボタン押下
        # 167 メインメニュー画面: 「即時実行」ボタン押下
        # 168 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # 169 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_169")
        
        # 170 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：請求審査処理名：契約情報一括作成処理
        # 171 スケジュール個別追加画面: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM = case_data.get("ShoriNM")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM)  
        
        # 172 スケジュール個別追加画面: 「No.3」ボタン押下
        self.click_button_by_label("3")
        self.wait_page_loaded()
        
        # 173 実行指示画面: 表示
        self.screen_shot("実行指示画面_173")
        
        # 174 実行指示画面: 以下を入力対象年月日開始：対象年月日終了：対象生年月日開始：対象生年月日終了：
        # Assert: パラメータ化
        params = [
            {"title":"対象年月日開始", "type": "text", "value": case_data.get("退所_対象年月日開始")},
            {"title":"対象年月日終了", "type": "text", "value": case_data.get("退所_対象年月日終了")},
            {"title":"対象生年月日開始日", "type": "text", "value": case_data.get("退所_対象生年月日開始日")},
            {"title":"対象生年月日終了日", "type": "text", "value": case_data.get("退所_対象生年月日終了日")}
        ]
        self.set_job_param_kodomo(params)
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 175 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 176 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_176")
        
        # 177 実行結果管理画面: 「No.1」ボタン押下
        # 178 結果確認画面: 表示
        # self.screen_shot("結果確認画面_178")
        # 179 結果確認画面: 「納品物確認」ボタン押下
        # 180 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_180")
        # 181 納品物管理画面: 「ダウンロード」ボタンを押下
        # 182 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_182")
        # 183 ファイルダウンロード画面: 「No.1」ボタン押下
        # 184 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 185 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_185")
        # 186 PDF: ×ボタン押下で閉じる

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：請求審査処理名：契約情報一括作成処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM)
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP170220_契約情報一括作成対象者一覧(退所).pdf", "ファイルダウンロード画面_182")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 187 ファイルダウンロード画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        self.wait_page_loaded()
        # 188 スケジュール個別追加画面: 「No.4」ボタン押下
        self.click_button_by_label("4")
        self.wait_page_loaded()
        
        # 189 実行指示画面: 表示
        self.screen_shot("実行指示画面_189")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 190 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 191 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_191")
        
        # スケジュール個別タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 192 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_192")
        
        # 193 メインメニュー画面: 「子ども子育て支援」ボタン押下
        # 194 メインメニュー画面: 「入所管理」ボタン押下
        # 195 メインメニュー画面: 「児童検索」ボタン押下
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        
        # 196 児童検索画面: 表示
        self.screen_shot("児童検索画面_196")
        
        # 197 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("宛名コード_退所"))
        
        # 198 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        
        # 199 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_199")
        
        # 200 児童台帳画面: 「入所管理」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 201 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_201")
        
        # 202 児童台帳画面: 「契約情報」ボタン押下
        self.click_button_by_label("契約情報")
        self.wait_page_loaded()
        
        # 203 契約情報画面: 表示
        # Assert: バッチ処理で作成した契約情報が登録されていること。
        self.screen_shot("契約情報画面_203")
        
        # 児童検索画面を閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 205 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_205")

        # 206 メインメニュー画面: 「バッチ管理」ボタン押下
        # 207 メインメニュー画面: 「即時実行」ボタン押下
        # 208 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # 209 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_209")
        
        # 210 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：請求審査処理名：契約情報一括作成処理
        # 211 スケジュール個別追加画面: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM = case_data.get("ShoriNM")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM)
        
        # 212 スケジュール個別追加画面: 「No.5」ボタン押下
        self.click_button_by_label("5")
        self.wait_page_loaded()
        
        # 213 実行指示画面: 表示
        self.screen_shot("実行指示画面_213")
        
        # 214 実行指示画面: 以下を入力対象年月日開始：対象年月日終了：対象生年月日開始：対象生年月日終了：
        # Assert: パラメータ化
        params = [
            {"title":"対象年月日開始", "type": "text", "value": case_data.get("３号⇒２号_対象年月日開始")},
            {"title":"対象年月日終了", "type": "text", "value": case_data.get("３号⇒２号_対象年月日終了")},
            {"title":"対象生年月日開始日", "type": "text", "value": case_data.get("３号⇒２号_対象生年月日開始日")},
            {"title":"対象生年月日終了日", "type": "text", "value": case_data.get("３号⇒２号_対象生年月日終了日")}
        ]
        self.set_job_param_kodomo(params)
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 215 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 216 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_216")
        
        # 217 実行結果管理画面: 「No.1」ボタン押下
        # 218 結果確認画面: 表示
        # self.screen_shot("結果確認画面_218")
        # 219 結果確認画面: 「納品物確認」ボタン押下
        # 220 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_220")
        # 221 納品物管理画面: 「ダウンロード」ボタンを押下
        # 222 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_222")
        # 223 ファイルダウンロード画面: 「No.1」ボタン押下
        # 224 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 225 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_225")
        # 226 PDF: ×ボタン押下で閉じる

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：請求審査処理名：契約情報一括作成処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM)
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP170230_契約情報一括作成対象者一覧(３号⇒２号).pdf", "ファイルダウンロード画面_222")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # 227 ファイルダウンロード画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        time.sleep(2)
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 228 スケジュール個別追加画面: 「No.6」ボタン押下
        self.click_button_by_label("6")
        self.wait_page_loaded()
        
        # 229 実行指示画面: 表示
        self.screen_shot("実行指示画面_229")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 230 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 231 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_231")
        
        # スケジュール個別タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 232 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_232")
        
        # 233 メインメニュー画面: 「子ども子育て支援」ボタン押下
        # 234 メインメニュー画面: 「入所管理」ボタン押下
        # 235 メインメニュー画面: 「児童検索」ボタン押下
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        
        # 236 児童検索画面: 表示
        self.screen_shot("児童検索画面_236")
        
        # 237 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("宛名コード_３号⇒２号"))
        
        # 238 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        
        # 239 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_239")
        
        # 240 児童台帳画面: 「入所管理」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 241 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_241")
        
        # 242 児童台帳画面: 「契約情報」ボタン押下
        self.click_button_by_label("契約情報")
        self.wait_page_loaded()
        
        # 243 契約情報画面: 表示
        # Assert: バッチ処理で作成した契約情報が登録されていること。
        self.screen_shot("契約情報画面_243")
        
        # 児童検索画面を閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 245 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_245")

        # 246 メインメニュー画面: 「バッチ管理」ボタン押下
        # 247 メインメニュー画面: 「即時実行」ボタン押下
        # 248 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # 249 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_249")
        
        # 250 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：請求審査処理名：契約情報一括作成処理
        # 251 スケジュール個別追加画面: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM = case_data.get("ShoriNM")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM)
        
        # 252 スケジュール個別追加画面: 「No.7」ボタン押下
        self.click_button_by_label("7")
        self.wait_page_loaded()
        
        # 253 実行指示画面: 表示
        self.screen_shot("実行指示画面_253")
        
        # 254 実行指示画面: 以下を入力対象年月日開始：対象年月日終了：対象生年月日開始：対象生年月日終了：
        # Assert: パラメータ化
        params = [
            {"title":"対象年月日開始", "type": "text", "value": case_data.get("支給認定差異_対象年月日開始")},
            {"title":"対象年月日終了", "type": "text", "value": case_data.get("支給認定差異_対象年月日終了")},
            {"title":"対象生年月日開始日", "type": "text", "value": case_data.get("支給認定差異_対象生年月日開始日")},
            {"title":"対象生年月日終了日", "type": "text", "value": case_data.get("支給認定差異_対象生年月日終了日")}
        ]
        self.set_job_param_kodomo(params)
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 255 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 256 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_256")
        
        # 257 実行結果管理画面: 「No.1」ボタン押下
        # 258 結果確認画面: 表示
        # self.screen_shot("結果確認画面_258")
        # 259 結果確認画面: 「納品物確認」ボタン押下
        # 260 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_260")
        # 261 納品物管理画面: 「ダウンロード」ボタンを押下
        # 262 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_262")
        # 263 ファイルダウンロード画面: 「No.1」ボタン押下
        # 264 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 265 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_265")
        # 266 PDF: ×ボタン押下で閉じる

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：請求審査処理名：契約情報一括作成処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM)
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP170240_契約情報一括作成対象者一覧(支給認定差異).pdf", "ファイルダウンロード画面_262")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 267 ファイルダウンロード画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 268 スケジュール個別追加画面: 「No.8」ボタン押下
        self.click_button_by_label("8")
        self.wait_page_loaded()
        
        # 269 実行指示画面: 表示
        self.screen_shot("実行指示画面_269")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 270 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 271 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_271")
        
        # スケジュール個別タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 272 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_272")
        
        # 273 メインメニュー画面: 「子ども子育て支援」ボタン押下
        # 274 メインメニュー画面: 「入所管理」ボタン押下
        # 275 メインメニュー画面: 「児童検索」ボタン押下
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        
        # 276 児童検索画面: 表示
        self.screen_shot("児童検索画面_276")
        
        # 277 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("宛名コード_支給認定差異"))
        
        # 278 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        
        # 279 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_279")
        
        # 280 児童台帳画面: 「入所管理」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 281 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_281")
        
        # 282 児童台帳画面: 「契約情報」ボタン押下
        self.click_button_by_label("契約情報")
        self.wait_page_loaded()
        
        # 283 契約情報画面: 表示
        # Assert: バッチ処理で作成した契約情報が登録されていること。
        self.screen_shot("契約情報画面_283")

    def tearDown(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params_list = case_data.get("sql_params")
        self.exec_sqlfile("test_QAP010_28030301_実施後スクリプト.sql", params=sql_params_list)
        super().tearDown()

        
