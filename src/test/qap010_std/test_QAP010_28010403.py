import time
import unittest
from datetime import datetime, date, timedelta
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.select import Select
class TestQAP010_28010403(KodomoSiteTestCaseBase):
    """TestQAP010_28010403"""

    def setUp(self):
        settings = self.common_test_data
        self.test_data = settings.get("test_qap010_28010403")
        atena_list = settings.get("test_qap010_28010403")
        self.exec_sqlfile("QAP010_28010403_削除スクリプト.sql", params=atena_list)
        super().setUp()
    
    # 提出された現況届の変更情報について登録内容の確認が行えることを確認する。
    def test_QAP010_28010403(self):
        """現況届変更情報確定"""
        
        driver = None

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.save_screenshot_migrate(driver, "メインメニュー 画面_1" , True)
        
        # 2 メインメニュー画面: 「子ども子育て支援」ボタン押下
        self._goto_kodomo()

        # 3 メインメニュー画面: 「入所管理」ボタン押下
        self._goto_nyuusyokannri()

        # 4 メインメニュー画面: 「児童検索」ボタン押下
        self._goto_jidoukennsaku()

        # 5 メインメニュー画面: 宛名コード：　　　　　　　　　を入力
        # Assert: パラメータ化
        # 宛名コード
        self.find_element_by_id("tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id("tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(self.test_data.get("case_qap001_atena_code"))

        # 6 児童台帳画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_QAPF105800_WrCmnBtn05_button").click()
        
        # 7 支給認定情報画面: 支給認定登録・履歴「№.1」ボタンを押下
        self.find_element_by_id("tab01_ZZZ000000_btnShikyuNinteiTorokuRirekiNo_1_1_button").click()
        
        # 8 支給認定情報画面: 申請内容「提出書類管理」ボタンを押下
        self.save_screenshot_migrate(driver, "支給認定情報画面_8" , True)
        self.find_element_by_id("tab01_QAPF103600_btnTeishutsuShoruiKanri_button").click()
        
        # 提出管理画面: 「追加」ボタンを押下
        self.find_element_by_id("tab01_QAZF002500_btnAddChg_button").click()
        # 提出管理画面: 状態区分：　　　　　　　を選択
        self.find_element_by_id("tab01_ZZZ000000_chkTeishutsuShoruiINFOMiteishutsuY1_1_1chk0").click()
        self.find_element_by_id("tab01_QAZF002500_checkbtn_button").click()
        self.find_element_by_id("tab01_QAZF002500_regbtn_button").click()
        self.find_element_by_id("tempId__1").click()
        time.sleep(2)
        
        # 9 提出管理画面: 「修正」ボタンを押下
        self.find_element_by_id("tab01_QAZF002500_btnEditChg_button").click()
        
        # 10 提出管理画面: 状態区分：　　　　　　　を選択
        self.find_element_by_id("tab01_ZZZ000000_chkTeishutsuShoruiINFOMiteishutsuY1_1_1chk0").click()
        self.find_element_by_id("tab01_ZZZ000000_chkTeishutsuShoruiINFOMiteishutsuY1_2_1chk0").click()
        self.find_element_by_id("tab01_ZZZ000000_txtTeishutsuShoruiINFOTeishutsuDay1_2_3_textboxInput").click()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZZZ000000_txtTeishutsuShoruiINFOTeishutsuDay1_2_3_textboxInput").send_keys(formatted_date)

        # 11 提出管理画面: 「登録」ボタンを押下
        self.find_element_by_id("tab01_QAZF002500_checkbtn_button").click()
        self.find_element_by_id("tab01_QAZF002500_regbtn_button").click()
        self.find_element_by_id("tempId__3").click()
        self.save_screenshot_migrate(driver, "提出管理画面_11" , True)
