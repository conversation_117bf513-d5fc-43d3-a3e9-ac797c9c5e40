import time
import unittest
from datetime import datetime, date, timedelta
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.select import Select
class TestQAP010_28010301(KodomoSiteTestCaseBase):
    """TestQAP010_28010301"""

    def setUp(self):
        settings = self.common_test_data
        self.test_data = settings.get("test_qap010_28010301")
        atena_list = settings.get("test_qap010_28010301")
        self.exec_sqlfile("QAP010_28010301_更新スクリプト.sql", params=atena_list)
        super().setUp()
    
    # 教育・保育給付認定取消情報の登録が行えることを確認する。
    def test_QAP010_28010301(self):
        """取消情報登録"""
        
        driver = None

        self.do_login()
        # 1 メインメニュー　画面: 表示
        self.save_screenshot_migrate(driver, "メインメニュー 画面_1" , True)
        
        # 2 メインメニュー　画面: 「子ども子育て支援」ボタン押下
        self._goto_kodomo()
        
        # 3 メインメニュー　画面: 「世帯情報」ボタン押下
        self._goto_setaijyouhou()

        # 4 メインメニュー　画面: 「検索」ボタンをダブルクリック
        self._goto_kennsaku()

        # 5 検索条件入力　画面: 表示
        self.save_screenshot_migrate(driver, "検索条件入力　画面_5" , True)
        
        # 6 検索条件入力　画面: カナ氏名：<パラメータ>、生年月日＜パラメータ＞入力
        # Assert: パラメータ化
        self.find_element_by_id("tab01_QAZF100001_txtKanaShimeiNM_textboxInput").click()
        self.find_element_by_id("tab01_QAZF100001_txtKanaShimeiNM_textboxInput").clear()
        self.find_element_by_id("tab01_QAZF100001_txtKanaShimeiNM_textboxInput").send_keys(self.test_data.get("case_qap001_kana_shimei"))
        self.find_element_by_id("tab01_QAZF100001_txtDate01Left_textboxInput").click()
        self.find_element_by_id("tab01_QAZF100001_txtDate01Left_textboxInput").clear()
        self.find_element_by_id("tab01_QAZF100001_txtDate01Left_textboxInput").send_keys(self.test_data.get("case_qap001_seinengappi"))
        self.find_element_by_id("tab01_QAZF100001_cond_item_0_9_").click()
        
        # 7 検索条件入力　画面: 「検索」ボタンクリック
        self.save_screenshot_migrate(driver, "検索条件入力　画面_7" , True)
        self.find_element_by_id("tab01_QAZF100001_WrCmnBtn05_button").click()
        
        # 8 世帯履歴　画面: 表示
        self.save_screenshot_migrate(driver, "世帯履歴　画面_8" , True)
        
        # 9 世帯履歴　画面: 「1」ボタンを押下
        self.find_element_by_id("tab01_ZZZ000000_btnSetaiRirekiNo_1_1_button").click()
        
        # 10 世帯台帳　画面: 表示
        self.save_screenshot_migrate(driver, "世帯台帳　画面_10" , True)
        
        # 11 世帯台帳　画面: №「1」　ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_btnJIDoIchiranRemban_1_1_button").click()
        
        # 12 支給認定情報　画面: 表示
        self.find_element_by_id("tab01_ZZZ000000_btnShikyuNinteiTorokuRirekiNo_1_1_button").click()
        self.save_screenshot_migrate(driver, "支給認定情報　画面_12" , True)

        # 13 支給認定情報　画面: 「修正」　ボタン押下
        self.find_element_by_id("tab01_QAPF103600_btnEditChg_button").click()
        
        # 14 支給認定情報　画面: 「支給認定結果」タブ　クリック
        self.find_element_by_id("tab01_QAPF103600_sikyuninteikekka_li").click()
        
        # 15 支給認定情報　画面: 取消年月日　を入力
        # Assert: パラメータ化
        self.find_element_by_id("tab01_QAPF103600_txtNinteiKekkaTorikeshiYMD_textboxInput").click()
        self.find_element_by_id("tab01_QAPF103600_txtNinteiKekkaTorikeshiYMD_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_QAPF103600_txtNinteiKekkaTorikeshiYMD_textboxInput").send_keys(formatted_date)
        
        # 16 支給認定情報　画面: 取消理由　を選択
        # Assert: パラメータ化
        self.find_element_by_id("tab01_QAPF103600_selNinteiKekkaTorikeshiRiyu_select").click()
        self.find_element_by_id("tab01_QAPF103600_selNinteiKekkaTorikeshiRiyu_select").send_keys(self.test_data.get("case_qap001_torikeshi_riyuu"))
        self.find_element_by_id("tab01_QAPF103600_txtNinteiKekkaNinteiKikanStart_textboxInput").click()
        self.find_element_by_id("tab01_QAPF103600_txtNinteiKekkaNinteiKikanStart_textboxInput").send_keys(self.test_data.get("case_qap001_ninteikikan_kaishi"))

        # 17 支給認定情報　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_QAPF103600_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "支給認定情報　画面_17" , True)
        
        # 18 支給認定情報　画面: 「登録」ボタン押下
        self.find_element_by_id("tab01_QAPF103600_regbtn_button").click()

        # 19 支給認定情報　画面: 「はい」押下
        self.find_element_by_id("tempId__1").click()
        
        # 20 支給認定情報　画面: 表示
        self.save_screenshot_migrate(driver, "支給認定情報　画面_20" , True)

if __name__ == "__main__":
    unittest.main() 
        
