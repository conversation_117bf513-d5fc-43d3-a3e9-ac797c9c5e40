
import datetime
from base.kodomo_case import KodomoSiteTestCaseBase
from selenium.webdriver.support.ui import Select

class TestQAP010_28030403(KodomoSiteTestCaseBase):
    """TestQAP010_28030403"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params_list = case_data.get("sql_params")
        self.exec_sqlfile("test_QAP010_28030403_実施前スクリプト.sql", params=sql_params_list)
        super().setUp()
    
    # 利用者負担額（切替後）の算出ができることを確認する。
    def test_QAP010_28030403(self):
        """利用者負担額算出"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        #システム日付を変数に設定
        date = datetime.date.today()
        sys_date = format(date, '%Y%m%d')

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「子ども子育て支援」ボタン押下
        # 4 メインメニュー画面: 「入所管理」ボタン押下
        # 5 メインメニュー画面: 「児童検索」ボタン押下
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        
        # 6 児童検索画面: 表示
        self.screen_shot("児童検索画面_6")
        
        # 7 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("AtenaCD"))

        # 8 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        
        # 9 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_9")
        
        # 10 児童台帳画面: 「入所管理」タブ押下
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 11 児童台帳画面: 「減免・調整申請」ボタン押下
        self.click_button_by_label("減免・調整申請")
        self.wait_page_loaded()
        
        # 12 保育料減免・調整管理（階層その他）画面: 表示
        self.screen_shot("保育料減免・調整管理（階層その他）画面_12")
        
        # 13 保育料減免・調整管理（階層その他）画面: 「追加」ボタン押下
        self.click_button_by_label("追加")
        self.wait_page_loaded()
        
        # 14 保育料減免・調整管理（階層その他）画面: 以下を入力申請年月日：（システム日付）決定年月日：（システム日付）認定年月：　〇〇年9月～〇〇年3月減免・調整種別：徴収金額指定減免金額指定：1000
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputShinseiDate_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputShinseiDate_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputShinseiDate_textboxInput").send_keys(sys_date)

        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputKetteiDate_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputKetteiDate_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputKetteiDate_textboxInput").send_keys(sys_date)

        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputNinteizukiStart_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputNinteizukiStart_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputNinteizukiStart_textboxInput").send_keys(case_data.get("認定年月開始"))

        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputNinteizukiEnd_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputNinteizukiEnd_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputNinteizukiEnd_textboxInput").send_keys(case_data.get("認定年月終了"))

        self.find_element_by_id(u"tab01_ZZZ000000_selShinseiInputGenmenType_select").click()
        Select(self.find_element_by_id(u"tab01_ZZZ000000_selShinseiInputGenmenType_select")).select_by_visible_text(case_data.get("減免・調整種別"))

        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputKingaku_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputKingaku_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputKingaku_textboxInput").send_keys(case_data.get("金額指定"))

        self.find_element_by_id(u"tab01_ZZZ000000_selShinseiInputKamoku_select").click()
        Select(self.find_element_by_id(u"tab01_ZZZ000000_selShinseiInputKamoku_select")).select_by_visible_text(case_data.get("科目"))
        
        # 15 保育料減免・調整管理（階層その他）画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.wait_page_loaded()
        
        # 16 保育料減免・調整管理（階層その他）画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        
        # 17 保育料減免・調整管理（階層その他）画面: 登録確認
        # Assert: 更新しました。を確認する。
        self.assert_message_area("tab01_QAPF106500_msg_span","登録しました。") 
        self.screen_shot("保育料減免・調整管理（階層その他）画面_17")
        
        # 児童検索タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 18 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_18")
        
        # 19 メインメニュー画面: 「バッチ管理」ボタン押下
        # 20 メインメニュー画面: 「即時実行」ボタン押下
        # 21 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # 22 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_22")
        
        # 23 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：賦課処理名：月次賦課計算処理
        # 24 スケジュール個別追加画面: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM = case_data.get("ShoriNM")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM)

        # 25 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()

        # 26 実行指示画面: 表示
        self.screen_shot("実行指示画面_26")
        
        # 27 実行指示画面: 以下を入力対象年月：対象年度：基準年月：
        # Assert: パラメータ化
        params = [
            {"title":"対象年月", "type": "text", "value": case_data.get("対象年月")},
            {"title":"対象年度", "type": "text", "value": case_data.get("対象年度")},
            {"title":"基準年月", "type": "text", "value": case_data.get("基準年月")}
        ]
        self.set_job_param_kodomo(params)
        
        # 28 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 29 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        # 処理が終わるまで待機する
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)

        self.screen_shot("実行結果管理画面_29")
        
        # 30 実行結果管理画面: 「No.1」ボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()
        
        # 31 結果確認画面: 表示
        self.screen_shot("結果確認画面_31")
        
        # 結果確認画面を閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 32 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_32")
        
        # 33 メインメニュー画面: 「子ども子育て支援」ボタン押下
        # 34 メインメニュー画面: 「入所管理」ボタン押下
        # 35 メインメニュー画面: 「児童検索」ボタン押下
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        
        # 36 児童検索画面: 表示
        self.screen_shot("児童検索画面_36")
        
        # 37 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("AtenaCD"))
        
        # 38 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        
        # 39 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_39")
        
        # 40 児童台帳画面: 「賦課」ボタン押下
        self.click_button_by_label("賦課")
        self.wait_page_loaded()

        # 41 児童賦課情報画面: 表示
        self.screen_shot("児童賦課情報画面_41")
        
        # 42 児童賦課情報画面: 「賦課計算(仮)」ボタン押下
        self.click_button_by_label("賦課計算(仮)")
        self.wait_page_loaded()

        # 43 児童賦課情報画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()

        # 44 児童賦課情報(仮)画面: 表示
        self.screen_shot("児童賦課情報(仮)画面_44")
        
        # 45 児童賦課情報(仮)画面: 「賦課計算(仮)」ボタン押下
        self.click_button_by_label("賦課計算(仮)")
        self.wait_page_loaded()

        # 46 児童賦課情報(仮)画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()

        # 47 児童賦課情報(仮)画面: 表示
        # Assert: 「個別仮賦課計算処理が正常に完了しました。」と表示されることを確認。
        self.screen_shot("児童賦課情報(仮)画面_47")
        self.assert_message_area("tab01_QAPF107010_msg_span","個別仮賦課計算処理が正常に完了しました。")
        
        # 48 児童賦課情報(仮)画面: 「９月」ボタン押下
        self.click_button_by_label("9月")

        # 49 月別仮計算結果(保育料)画面: 表示
        # Assert: 9月以降の料金について、9月度課税認定情報の税が反映されていることを確認
        self.screen_shot("月別仮計算結果(保育料)画面_49")
        
        # 50 月別仮計算結果(保育料)画面: パンくずリスト「児童賦課情報(仮)」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF107110_navi']/li[4]/a").click()
        
        # 51 児童賦課情報(仮)画面: 「特別保育利用料」タブをクリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF107010_hituyoriyu_li']/a/span").click()

        # 52 児童賦課情報(仮)画面: 「９月」ボタン押下
        self.click_button_by_label("9月")

        # 53 月別仮計算結果(特別保育料)画面: 表示
        # Assert: 9月以降の料金について、9月度課税認定情報の税が反映されていることを確認
        self.screen_shot("月別仮計算結果(特別保育料)画面_53")
        
        # 54 月別仮計算結果(特別保育料)画面: パンくずリスト「児童賦課情報(仮)」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF107210_navi']/li[4]/a").click()
        
        # 55 児童賦課情報(仮)画面: 「その他費用」タブをクリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF107010_sikyuninteikekka_li']/a/span").click()

        # 55 児童賦課情報(仮)画面: 科目選択
        self.find_element_by_id(u"tab01_QAPF107010_selSonotaHiyouKamokuKari_select").click()
        Select(self.find_element_by_id(u"tab01_QAPF107010_selSonotaHiyouKamokuKari_select")).select_by_visible_text("教材費")

        # 56 児童賦課情報(仮)画面: 「９月」ボタン押下
        self.click_button_by_label("9月")

        # 57 月別仮計算結果(その他費用)画面: 表示
        # Assert: 9月以降の料金について、9月度課税認定情報の税が反映されていることを確認
        self.screen_shot("月別仮計算結果(その他費用)画面_57")
        
        # 58 月別仮計算結果(その他費用)画面: パンくずリスト「児童賦課情報」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF107310_navi']/li[3]/a").click()

        # 59 児童賦課情報画面: 「賦課計算」ボタン押下
        self.click_button_by_label("賦課計算")
        self.wait_page_loaded()

        # 60 児童賦課情報画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()

        # 61 児童賦課情報画面: 表示
        # Assert: 「個別賦課計算処理が正常に完了しました。」と表示されることを確認。
        self.screen_shot("児童賦課情報画面_61")
        self.assert_message_area("tab01_QAPF107000_msg_span","個別賦課計算処理が正常に完了しました。")
        
        # 62 児童賦課情報画面: 「９月」ボタン押下
        self.click_button_by_label("9月")

        # 63 月別賦課履歴(保育料)画面: 表示
        # Assert: 9月以降の料金について、9月度課税認定情報の税が反映されていることを確認
        self.screen_shot("月別賦課履歴(保育料)画面_63")
        
        # 64 月別賦課履歴(保育料)画面: パンくずリスト「児童賦課情報」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF107100_navi']/li[3]/a").click()

        # 65 児童賦課情報画面: 「特別保育利用料」タブをクリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF107000_hituyoriyu_li']/a/span").click()

        # 66 児童賦課情報画面: 「９月」ボタン押下
        self.click_button_by_label("9月")

        # 67 月別賦課履歴(特別保育料)画面: 表示
        # Assert: 9月以降の料金について、9月度課税認定情報の税が反映されていることを確認
        self.screen_shot("月別賦課履歴(特別保育料)画面_67")
        
        # 68 月別賦課履歴(特別保育料)画面: パンくずリスト「児童賦課情報」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF107200_navi']/li[3]/a").click()

        # 69 児童賦課情報画面: 「その他費用」タブをクリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF107000_sikyuninteikekka_li']/a/span").click()
        
        # 56 児童賦課情報画面: 科目選択
        self.find_element_by_id(u"tab01_QAPF107000_selSonotaHiyouKamoku_select").click()
        Select(self.find_element_by_id(u"tab01_QAPF107000_selSonotaHiyouKamoku_select")).select_by_visible_text("教材費")

        # 70 児童賦課情報画面: 「９月」ボタン押下
        self.click_button_by_label("9月")

        # 71 月別賦課履歴(その他費用)画面: 表示
        # Assert: 9月以降の料金について、9月度課税認定情報の税が反映されていることを確認
        self.screen_shot("月別賦課履歴(その他費用)画面_71")
        
        # 72 月別賦課履歴(その他費用)画面: パンくずリスト「児童賦課情報」をクリック
        
