
import datetime
from base.kodomo_case import KodomoSiteTestCaseBase
from selenium.webdriver.support.ui import Select

class TestQAP010_28030302(KodomoSiteTestCaseBase):
    """TestQAP010_28030302"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params_list = case_data.get("sql_params")
        self.exec_sqlfile("test_QAP010_28030302_実施前スクリプト.sql", params=sql_params_list)
        super().setUp()
    
    # 入力情報をもとに、利用者負担額が算出できることを確認する。
    def test_QAP010_28030302(self):
        """利用者負担額算出"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        #システム日付を変数に設定
        date = datetime.date.today()
        sys_date = format(date, '%Y%m%d')

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「子ども子育て支援」ボタン押下  
        # 4 メインメニュー画面: 「入所管理」ボタン押下
        # 5 メインメニュー画面: 「児童検索」ボタン押下
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        
        # 6 児童検索画面: 表示
        self.screen_shot("児童検索画面_6")
        
        # 7 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("AtenaCD"))
        
        # 8 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        
        # 9 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_9")
        
        # 10 児童台帳画面: 「入所管理」タブ押下
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 11 児童台帳画面: 「減免・調整申請」ボタン押下
        self.click_button_by_label("減免・調整申請")
        self.wait_page_loaded()
        
        # 12 保育料減免・調整管理（階層その他）画面: 表示
        self.screen_shot("保育料減免・調整管理（階層その他）画面_12")
        
        # 13 保育料減免・調整管理（階層その他）画面: 「追加」ボタン押下
        self.click_button_by_label("追加")
        self.wait_page_loaded()
        
        # 14 保育料減免・調整管理（階層その他）画面: 以下を入力申請年月日：（システム日付）決定年月日：（システム日付）認定年月：　　～減免・調整種別：徴収金額指定減免金額指定：1000
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputShinseiDate_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputShinseiDate_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputShinseiDate_textboxInput").send_keys(sys_date)

        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputKetteiDate_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputKetteiDate_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputKetteiDate_textboxInput").send_keys(sys_date)

        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputNinteizukiStart_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputNinteizukiStart_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputNinteizukiStart_textboxInput").send_keys(case_data.get("認定年月開始"))

        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputNinteizukiEnd_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputNinteizukiEnd_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputNinteizukiEnd_textboxInput").send_keys(case_data.get("認定年月終了"))

        self.find_element_by_id(u"tab01_ZZZ000000_selShinseiInputGenmenType_select").click()
        Select(self.find_element_by_id(u"tab01_ZZZ000000_selShinseiInputGenmenType_select")).select_by_visible_text(case_data.get("減免・調整種別"))

        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputKingaku_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputKingaku_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShinseiInputKingaku_textboxInput").send_keys(case_data.get("金額指定"))

        self.find_element_by_id(u"tab01_ZZZ000000_selShinseiInputKamoku_select").click()
        Select(self.find_element_by_id(u"tab01_ZZZ000000_selShinseiInputKamoku_select")).select_by_visible_text(case_data.get("科目"))
        
        # 15 保育料減免・調整管理（階層その他）画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.wait_page_loaded()
        
        # 16 保育料減免・調整管理（階層その他）画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        
        # 17 保育料減免・調整管理（階層その他）画面: 登録確認
        # Assert: 登録しました。を確認する。
        self.screen_shot("保育料減免・調整管理（階層その他）画面_17")
        self.assert_message_area("tab01_QAPF106500_msg_span","登録しました。")
        
        # 18 保育料減免・調整管理（階層その他）画面: パンくずリスト「児童台帳」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF106500_navi']/li[2]/a").click()
        
        # 19 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_19")
        
        # 20 児童台帳画面: 「賦課」ボタン押下
        self.click_button_by_label("賦課")
        self.wait_page_loaded()
        
        # 21 児童賦課情報画面: 表示
        self.screen_shot("児童賦課情報画面_21")
        
        # 22 児童賦課情報画面: 「賦課計算(仮)」ボタン押下
        self.click_button_by_label("賦課計算(仮)")
        self.wait_page_loaded()
        
        # 23 児童賦課情報画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        
        # 24 児童賦課情報(仮)画面: 表示
        self.screen_shot("児童賦課情報(仮)画面_24")
        
        # 25 児童賦課情報(仮)画面: 「賦課計算(仮)」ボタン押下
        self.click_button_by_label("賦課計算(仮)")
        self.wait_page_loaded()
        
        # 26 児童賦課情報(仮)画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        
        # 27 児童賦課情報(仮)画面: 表示
        # Assert: 「個別仮賦課計算処理が正常に完了しました。」と表示されることを確認。
        self.screen_shot("児童賦課情報(仮)画面_27")
        self.assert_message_area("tab01_QAPF107010_msg_span","個別仮賦課計算処理が正常に完了しました。")
        
        # 28 児童賦課情報(仮)画面: 「〇月」ボタン押下
        self.click_button_by_label("6月")
        
        # 29 月別仮計算結果(保育料)画面: 表示
        self.screen_shot("月別仮計算結果(保育料)画面_29")
        
        # 30 月別仮計算結果(保育料)画面: パンくずリスト「児童賦課情報(仮)」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF107110_navi']/li[4]/a").click()
        
        # 31 児童賦課情報(仮)画面: 「特別保育利用料」タブをクリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF107010_hituyoriyu_li']/a/span").click()
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 32 児童賦課情報(仮)画面: 「〇月」ボタン押下
        self.click_button_by_label("6月")
        
        # 33 月別仮計算結果(特別保育料)画面: 表示
        self.screen_shot("月別仮計算結果(特別保育料)画面_33")
        
        # 34 月別仮計算結果(特別保育料)画面: パンくずリスト「児童賦課情報(仮)」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF107210_navi']/li[4]/a").click()
        
        # 35 児童賦課情報(仮)画面: 「その他費用」タブをクリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF107010_sikyuninteikekka_li']/a/span").click()
        
        # 36 児童賦課情報(仮)画面: 科目選択
        self.find_element_by_id(u"tab01_QAPF107010_selSonotaHiyouKamokuKari_select").click()
        Select(self.find_element_by_id(u"tab01_QAPF107010_selSonotaHiyouKamokuKari_select")).select_by_visible_text("副食費2号")

        # 36 児童賦課情報(仮)画面: 「〇月」ボタン押下
        self.click_button_by_label("6月")
        
        # 37 月別仮計算結果(その他費用)画面: 表示
        self.screen_shot("月別仮計算結果(その他費用)画面_37")
        
        # 38 月別仮計算結果(その他費用)画面: パンくずリスト「児童賦課情報」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF107310_navi']/li[3]/a").click()
        self.wait_page_loaded()
        # 39 児童賦課情報画面: 「賦課計算」ボタン押下
        self.click_button_by_label("賦課計算")
        self.wait_page_loaded()
        
        # 40 児童賦課情報画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        self.wait_page_loaded()
        # 41 児童賦課情報画面: 表示
        # Assert: 「個別賦課計算処理が正常に完了しました。」と表示されることを確認。
        self.screen_shot("児童賦課情報画面_41")
        self.assert_message_area("tab01_QAPF107000_msg_span","個別賦課計算処理が正常に完了しました。")
        
        # 42 児童賦課情報画面: 「〇月」ボタン押下
        self.click_button_by_label("6月")
        
        # 43 月別賦課履歴(保育料)画面: 表示
        self.screen_shot("月別賦課履歴(保育料)画面_43")
        
        # 44 月別賦課履歴(保育料)画面: No「１」ボタンを押下
        self.click_button_by_label("1")
        
        # 45 賦課履歴詳細情報(保育料)画面: 表示
        self.screen_shot("賦課履歴詳細情報(保育料)画面_45")
        
        # 46 賦課履歴詳細情報(保育料)画面: 「閉じる」ボタンを押下
        self.click_button_by_label("閉じる")
        
        # 47 月別賦課履歴(保育料)画面: パンくずリスト「児童賦課情報」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF107100_navi']/li[3]/a").click()
        
        # 48 児童賦課情報画面: 「特別保育利用料」タブをクリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF107000_hituyoriyu_li']/a/span").click()
        
        # 49 児童賦課情報画面: 「〇月」ボタン押下
        self.click_button_by_label("6月")
        
        # 50 月別賦課履歴(特別保育料)画面: 表示
        self.screen_shot("月別賦課履歴(特別保育料)画面_50")
        
        # 51 月別賦課履歴(特別保育料)画面: No「１」ボタンを押下
        self.click_button_by_label("1")
        
        # 52 賦課履歴詳細情報(特別保育料)画面: 表示
        self.screen_shot("賦課履歴詳細情報(特別保育料)画面_52")
        
        # 53 賦課履歴詳細情報(特別保育料)画面: 「閉じる」ボタンを押下
        self.click_button_by_label("閉じる")
        
        # 54 月別賦課履歴(特別保育料)画面: パンくずリスト「児童賦課情報」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF107200_navi']/li[3]/a").click()
        
        # 55 児童賦課情報画面: 「その他費用」タブをクリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF107000_sikyuninteikekka_li']/a/span").click()
        
        # 56 児童賦課情報画面: 科目選択
        self.find_element_by_id(u"tab01_QAPF107000_selSonotaHiyouKamoku_select").click()
        Select(self.find_element_by_id(u"tab01_QAPF107000_selSonotaHiyouKamoku_select")).select_by_visible_text("副食費2号")

        # 56 児童賦課情報画面: 「〇月」ボタン押下
        self.click_button_by_label("6月")
        
        # 57 月別賦課履歴(その他費用)画面: 表示
        self.screen_shot("月別賦課履歴(その他費用)画面_57")
        
        # 58 月別賦課履歴(その他費用)画面: No「１」ボタンを押下
        self.click_button_by_label("1")
        
        # 59 賦課履歴詳細情報(その他費用)画面: 表示
        self.screen_shot("賦課履歴詳細情報(その他費用)画面_59")
        
        # 60 賦課履歴詳細情報(その他費用)画面: 「閉じる」ボタンを押下
        self.click_button_by_label("閉じる")
        
        # 61 月別賦課履歴(その他費用)画面: パンくずリスト「児童賦課情報」をクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF107300_navi']/li[3]/a").click()
        self.wait_page_loaded()
        # 62 児童賦課情報画面: 「個別賦課・調定」ボタン押下
        self.click_button_by_label("個別賦課・調定")
        self.wait_page_loaded()
        
        # 63 個別賦課処理実行画面: 表示
        self.screen_shot("個別賦課処理実行画面_63")
        
        # 64 個別賦課処理実行画面: 「賦課情報反映」ボタン押下
        self.click_button_by_label("賦課情報反映")
        self.wait_page_loaded()
        
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        
        # 65 個別賦課処理実行画面: 表示
        # Assert: 「個別賦課調定処理が正常に完了しました。」と表示されることを確認。
        self.screen_shot("個別賦課処理実行画面_65")
        self.assert_message_area("tab01_QAPF107700_msg_span","個別賦課調定処理が正常に完了しました。")
        
        # 66 個別賦課処理実行画面: 「閉じる」ボタン押下
        self.click_button_by_label("閉じる")
        
        # 児童検索タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 68 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_68")
        
        # 69 メインメニュー画面: 「バッチ管理」ボタン押下  
        # 70 メインメニュー画面: 「即時実行」ボタン押下
        # 71 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # 72 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_72")
        
        # 73 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：賦課処理名：月次賦課計算処理
        # 74 スケジュール個別追加画面: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM_1 = case_data.get("ShoriNM_1")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM_1)

        # 75 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()
        
        # 76 実行指示画面: 表示
        self.screen_shot("実行指示画面_76")
        
        # 77 実行指示画面: 以下を入力対象年月：対象年度：基準年月：
        # Assert: パラメータ化
        params = [
            {"title":"対象年月", "type": "text", "value": case_data.get("対象年月")},
            {"title":"対象年度", "type": "text", "value": case_data.get("対象年度_月次")},
            {"title":"基準年月", "type": "text", "value": case_data.get("基準年月")}
        ]
        self.set_job_param_kodomo(params)
        
        # 78 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 79 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_79")
        
        # 80 実行結果管理画面: 「No.1」ボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()
        
        # 81 結果確認画面: 表示
        self.screen_shot("結果確認画面_81")
        
        # スケジュール個別追加タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 83 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_83")
        
        # 84 メインメニュー画面: 「バッチ管理」ボタン押下 
        # 85 メインメニュー画面: 「即時実行」ボタン押下
        # 86 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])

        # 87 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_87")
        
        # 88 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：賦課処理名：年次賦課計算処理
        # 89 スケジュール個別追加画面: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM_2 = case_data.get("ShoriNM_2")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM_2)
        
        # 90 スケジュール個別追加画面: 「No.1」ボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()
        
        # 91 実行指示画面: 表示
        self.screen_shot("実行指示画面_91")
        
        # 92 実行指示画面: 以下を入力対象年度：
        # Assert: パラメータ化
        params = [
            {"title":"対象年度", "type": "text", "value": case_data.get("対象年度_年次")}
        ]
        self.set_job_param_kodomo(params)
        
        # 93 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 94 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_94")
        
        # 95 実行結果管理画面: 「No.1」ボタン押下
        self.click_button_by_label("1")
        
        # 96 結果確認画面: 表示
        self.screen_shot("結果確認画面_96")
        
