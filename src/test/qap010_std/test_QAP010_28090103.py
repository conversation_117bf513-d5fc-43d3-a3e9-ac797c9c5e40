import os
import time
from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010_28090103(KodomoSiteTestCaseBase):
    """TestQAP010_28090103"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params_list = case_data.get("sql_params")
        self.exec_sqlfile("test_QAP010_28090103_実施前スクリプト.sql", params=sql_params_list)
        super().setUp()
    
    # 認定結果の登録が行えることを確認する。
    def test_QAP010_28090103(self):
        """認定結果登録"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        importDataFolder = os.path.abspath(self.common_test_data.get("importDataFolder"))
        

        self.do_login()
        # 2 メインメニュー　画面: 表示
        self.screen_shot("メインメニュー　画面_2")
        
        # 3 メインメニュー　画面: 「認可外申請管理」ボタン押下
        # 4 メインメニュー　画面: 「認可外申請検索」ボタンをダブルクリック
        self.goto_menu(["認可外申請管理","認可外申請検索"])

        # 5 検証条件入力　画面: 表示
        self.screen_shot("検証条件入力　画面_5")
        
        # 6 検証条件入力　画面: 住民コード＜パラメータ＞入力
        # Assert: パラメータ化

        self.find_element_by_id(u"tab01_QAZF100001_txtJuminCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAZF100001_txtJuminCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAZF100001_txtJuminCD_textboxInput").send_keys(case_data.get("sql_params").get("児童_宛名C_2"))

        self.screen_shot("検証条件入力　画面_7")
        # 7 検証条件入力　画面: 「検索」ボタンクリック
        self.click_button_by_label("検索(Enter)")
        
        # 8 世帯履歴　画面: 表示
        self.screen_shot("世帯履歴　画面_8")
        
        # 9 世帯履歴　画面: 「1」ボタンを押下
        self.click_button_by_label("1")

        # 10 世帯台帳　画面: 表示
        self.screen_shot("世帯台帳　画面_10")
        
        # 11 世帯台帳　画面: 「支給認定履歴」タブ　クリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF100300_sikyuninteirireki_li']/a").click()

        # 12 世帯台帳　画面: 表示
        self.screen_shot("世帯台帳　画面_12")
        
        # 13 世帯台帳　画面: №「 」　ボタン押下
        self.click_button_by_label("1")

        # 14 支給認定情報　画面: 表示
        self.screen_shot("支給認定情報　画面_14")
        
        # 15 支給認定情報　画面: 「修正」　ボタン押下
        self.click_button_by_label("修正")
        self.wait_page_loaded()

        # 16 支給認定情報　画面: 「支給認定結果」タブ　クリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF103600_sikyuninteikekka_li']/a").click()
        self.wait_page_loaded()

        # 17 支給認定情報　画面: 認定・却下年月日　を入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF103600_txtNinteiKekkaHitsuyoseiNinteiKetteiDay_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF103600_txtNinteiKekkaHitsuyoseiNinteiKetteiDay_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF103600_txtNinteiKekkaHitsuyoseiNinteiKetteiDay_textboxInput").send_keys(case_data.get("認定・却下年月日"))

        # 18 支給認定情報　画面: 認定結果、支給認定区分、保育の必要性（事由）、保育の必要性（続柄）　を選択
        # Assert: パラメータ化

        self.find_element_by_id(u"tab01_QAPF103600_selNinteiKekkaNinteiKekka_select").send_keys(case_data.get("認定結果"))
        self.find_element_by_id(u"tab01_QAPF103600_selNinteiKekkaShikyuNinteiKbn_select").send_keys(case_data.get("支給認定区分"))
        self.find_element_by_id(u"tab01_QAPF103600_selNinteiKekkaHoikuHitsuyoseiJiyu_select").send_keys(case_data.get("保育の必要性（事由）"))
        self.find_element_by_id(u"tab01_QAPF103600_selNinteiKekkaHoikuHitsuyoseiZokugara_select").send_keys(case_data.get("保育の必要性（続柄）"))
        self.find_element_by_id(u"tab01_QAPF103600_txtNinteiKekkaNinteiKikanStart_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF103600_txtNinteiKekkaNinteiKikanStart_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF103600_txtNinteiKekkaNinteiKikanStart_textboxInput").send_keys(case_data.get("認定期間（開始）"))
        self.find_element_by_id(u"tab01_QAPF103600_txtNinteiKekkaNinteiKikanEnd_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF103600_txtNinteiKekkaNinteiKikanEnd_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF103600_txtNinteiKekkaNinteiKikanEnd_textboxInput").send_keys(case_data.get("認定期間（終了）"))
        # 19 支給認定情報　画面: 認定結果、認定しない理由　を選択
        # Assert: パラメータ化
       
        # 20 支給認定情報　画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.screen_shot("支給認定情報　画面_20")
        
        # 21 支給認定情報　画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.wait_page_loaded()

        # 22 支給認定情報　画面: 「はい」押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        self.wait_page_loaded()

        # 23 支給認定情報　画面: 「支給認定結果」タブ　クリック

        self.find_element_by_xpath("//li[@id='tab01_QAPF103600_sikyuninteikekka_li']/a").click()
        # 24 支給認定情報　画面: 表示
        self.screen_shot("支給認定情報　画面_24")
        
        # 25 支給認定情報　画面: パンくず「世帯台帳」クリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF103600_navi']/li[3]/a").click()

        # 26 世帯台帳　画面: 表示
        self.screen_shot("世帯台帳　画面_26")
        
        # 27 世帯台帳　画面: 「児童一覧」タブ　クリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF100300_jidoiciran_li']/a").click()

        # 28 世帯台帳　画面: №「X」　ボタン押下
        # Assert: パラメータ化
        self.click_button_by_label("1")

        # 29 利用者申請管理　画面: 表示
        self.screen_shot("利用者申請管理　画面_29")
        
        # 30 利用者申請管理　画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 31 利用者申請管理　画面: 決定年月日　を入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF221000_txtKetteinengappi_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF221000_txtKetteinengappi_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF221000_txtKetteinengappi_textboxInput").send_keys(case_data.get("決定年月日"))

        # 32 利用者申請管理　画面: 決定結果　を選択
        self.find_element_by_id(u"tab01_QAPF221000_selKetteikekka_select").send_keys(case_data.get("決定結果"))

        # 33 利用者申請管理　画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.screen_shot("利用者申請管理　画面_33")
        
        # 34 利用者申請管理　画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.wait_page_loaded()

        # 35 利用者申請管理　画面: 「はい」押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        self.wait_page_loaded()

        # 36 利用者申請管理　画面: 表示
        self.screen_shot("利用者申請管理　画面_36")
        self.find_element_by_xpath("//li[@id='01']/span[2]").click()

        # 38 メインメニュー　画面: 表示
        self.screen_shot("メインメニュー　画面_38")
        
        # 39 メインメニュー　画面: 「バッチ管理」ボタン押下
        
        # 40 メインメニュー　画面: 「即時実行」ボタンを押下
        
        # 41 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        self.wait_page_loaded()

        # 42 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        # 43 スケジュール個別追加　画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("スケジュール_業務名_1"), case_data.get("スケジュール_サブシステム名_1"), case_data.get("スケジュール_処理名_1"))
        self.wait_page_loaded()

        # 44 スケジュール個別追加　画面: 表示
        self.screen_shot("スケジュール個別追加　画面_44")
        
        # 45 スケジュール個別追加　画面: 「№1」ボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()
        
        # 46 実行指示　画面: 表示
        self.screen_shot("実行指示　画面_46")
        
        # 47 実行指示　画面: 「受理物登録」ボタン押下
        self.click_button_by_label("受理物登録")

        # 48 受理物登録　画面: 表示
        self.screen_shot("受理物登録　画面_48")
        self.driver.set_window_size(1600, 1200)
        # 49 受理物登録　画面: 「アップロード」ボタン押下
        self.click_button_by_label("アップロード")
        self.wait_page_loaded()
        time.sleep(1)
        # 50 受理物登録　画面: 「参照」ボタン押下
        
        # 51 受理物登録　画面（ポップアップ）: アップロードするファイルを選択
        self.driver.switch_to.frame(1)
        upload_button = self.find_element_by_xpath("//input[@id='UPLOAD1']")
        self.wait_page_loaded()
        fileName = os.path.join(importDataFolder,case_data.get("対象ファイル"))
        upload_button.send_keys(fileName)
        self.wait_page_loaded()
        # 52 受理物登録　画面（ポップアップ）: 「開く」ボタン押下
        
        # 53 受理物登録　画面（ポップアップ）: 「追加」ボタン押下
        self.find_element_by_id(u"ADD").click()

        # 54 受理物登録　画面（ポップアップ）: 表示
        self.screen_shot("受理物登録　画面（ポップアップ）_54")
        self.driver.set_window_size(1600, 1200)
        # 55 受理物登録　画面（ポップアップ）: 「送信」ボタン押下
        self.find_element_by_xpath("//input[@value='送信']").click()
        
        self.driver.switch_to.default_content()

        # 56 受理物登録　画面: 「件数表示」ボタン押下
        self.click_button_by_label("件数表示")
        self.click_button_by_label("登録実行")

        # 57 受理物登録　画面: 表示
        self.screen_shot("受理物登録　画面_57")
        
        # 58 受理物登録　画面: タブを閉じる
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()

        # 59 実行指示　画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.screen_shot("実行指示　画面_59")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 60 実行指示　画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        time.sleep(3)

        # 61 実行結果管理　画面: 表示
        # Assert: 「（QPNBN00100）認可外申請データ取込処理」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理　画面_61")
        
        # 62 実行結果管理　画面: 「№」ボタン押下
        # self.click_button_by_label("1")

        # 63 結果確認　画面: 表示
        # self.screen_shot("結果確認　画面_63")
        
        # 64 結果確認　画面: 「ダウンロード」ボタン押下
        # self.click_button_by_label("ダウンロード")
        
        # 65 ファイルダウンロード: 「1」ボタン押下
        # self.driver.switch_to.frame(1)
        # self.find_element_by_xpath('//*[@id="FILE_LIST"]/table/tbody/tr/td[1]/input').click()
        # self.driver.switch_to.default_content()
        # self.click_button_by_label("閉じる")

        # 66 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 67 ログ: 表示
        # self.screen_shot("ログ_67")

        # 68 ログ: 閉じる

        # 69 結果確認　画面: 「納品物確認」ボタン押下
        
        # 70 納品物管理　画面: 認可外一括取込_エラーリスト.csv　「ダウンロード」ボタン押下
        
        # 71 ファイルダウンロード: 「1」ボタン押下
        
        # 72 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下


        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援、サブシステム名：認可外申請管理、処理名：認可外申請データ取込処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("納品物_業務名_1"), case_data.get("納品物_サブシステム名_1"), case_data.get("納品物_処理名_1"))
        # 納品物管理画面: 「認可外一括取込_エラーリスト.csv」の「ダウンロード」ボタン押下
        #ファイルダウンロード画面: 表示
        #ファイルダウンロード画面: 「認可外一括取込_エラーリスト.csv」のNoボタン押下

        self.pdf_download("認可外一括取込_エラーリスト.csv","")
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()     
        self.find_element_by_xpath("//li[@id='01']/span[2]").click()
        # 73 帳票（CSV）: 表示
        # 74 帳票（CSV）: 閉じる
        
        # 76 メインメニュー　画面: 表示
        self.screen_shot("メインメニュー　画面_76")
        
        # 77 メインメニュー　画面: 「バッチ管理」ボタン押下        
        # 78 メインメニュー　画面: 「即時実行」ボタンを押下        
        # 79 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])

        # 80 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        # 81 スケジュール個別追加　画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("納品物_業務名_1"), case_data.get("納品物_サブシステム名_1"), case_data.get("納品物_処理名_1"))
        
        # 82 スケジュール個別追加　画面: 表示
        self.screen_shot("スケジュール個別追加　画面_82")
        
        # 83 スケジュール個別追加　画面: 「№2」ボタン押下
        self.click_button_by_label("2")

        # 84 実行指示　画面: 表示
        self.screen_shot("実行指示　画面_84")
        
        # 85 実行指示　画面: ワーニングデータ登録区分、決定結果更新区分、並び順　を選択
        # 86 実行指示　画面: 決定年月日、発行年月日（当日）　を入力
        # Assert: パラメータ化
        params = [
            {"title":"ワーニングデータ登録区分", "type": "select", "value": case_data.get("ワーニングデータ登録区分")},
            {"title":"決定結果更新区分", "type": "select", "value": case_data.get("決定結果更新区分")},
            {"title":"並び順", "type": "select", "value": case_data.get("並び順")},
            {"title":"決定年月日", "type": "text", "value": case_data.get("決定年月日")},
        ]
        self.set_job_param_kodomo(params)
        
        # 87 実行指示　画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.screen_shot("実行指示　画面_87")
        
        # 88 実行指示　画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()

        # 89 実行結果管理　画面: 表示
        # Assert: 「（QPNBN00200）認可外申請データ登録処理」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理　画面_89")
        
        # 90 実行結果管理　画面: 「№」ボタン押下
        # 91 結果確認　画面: 表示
        # self.screen_shot("結果確認　画面_91")
        # 92 結果確認　画面: 「ダウンロード」ボタン押下
        # 93 ファイルダウンロード: 「1」ボタン押下
        # 94 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 95 ログ: 表示
        # self.screen_shot("ログ_95")
        # 96 ログ: 閉じる
        # 97 結果確認　画面: 「納品物確認」ボタン押下
        # 98 納品物管理　画面: QAPP701800_認可外申請登録結果リスト.pdf　「ダウンロード」ボタン押下
        # 99 ファイルダウンロード: 「1」ボタン押下
        # 100 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 101 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_101")
        # 102 帳票（PDF）: 閉じる

        # 納品物確認画面: 業務名：子ども子育て支援、サブシステム名：認可外申請管理、処理名：認可外申請データ取込処理
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("納品物_業務名_1"), case_data.get("納品物_サブシステム名_1"), case_data.get("納品物_処理名_1"))

        # 納品物管理画面: 「QAPP701800_認可外申請登録結果リスト.pdf」の「ダウンロード」ボタン押下
        #ファイルダウンロード画面: 表示
        #ファイルダウンロード画面: 「QQAPP701800_認可外申請登録結果リスト.pdf」のNoボタン押下
        self.pdf_download("QAPP701800_認可外申請登録結果リスト.pdf","")
        self.click_button_by_label("閉じる")

        # 103 納品物管理　画面: 認可外一括登録_結果リスト.csv　「ダウンロード」ボタン押下
        # 104 ファイルダウンロード: 「1」ボタン押下
        # 105 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 106 帳票（CSV）: 表示
        # self.screen_shot("帳票（CSV）_106")
        # 107 帳票（CSV）: 閉じる
        self.pdf_download("認可外一括登録_結果リスト.csv","")
        self.click_button_by_label("閉じる")
        self.find_element_by_xpath("//li[@id='02']/span[2]").click() 
        
