from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010_28050101(KodomoSiteTestCaseBase):
    """TestQAP010_28050101"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params_list = case_data.get("sql_params")
        self.exec_sqlfile("test_QAP010_28050101_実施前スクリプト.sql", params=sql_params_list)
        super().setUp()
    
    # 事業所、事業者情報について登録することができることを確認する。
    def test_QAP010_28050101(self):
        """事業所・事業者情報登録"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})

        self.do_login()
        # 2 メインメニュー 画面: 表示
        self.screen_shot("メインメニュー 画面_2")
        
        # 3 メインメニュー 画面: 「子ども子育て支援」クリック
        
        # 4 メインメニュー 画面: 「施設事業者（認可事業者）」クリック
        
        # 5 メインメニュー 画面: 「追加」ダブルクリック
        self.goto_menu(["子ども子育て支援","施設事業者（認可事業者）","追加"])

        # 6 認可申請情報（事業者） 画面: 表示
        self.screen_shot("認可申請情報（事業者） 画面_6")
        
        # 7 認可申請情報（事業者） 画面: 「異動年月日」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_txtIDoYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtIDoYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtIDoYMD_textboxInput").send_keys(case_data.get("事業者_異動年月日"))
        self.wait_page_loaded()

        # 8 認可申請情報（事業者） 画面: 「異動内容」入力
        # Assert: 初期値とする：「新規」
        # 9 認可申請情報（事業者） 画面: 「申請年月日」入力
        # Assert: 初期値とする：「当日日付」

        # 10 認可申請情報（事業者） 画面: 「決定年月日」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_txtKetteiYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtKetteiYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtKetteiYMD_textboxInput").send_keys(case_data.get("事業者_決定年月日"))
        self.wait_page_loaded()

        # 11 認可申請情報（事業者） 画面: 「審査結果」入力
        # Assert: 初期値とする：「承認」
        self.find_element_by_id(u"tab01_ZZZ000000_selShinsaKekka_select").send_keys(case_data.get("事業者_審査結果"))
        self.wait_page_loaded()

        # 12 認可申請情報（事業者） 画面: 「法人等カナ名称」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinKanaNM_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinKanaNM_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinKanaNM_textboxInput").send_keys(case_data.get("事業者_法人等カナ名称"))
        self.wait_page_loaded()

        # 13 認可申請情報（事業者） 画面: 「事業者番号」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_txtJigyoshaNo_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtJigyoshaNo_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtJigyoshaNo_textboxInput").send_keys(case_data.get("sql_params").get("事業者番号"))
        self.wait_page_loaded()

        # 14 認可申請情報（事業者） 画面: 「法人等名称」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinNM_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinNM_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinNM_textboxInput").send_keys(case_data.get("事業者_法人等名称"))
        self.wait_page_loaded()

        # 15 認可申請情報（事業者） 画面: 「郵便番号（前３桁）」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinPostNoOya_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinPostNoOya_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinPostNoOya_textboxInput").send_keys(case_data.get("事業者_郵便番号（前３桁）"))
        self.wait_page_loaded()

        # 16 認可申請情報（事業者） 画面: 「郵便番号（後４桁）」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinPostNoKo_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinPostNoKo_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinPostNoKo_textboxInput").send_keys(case_data.get("事業者_郵便番号（後４桁）"))
        self.wait_page_loaded()

        # 17 認可申請情報（事業者） 画面: 「直接入力（法人住所）」クリック
        self.find_element_by_id(u"tab01_ZZZ000000_chkHojinChokusetsuInputchk0").click()
        self.wait_page_loaded()

        # 18 認可申請情報（事業者） 画面: 「住所」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinJusho_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinJusho_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinJusho_textboxInput").send_keys(case_data.get("事業者_住所"))
        self.wait_page_loaded()

        # 19 認可申請情報（事業者） 画面: 「電話番号」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinTEL_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinTEL_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtHojinTEL_textboxInput").send_keys(case_data.get("事業者_電話番号"))
        self.wait_page_loaded()
        
        # 20 認可申請情報（事業者） 画面: 「法人等種別」入力
        # Assert: 初期値とする：「社会福祉法人」
        self.find_element_by_id(u"tab01_ZZZ000000_selHojinShubetsu_select").send_keys(case_data.get("事業者_法人等種別"))
        self.wait_page_loaded()

        # 21 認可申請情報（事業者） 画面: 「法人等所在地（県）」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_selHojinShozaichiTodofuken_select").send_keys(case_data.get("事業者_法人等所在地（県）"))
        self.wait_page_loaded()

        # 22 認可申請情報（事業者） 画面: 「法人等所在地（市区町村）」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_selHojinShozaichiShikuchoson_select").send_keys(case_data.get("事業者_法人等所在地（市区町村）"))
        self.wait_page_loaded()

        # 23 認可申請情報（事業者） 画面: 「設立年月日」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_txtSetsuritsuYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtSetsuritsuYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtSetsuritsuYMD_textboxInput").send_keys(case_data.get("事業者_設立年月日"))
        self.wait_page_loaded()

        # 24 認可申請情報（事業者） 画面: 「代表者カナ氏名」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_txtDaihyoshaKanaShimei_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtDaihyoshaKanaShimei_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtDaihyoshaKanaShimei_textboxInput").send_keys(case_data.get("事業者_代表者カナ氏名"))
        self.wait_page_loaded()

        # 25 認可申請情報（事業者） 画面: 「代表者氏名」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_txtDaihyoshaShimei_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtDaihyoshaShimei_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtDaihyoshaShimei_textboxInput").send_keys(case_data.get("事業者_代表者氏名"))

        # 26 認可申請情報（事業者） 画面: 「代表者職名」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_ZZZ000000_txtDaihyoshaShokuNM_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtDaihyoshaShokuNM_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtDaihyoshaShokuNM_textboxInput").send_keys(case_data.get("事業者_代表者職名"))

        # 27 認可申請情報（事業者） 画面: 「就任年月日」入力
        # Assert: パラメータ化

        self.find_element_by_id(u"tab01_ZZZ000000_txtShuninYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShuninYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_ZZZ000000_txtShuninYMD_textboxInput").send_keys(case_data.get("事業者_就任年月日"))

        # 28 認可申請情報（事業者） 画面: 「入力チェックボタン」クリック
        self.click_button_by_label("入力チェック")
        self.screen_shot("認可申請情報（事業者） 画面_28")
        
        # 29 認可申請情報（事業者） 画面: 「登録ボタン」クリック
        self.click_button_by_label("登録")

        # 30 認可申請情報（事業者） 画面: 「はいボタン」クリック
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()

        # 31 認可申請情報（事業者） 画面: 表示
        self.screen_shot("認可申請情報（事業者） 画面_31")
        
        # 32 認可申請情報（事業者） 画面: 「タブ[×]」クリック
        self.find_element_by_xpath("//li[@id='01']/span[2]").click()

        # 33 メインメニュー 画面: 「施設事業者（認可事業所）」クリック
        
        # 34 メインメニュー 画面: 「追加」ダブルクリック
        self.goto_menu(["子ども子育て支援","施設事業者（認可事業所）","追加"])

        # 35 認可申請情報（施設事業所） 画面: 表示
        self.screen_shot("認可申請情報（施設事業所） 画面_35")
        
        # 36 認可申請情報（施設事業所） 画面: 「異動年月日」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtIDoYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtIDoYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtIDoYMD_textboxInput").send_keys(case_data.get("事業所_異動年月日"))

        # 37 認可申請情報（施設事業所） 画面: 「異動内容」入力
        # Assert: 初期値とする：「新規」

        # 38 認可申請情報（施設事業所） 画面: 「申請年月日」入力
        # Assert: 初期値とする：「当日日付」

        # 39 認可申請情報（施設事業所） 画面: 「決定年月日」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtKetteiYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtKetteiYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtKetteiYMD_textboxInput").send_keys(case_data.get("事業所_決定年月日"))

        # 40 認可申請情報（施設事業所） 画面: 「審査結果」入力
        # Assert: 初期値とする：「承認」
        self.find_element_by_id(u"tab01_QAPF200200_selShinsaKekka_select").send_keys(case_data.get("事業所_審査結果"))

        # 41 認可申請情報（施設事業所） 画面: 「施設コード」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuCD_textboxInput").send_keys(case_data.get("事業所_施設コード"))
        
        # 41 認可申請情報（施設事業所） 画面: 「所在区」入力
        self.find_element_by_id(u"tab01_QAPF200200_selWrGyoseiku_JigyoshoShokanku_select").send_keys(case_data.get("事業所_所在区"))

        # 42 認可申請情報（施設事業所） 画面: 「認可区分」入力
        # Assert: 初期値とする：「認可」
        self.find_element_by_id(u"tab01_QAPF200200_selNinkaKbn_select").send_keys(case_data.get("事業所_認可区分"))

        # 43 認可申請情報（施設事業所） 画面: 「認可年月日」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaYMD_textboxInput").send_keys(case_data.get("事業所_認可年月日"))

        # 44 認可申請情報（施設事業所） 画面: 「認定年月日」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtNinteiYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtNinteiYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtNinteiYMD_textboxInput").send_keys(case_data.get("事業所_認定年月日"))

        # 45 認可申請情報（施設事業所） 画面: 「施設カナ名称」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuKanaNM_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuKanaNM_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuKanaNM_textboxInput").send_keys(case_data.get("事業所_施設カナ名称"))

        # 46 認可申請情報（施設事業所） 画面: 「施設名称」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuNM_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuNM_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuNM_textboxInput").send_keys(case_data.get("事業所_施設名称"))

        # 47 認可申請情報（施設事業所） 画面: 「郵便番号（前３桁）」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuPostNoOya_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuPostNoOya_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuPostNoOya_textboxInput").send_keys(case_data.get("事業所_郵便番号（前３桁）"))

        # 48 認可申請情報（施設事業所） 画面: 「郵便番号（後４桁）」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuPostNoKo_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuPostNoKo_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuPostNoKo_textboxInput").send_keys(case_data.get("事業所_郵便番号（後４桁）"))

        # 49 認可申請情報（施設事業所） 画面: 「直接入力（施設住所）」クリック
        self.find_element_by_id(u"tab01_QAPF200200_chkShisetsuChokusetsuInputchk0").click()

        # 50 認可申請情報（施設事業所） 画面: 「住所」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuJusho_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuJusho_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuJusho_textboxInput").send_keys(case_data.get("事業所_住所"))

        # 51 認可申請情報（施設事業所） 画面: 「電話番号」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuTEL_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuTEL_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtShisetsuTEL_textboxInput").send_keys(case_data.get("事業所_電話番号"))

        # 52 認可申請情報（施設事業所） 画面: 「施設種類」入力
        # Assert: 初期値とする：「認定こども園－幼保連携型」
        self.find_element_by_id(u"tab01_QAPF200200_selShisetsuJigyoshoMeisaiKbn_select").send_keys(case_data.get("事業所_施設種類"))

       
        # 53 認可申請情報（施設事業所） 画面: 「施設所在地（県）」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_selShisetsuShozaichiTodofuken_select").send_keys(case_data.get("事業所_施設所在地（県）"))

        # 54 認可申請情報（施設事業所） 画面: 「施設所在地（市区町村）」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_selShisetsuShozaichiShikuchoson_select").send_keys(case_data.get("事業所_施設所在地（市区町村）"))

        # 55 認可申請情報（施設事業所） 画面: 「事業開始年月日」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtJigyoStartYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtJigyoStartYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtJigyoStartYMD_textboxInput").send_keys(case_data.get("事業所_事業開始年月日"))

        
        # 56 認可申請情報（施設事業所） 画面: 「認可定員」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaTein_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaTein_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaTein_textboxInput").send_keys(case_data.get("事業所_認可定員"))

        # 57 認可申請情報（施設事業所） 画面: 「１号認定」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaTein1_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaTein1_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaTein1_textboxInput").send_keys(case_data.get("事業所_１号認定"))

        # 58 認可申請情報（施設事業所） 画面: 「２号認定」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaTein2_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaTein2_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaTein2_textboxInput").send_keys(case_data.get("事業所_２号認定"))

        # 59 認可申請情報（施設事業所） 画面: 「３号認定」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaTein3_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaTein3_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtNinkaTein3_textboxInput").send_keys(case_data.get("事業所_３号認定"))

        # 60 認可申請情報（施設事業所） 画面: 「地域枠」入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF200200_txtChikiwakuNinkaTein_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF200200_txtChikiwakuNinkaTein_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF200200_txtChikiwakuNinkaTein_textboxInput").send_keys(case_data.get("事業所_地域枠"))

        # 61 認可申請情報（施設事業所） 画面: 「適用有無」入力
        # Assert: 「無し」を選択
        self.find_element_by_id(u"tab01_QAPF200200_selTekiyoUmu_select").send_keys(case_data.get("事業所_適用有無"))

        # 62 認可申請情報（施設事業所） 画面: 「みなし確認適用の有無」入力
        # Assert: 初期値とする：「有り」
        self.find_element_by_id(u"tab01_QAPF200200_selMinashiKakuninTekiyoUmu_select").send_keys(case_data.get("事業所_みなし確認適用の有無"))

        # 63 認可申請情報（施設事業所） 画面: 「事業者情報タブ」クリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF200200_answer_li']/a").click()

        # 64 認可申請情報（施設事業所） 画面: 表示
        self.screen_shot("認可申請情報（施設事業所） 画面_64")
        
        # 65 認可申請情報（施設事業所） 画面: 「事業者番号検索ボタン」クリック
        self.find_element_by_id(u"tab01_QAPF200200_btnJigyoshaKensaku_button").click()

        # 66 認可事業者検索 画面: 表示
        self.screen_shot("認可事業者検索 画面_66")
        
        # 67 認可事業者検索 画面: 「事業者番号」入力
        self.find_element_by_id(u"tab01_QAPF201000_txtNinkaJigyoshaKensakuJigyoshaNo_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF201000_txtNinkaJigyoshaKensakuJigyoshaNo_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF201000_txtNinkaJigyoshaKensakuJigyoshaNo_textboxInput").send_keys(case_data.get("sql_params").get("事業者番号"))
        self.wait_page_loaded()
        # Assert: パラメータ化
        
        # 68 認可事業者検索 画面: 「検索(Enter)ボタン」クリック
        self.click_button_by_label("検索(Enter)")

        # 69 認可申請情報（施設事業所） 画面: 表示
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 70 認可申請情報（施設事業所） 画面: 「入力チェックボタン」クリック
        self.click_button_by_label("入力チェック")
        self.screen_shot("認可申請情報（施設事業所） 画面_70")
        
        # 71 認可申請情報（施設事業所） 画面: 「登録ボタン」クリック
        self.click_button_by_label("登録")

        # 72 認可申請情報（施設事業所） 画面: 「はいボタン」クリック
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()

        # 73 認可申請情報（施設事業所） 画面: 表示
        self.screen_shot("認可申請情報（施設事業所） 画面_73")
        
        # 74 認可申請情報（施設事業所） 画面: 「修正ボタン」クリック
        # self.click_button_by_label("修正")

        # 75 認可申請情報（施設事業所） 画面: 「事業所番号」入力
        # Assert: パラメータ化
        # self.find_element_by_id(u"tab01_QAPF200200_txtJigyoshoNo_textboxInput").click()
        # self.find_element_by_id(u"tab01_QAPF200200_txtJigyoshoNo_textboxInput").clear()
        # self.find_element_by_id(u"tab01_QAPF200200_txtJigyoshoNo_textboxInput").send_keys(case_data.get("sql_params").get("事業所番号"))

        # # 76 認可申請情報（施設事業所） 画面: 「入力チェックボタン」クリック
        # self.click_button_by_label("入力チェック")
        # self.screen_shot("認可申請情報（施設事業所） 画面_76")
        
        # # 77 認可申請情報（施設事業所） 画面: 「登録ボタン」クリック
        # self.click_button_by_label("登録")

        # # 78 認可申請情報（施設事業所） 画面: 「はいボタン」クリック
        # self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()

        # # 79 認可申請情報（施設事業所） 画面: 表示
        # self.screen_shot("認可申請情報（施設事業所） 画面_79")
        
        # 80 認可申請情報（施設事業所） 画面: 「タブ[×]」クリック
        
