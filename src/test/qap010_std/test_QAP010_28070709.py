import time
import unittest
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAP010_28070709(KodomoSiteTestCaseBase):
    """TestQAP010_28070709"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()
    
    # 滞納処分にかかわる各種帳票が作成できることを確認する。
    def test_QAP010_28070709(self):
        """各種帳票作成"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        settings = self.common_test_data
        self.test_data = settings.get("test_qap010_28070709")

        self.do_login()
        # 2 メインメニュー画面: 「バッチ管理」クリック
        self.find_element_by_xpath("//*[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        time.sleep(1)
        
        # 3 メインメニュー画面: 「即時実行」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)
        
        # 4 メインメニュー画面: 「スケジュール個別追加」ダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        
        # 5 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_4")
        
        # 6 スケジュール個別追加画面: 業務名：収納・滞納 サブシステム名：日次 処理名：日次消込処理 処理区分：
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuGyomuNM_select"),"収納・滞納")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuSubSystemNM_select"),"児童")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuShoriNM_select"),"日次消込処理")
        
        # 7 スケジュール個別追加画面: 「検索」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF000400_BtnKensaku_button").click()
        
        # 8 スケジュール個別追加画面: 「(JAABN06400) 日次消込本処理」Noボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_BtnMode32_3_4_button").click()
        
        # 9 実行指示画面: 表示
        self.screen_shot("実行指示画面_8")
        
        # 10 実行指示画面: 取扱業務：（パラメータ化）　を選択
        # Assert: 後続テストのための事前準備として処理を実施
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF002200_groupingKBN_select"),self.test_data.get("qap010_28070709_groupingKBN",""))
        self.screen_shot("実行指示画面_9")
        
        # 11 実行指示画面: 「実行」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF002200_executebtn_button").click()
        
        # 12 実行結果管理画面: 表示
        # Assert: 「実行しました。」のメッセージのチェック
        self.screen_shot("実行結果管理画面_11")
        
        # 13 実行結果管理画面: 正常終了まで5分置きに「実行」ボタンクリック
        self.wait_job_finished_kodomo(3600, 20)
        self.screen_shot("実行結果管理画面_12")
        
        # 14 メインメニュー画面: 表示
        time.sleep(1)
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.screen_shot("メインメニュー画面_13")
        
        # 15 メインメニュー画面: 「収納」クリック
        self.find_element_by_xpath("//*[@id='rootmenu_0']/li[contains(span,'収納')]/span").click()
        time.sleep(1)
        
        # 16 メインメニュー画面: 「個人別収納状況」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'収納')]/ul/li[contains(span,'個人別収納状況')]/span").click()
        time.sleep(1)
        
        # 17 メインメニュー画面: 「個人別収納状況」ダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'収納')]/ul/li[contains(span,'個人別収納状況')]/ul/li[contains(span,'個人別収納状況')]/span")
        self.actionChains.double_click(searchBtn).perform()
        
        # 18 （個人・法人）検索条件入力画面: 表示
        self.screen_shot("（個人・法人）検索条件入力画面_18")
        
        # 19 （個人・法人）検索条件入力画面: 住民コード：（パラメータ化）　を入力
        self.find_element(By.ID,"tab01_JAAF400100_txtJuminCD_textboxInput").send_keys("")
        self.find_element(By.ID,"tab01_JAAF400100_txtJuminCD_textboxInput").send_keys(self.test_data.get("qap010_28070709_txtJuminCD",""))
        
        # 20 （個人・法人）検索条件入力画面: 「検索」ボタンクリック
        self.find_element(By.ID,"tab01_JAAF400100_WrCmnBtn05_button").click()
        
        # 21 （個人・法人）管理情報照会画面: 表示
        self.screen_shot("（個人・法人）管理情報照会画面_20")
        
        # 22 （個人・法人）管理情報照会画面: 「処分一覧」ボタンクリック
        self.find_element(By.ID,"tab01_JAAF400300_btnShobunIchiran_button").click()
        
        # 23 処分一覧画面: 表示
        self.screen_shot("処分一覧画面_22")
        
        # 24 処分一覧画面: 処分種類：分納誓約
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JADF000100_selShobunShurui_select"),"分納誓約")
        
        # 25 処分一覧画面: 「新規」ボタンクリック
        self.find_element(By.ID,"tab01_JADF000100_btnShinkiToroku_button").click()
        
        # 26 分納誓約管理画面: 表示
        self.screen_shot("分納誓約管理画面_25")
        
        # 27 分納誓約管理画面: 「納付明細一覧」タブクリック
        self.find_element(By.ID,"tab01_JAEF200100_2_li").click()
        
        # 28 分納誓約管理画面：納付明細一覧タブ: 表示
        self.screen_shot("分納誓約管理画面：納付明細一覧タブ_27")
        
        # 29 分納誓約管理画面：納付明細一覧タブ: 「期別選択」ボタンクリック
        self.find_element(By.ID,"tab01_JAEF200100_btnKibetsuSentaku_button").click()
        
        # 30 期別選択画面: 表示
        self.screen_shot("期別選択画面_29")
        
        # 31 期別選択画面: 「到来」ボタンクリック
        self.find_element(By.ID,"tab01_JADF000200_btnTorai_button").click()
        
        # 32 期別選択画面: 「決定」ボタンクリック
        self.find_element(By.ID,"tab01_JADF000200_btnKettei_button").click()
        
        # 33 分納誓約管理画面：納付明細一覧タブ: 表示
        # Assert: 分納誓約登録対象となる明細が表示されていることを確認
        
        # 34 分納誓約管理画面：納付明細一覧タブ: 「分納誓約情報」タブクリック
        self.find_element(By.ID,"tab01_JAEF200100_1_li").click()
        
        # 35 分納誓約管理画面：分納誓約情報タブ: 表示
        
        # 36 分納誓約管理画面：分納誓約情報タブ: 仮登録日：（パラメータ化）　を入力 約束日：（パラメータ化）　を入力 延滞金計算日：（パラメータ化）　を入力開始年月：（パラメータ化）　を入力
        # 仮登録日
        self.find_element(By.ID,"tab01_JAEF200100_txtKariTorokubi_textboxInput").clear()
        self.find_element(By.ID,"tab01_JAEF200100_txtKariTorokubi_textboxInput").send_keys(self.test_data.get("qap010_28070709_txtKariTorokubi",""))
        
        # 約束日
        self.find_element(By.ID,"tab01_JAEF200100_txtYakusokubi_textboxInput").clear()
        self.find_element(By.ID,"tab01_JAEF200100_txtYakusokubi_textboxInput").send_keys(self.test_data.get("qap010_28070709_txtYakusokubi",""))
        
        # 延滞金計算日
        self.find_element(By.ID,"tab01_JAEF200100_txtEntaikinKeisanDay_textboxInput").clear()
        self.find_element(By.ID,"tab01_JAEF200100_txtEntaikinKeisanDay_textboxInput").send_keys(self.test_data.get("qap010_28070709_txtEntaikinKeisanDay",""))
        
        # 開始年月
        self.find_element(By.ID,"tab01_JAEF200100_txtStartYM_textboxInput").clear()
        self.find_element(By.ID,"tab01_JAEF200100_txtStartYM_textboxInput").send_keys(self.test_data.get("qap010_28070709_txtStartYM",""))	
        
        # 37 分納誓約管理画面：分納誓約情報タブ: 区分：分納誓約　を選択
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JAEF200100_selBunNoKbn_select"),"分納誓約")
        
        # 38 分納誓約管理画面：分納誓約情報タブ: 分納理由：低所得　を選択
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JAEF200100_selBunNoRiyu_select"),"低所得")
        
        # 39 分納誓約管理画面：分納誓約情報タブ: 確定延滞金額：有　を選択
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JAEF200100_selKakuteiEntaikinUmu_select"),"有")
        
        # 40 分納誓約管理画面：分納誓約情報タブ: 計算延滞金額：有　を選択
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JAEF200100_selKeisanEntaikinUmu_select"),"有")
        
        # 41 分納誓約管理画面：分納誓約情報タブ: 納付優先順位：本＞加＞督＞延　を選択
        # self.select_Option(self.driver,self.find_element(By.ID,"tab01_JAEF200100_selNofuYusenRank_select"),"本＞加＞督＞延")
        
        # 42 分納誓約管理画面：分納誓約情報タブ: 期日：月末　を選択
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JAEF200100_selKijitsu_select"),"月末")
        
        # 43 分納誓約管理画面：分納誓約情報タブ: 納付額/回：50　を入力
        self.find_element(By.ID,"tab01_JAEF200100_txtMaiNofugaku_textboxInput").send_keys("")
        self.find_element(By.ID, "tab01_JAEF200100_txtMaiNofugaku_textboxInput").send_keys("100")
        
        # 44 分納誓約管理画面：分納誓約情報タブ: 備考：テスト登録　を入力
        self.find_element(By.ID,"tab01_JAEF200100_fldBiko_textarea").send_keys("")
        self.find_element(By.ID,"tab01_JAEF200100_fldBiko_textarea").send_keys("テスト登録")
        
        # 45 分納誓約管理画面：分納誓約情報タブ: 支払方法：納付書（納）　を選択
        # self.select_Option(self.driver,self.find_element(By.ID,"tab01_JAEF200100_selShiharaiHoho_input"),"納付書（納）")
        
        # 46 分納誓約管理画面：分納誓約情報タブ: 「計画」ボタンクリック
        self.find_element(By.ID,"tab01_JAEF200100_btnKeikaku_button").click()
        
        # 47 分納誓約管理画面：分納誓約情報タブ: 「計画確認」ボタンクリック
        self.find_element(By.ID,"tab01_JAEF200100_btnKeikakuKakunin_button").click()
        
        # 48 納付計画詳細照会画面: 表示
        
        # 49 納付計画詳細照会画面: パンくず「分納誓約管理」クリック
        self.find_element_by_xpath("//*[@id='tab01_JAEF200200_navi']/li[4]/a").click()
        
        # 50 納付計画詳細照会画面: 表示
        
        # 51 分納誓約管理画面：分納誓約情報タブ: 「経過登録」ボタンクリック
        self.find_element(By.ID,"tab01_JAEF200100_btnKeikaReg_button").click()
        
        # 52 共通経過登録確認ダイアログ: 表示
        self.screen_shot("共通経過登録確認ダイアログ_50")
        
        # 53 共通経過登録確認ダイアログ: 「はい」ボタンクリック
        self.find_element(By.ID,"tempId__1").click()
        
        # 54 共通経過入力画面: 表示
        self.screen_shot("共通経過入力画面_52")
        
        # 55 共通経過入力画面: 「決定」ボタンクリック
        self.find_element(By.ID,"tab01_JADF200500_btnKettei_button").click()
        
        # 56 分納誓約管理画面: 表示
        self.screen_shot("分納誓約管理画面_54")
        
        # 57 分納誓約管理画面: 「登録」ボタンクリック
        self.find_element(By.ID,"tab01_JAEF200100_regbtn_button").click()
        
        # 58 分納誓約管理画面: 表示
        self.screen_shot("分納誓約管理画面_56")
        
        # 59 分納誓約管理画面: 「はい」ボタンクリック
        self.find_element(By.ID,"tempId__3").click()
        
        # 60 分納誓約管理画面: 表示
        # Assert: 「登録しました。」のメッセージのチェック
        self.screen_shot("分納誓約管理画面_58")
        
        # 61 分納誓約管理画面: 「印刷」ボタンクリック
        self.find_element(By.ID,"tab01_JAEF200100_printbtn_button").click()
        
        # 62 収納共通印刷指示画面: 表示
        self.screen_shot("収納共通印刷指示画面_60")
        
        # 63 収納共通印刷指示画面: 「印刷」ボタンクリック
        self.find_element(By.ID,"tab01_JAPF000800_printBtn_button").click()
        
        # 64 印刷確認ダイアログ: 表示
        self.screen_shot("印刷確認ダイアログ_62")
        
        # 65 印刷確認ダイアログ: 「はい」ボタンクリック
        self.find_element(By.ID,"tempId__5").click()
        time.sleep(2)
        
        # 66 印刷確認ダイアログ: No「1」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_WrBtnNo_1_1_button").click()
        
        # 67 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_65")
        
        # 68 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 69 PDF「納付誓約書」: 表示
        self.screen_shot("PDF「納付誓約書」_67")
        
        # 70 印刷確認ダイアログ: No「2」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_WrBtnNo_2_1_button").click()
        
        # 71 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_69")
        
        # 72 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 73 PDF「納付計画一覧表」: 表示
        self.screen_shot("PDF「納付計画一覧表」_71")
        
        # 74 印刷確認ダイアログ: No「3」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_WrBtnNo_3_1_button").click()
        
        # 75 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_73")
        
        # 76 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 77 PDF「納付計画書（計画明細）」: 表示
        self.screen_shot("PDF「納付計画書（計画明細）」_75")
        
        # 78 印刷確認ダイアログ: No「4」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_WrBtnNo_4_1_button").click()
        
        # 79 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_77")
        
        # 80 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 81 PDF「未納明細書」: 表示
        self.screen_shot("PDF「未納明細書」_79")
        
        # 82 印刷確認ダイアログ: No「5」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_WrBtnNo_5_1_button").click()
        
        # 83 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_81")
        
        # 84 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 85 PDF「納付書兼納税済通知書」: 表示
        self.screen_shot("PDF「納付書兼納税済通知書」_83")
        
