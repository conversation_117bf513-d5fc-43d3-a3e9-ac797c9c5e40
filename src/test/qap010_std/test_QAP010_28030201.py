
import datetime
from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010_28030201(KodomoSiteTestCaseBase):
    """TestQAP010_28030201"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params_list = case_data.get("sql_params")
        #システム日付を変数に設定
        date = datetime.date.today()
        sql_params_list["システム日付"] = format(date, '%Y%m%d')
        self.exec_sqlfile("test_QAP010_28030201_実施前スクリプト.sql", params=sql_params_list)
        super().setUp()
    
    # 退所申請について登録できることを確認する。
    def test_QAP010_28030201(self):
        """退所登録"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")

        # 3 メインメニュー画面: 「子ども子育て支援」ボタン押下       
        # 4 メインメニュー画面: 「入所管理」ボタン押下       
        # 5 メインメニュー画面: 「児童検索」ボタン押下
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        
        # 6 児童検索画面: 表示
        self.screen_shot("児童検索画面_6")
        
        # 7 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("AtenaCD"))
        
        # 8 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()

        # 9 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_9")
        
        # 10 児童台帳画面: 「入所管理」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 11 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_11")
        
        # 12 児童台帳画面: 「入所異動」ボタン押下
        # 13 入所管理（異動）画面: 表示
        self.click_button_by_label("入所異動")  
        self.wait_page_loaded()
        
        # 14 入所管理（異動）画面: 「修正」ボタン押下
        self.click_button_by_label("修正")  
        
        # 15 入所管理（異動）画面: 以下を入力申請年月日：決定年月日：退所年月日：
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAPF104100_txtIdoNaiyoShinseiYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF104100_txtIdoNaiyoShinseiYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF104100_txtIdoNaiyoShinseiYMD_textboxInput").send_keys(case_data.get("ShinseiYMD"))

        self.find_element_by_id(u"tab01_QAPF104100_txtIdoNaiyoKetteiYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF104100_txtIdoNaiyoKetteiYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF104100_txtIdoNaiyoKetteiYMD_textboxInput").send_keys(case_data.get("KetteiYMD"))

        self.find_element_by_id(u"tab01_QAPF104100_txtIdoNaiyoTaishoYMD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF104100_txtIdoNaiyoTaishoYMD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF104100_txtIdoNaiyoTaishoYMD_textboxInput").send_keys(case_data.get("TaishoYMD"))
        
        # 16 入所管理（異動）画面: 「登録」ボタン押下
        self.find_element_by_id(u"tab01_QAPF104100_fldIdoNaiyoBiko_textarea").click()
        self.click_button_by_label("登録")
        
        # 17 入所管理（異動）画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        
        # 18 入所管理（異動）画面: 登録確認
        # Assert: 登録しました。を確認する。
        self.screen_shot("入所管理（異動）画面_18")
        
        # 19 入所管理（異動）画面: パンくずリスト「児童台帳」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_QAPF104100_navi']/li[2]/a").click()
        
        # 20 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_20")
        
        # 児童検索画面を閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 22 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_22")
        
        # 23 メインメニュー画面: 「バッチ管理」ボタン押下        
        # 24 メインメニュー画面: 「即時実行」ボタン押下        
        # 25 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # 26 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_26")
        
        # 27 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：入所処理名：一括退所処理       
        # 28 スケジュール個別追加画面: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM_1 = case_data.get("ShoriNM_1")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM_1)
       
        # 29 スケジュール個別追加画面: 「No.1」ボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()
        
        # 30 実行指示画面: 表示
        self.screen_shot("実行指示画面_30")
        
        # 31 実行指示画面: 以下を入力基準日：抽出区分：入所希望期間終了
        # Assert: パラメータ化
        params = [
            {"title":"基準日", "type": "text", "value": case_data.get("基準日")},
            {"title":"抽出区分", "type": "select", "value": case_data.get("抽出区分")}
        ]
        self.set_job_param_kodomo(params)
        
        # 32 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 33 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_33")
        
        # 34 実行結果管理画面: 「No.1」ボタン押下
        # 35 結果確認画面: 表示
        # self.screen_shot("結果確認画面_35")
        # 36 結果確認画面: 「納品物確認」ボタン押下
        # 37 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_37")
        # 38 納品物管理画面: 「ダウンロード」ボタンを押下
        # 39 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_39")
        # 40 ファイルダウンロード画面: 「No.1」ボタン押下
        # 41 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 42 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_42")
        # 43 PDF: ×ボタン押下で閉じる
        
        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：入所処理名：一括退所処理 
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_1)
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("一括退所対象者一覧.csv", "ファイルダウンロード画面_39")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # 44 ファイルダウンロード画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li/a").click()
        self.wait_page_loaded()
        
        # 45 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()
        
        # 46 実行指示画面: 表示
        self.screen_shot("実行指示画面_46")
        
        # 47 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 48 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_48")
        
        # 49 実行結果管理画面: 「No.1」ボタン押下
        # 50 結果確認画面: 表示
        # self.screen_shot("結果確認画面_50")
        # 51 結果確認画面: 「納品物確認」ボタン押下
        # 52 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_52")
        # 53 納品物管理画面: 「ダウンロード」ボタンを押下
        # 54 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_54")
        # 55 ファイルダウンロード画面: 「No.1」ボタン押下
        # 56 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 57 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_57")
        # 58 PDF: ×ボタン押下で閉じる

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：入所処理名：一括退所処理 
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_1)
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("一括退所対象者一覧.csv", "ファイルダウンロード画面_54")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # スケジュール個別タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 59 メインメニュー画面: 「子ども子育て支援」ボタン押下       
        # 60 メインメニュー画面: 「入所管理」ボタン押下       
        # 61 メインメニュー画面: 「児童検索」ボタン押下
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        
        # 62 児童検索画面: 表示
        self.screen_shot("児童検索画面_62")
        
        # 63 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("AtenaCD_一括退所"))
        
        # 64 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        
        # 65 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_65")
        
        # 66 児童台帳画面: 「入所管理」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 67 児童台帳画面: 表示
        # Assert: 対象児童の入所管理情報が「退所」となっていること。
        self.screen_shot("児童台帳画面_67")
        
        # 69 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_69")
        
        # 児童検索タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 70 メインメニュー画面: 「バッチ管理」ボタン押下       
        # 71 メインメニュー画面: 「即時実行」ボタン押下       
        # 72 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # 73 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_73")
        
        # 74 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：入所処理名：卒園処理
        # 75 スケジュール個別追加画面: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM_2 = case_data.get("ShoriNM_2")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM_2)
        
        # 76 スケジュール個別追加画面: 「No.1」ボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()
        
        # 77 実行指示画面: 表示
        self.screen_shot("実行指示画面_77")
        
        # 78 実行指示画面: 以下を入力対象年度：
        # Assert: パラメータ化
        params = [
            {"title":"対象年度", "type": "text", "value": case_data.get("対象年度")}
        ]
        self.set_job_param_kodomo(params)
        
        # 79 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 80 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_80")
        
        # 81 実行結果管理画面: 「No.1」ボタン押下
        # 82 結果確認画面: 表示
        # self.screen_shot("結果確認画面_82")
        # 83 結果確認画面: 「納品物確認」ボタン押下
        # 84 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_84")
        # 85 納品物管理画面: 「ダウンロード」ボタンを押下
        # 86 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_86")
        # 87 ファイルダウンロード画面: 「No.1」ボタン押下
        # 88 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 89 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_89")
        # 90 PDF: ×ボタン押下で閉じる

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：入所処理名：卒園処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_2)
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("卒園対象外者確認一覧.csv", "ファイルダウンロード画面_86")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 91 ファイルダウンロード画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li/a").click()
        self.wait_page_loaded()
        
        # 92 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()

        # 93 実行指示画面: 表示
        self.screen_shot("実行指示画面_93")
        
        # 94 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()

        # 95 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_95")
        
        # 96 実行結果管理画面: 「No.1」ボタン押下
        # 97 結果確認画面: 表示
        # self.screen_shot("結果確認画面_97")
        # 98 結果確認画面: 「納品物確認」ボタン押下
        # 99 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_99")
        # 100 納品物管理画面: 「ダウンロード」ボタンを押下
        # 101 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_101")
        # 102 ファイルダウンロード画面: 「No.1」ボタン押下
        # 103 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 104 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_104")
        # 105 PDF: ×ボタン押下で閉じる

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：入所処理名：卒園処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_2)
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("卒園対象外者確認一覧.csv", "ファイルダウンロード画面_101")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # スケジュール個別タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 106 児童検索画面: 住民コード：
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("AtenaCD_卒園"))
        
        # 107 児童検索画面: 「検索」ボタン押下
        self.driver.set_window_size(1450,2000)
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        
        # 108 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_108")
        
        # 109 児童台帳画面: 「入所管理」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 110 児童台帳画面: 表示
        # Assert: 対象児童の入所管理情報が「退所」となっていること。
        self.screen_shot("児童台帳画面_110")
        
        # 児童検索タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 112 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_112")
        
        # 113 メインメニュー画面: 「バッチ管理」ボタン押下       
        # 114 メインメニュー画面: 「即時実行」ボタン押下        
        # 115 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # 116 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_116")
        
        # 117 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：入所処理名：一括転園処理   
        # 118 スケジュール個別追加画面: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM_3 = case_data.get("ShoriNM_3")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM_3)
        
        # 119 スケジュール個別追加画面: 「No.1」ボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()

        # 120 実行指示画面: 表示
        self.screen_shot("実行指示画面_120")
        
        # 121 実行指示画面: 以下を入力旧施設コード：新施設コード：申請年月日：決定年月日：退所年月日：
        # Assert: パラメータ化
        params = [
            {"title":"旧施設コード", "type": "text", "value": case_data.get("旧施設コード")},
            {"title":"新施設コード", "type": "text", "value": case_data.get("新施設コード")},
            {"title":"申請年月日", "type": "text", "value": case_data.get("申請年月日")},
            {"title":"決定年月日", "type": "text", "value": case_data.get("決定年月日")},
            {"title":"退所年月日", "type": "text", "value": case_data.get("退所年月日")}
        ]
        self.set_job_param_kodomo(params)
        
        # 122 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 123 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_123")
        
        # 124 実行結果管理画面: 「No.1」ボタン押下
        # 125 結果確認画面: 表示
        # self.screen_shot("結果確認画面_125")
        # 126 結果確認画面: 「納品物確認」ボタン押下
        # 127 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_127")
        # 128 納品物管理画面: 「ダウンロード」ボタンを押下
        # 129 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_129")
        # 130 ファイルダウンロード画面: 「No.1」ボタン押下
        # 131 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 132 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_132")
        # 133 PDF: ×ボタン押下で閉じる

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：入所処理名：一括転園処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_3)
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("一括転園対象者一覧.csv", "ファイルダウンロード画面_129")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 134 ファイルダウンロード画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        
        # 135 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()
        
        # 136 実行指示画面: 表示
        self.screen_shot("実行指示画面_136")
        
        # 137 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        
        # 138 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_138")
        
        # 139 実行結果管理画面: 「No.1」ボタン押下
        # 140 結果確認画面: 表示
        # self.screen_shot("結果確認画面_140")
        # 141 結果確認画面: 「納品物確認」ボタン押下
        # 142 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_142")
        # 143 納品物管理画面: 「ダウンロード」ボタンを押下
        # 144 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_144")
        # 145 ファイルダウンロード画面: 「No.1」ボタン押下
        # 146 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 147 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_147")
        # 148 PDF: ×ボタン押下で閉じる
        
        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：入所処理名：一括転園処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_3)
        
        # TODO ジョブ異常終了
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("", "ファイルダウンロード画面_144")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # スケジュール個別タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()

        # 149 児童検索画面: 住民コード：
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("AtenaCD_一括転園"))
        
        # 150 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        
        # 151 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_151")
        
        # 152 児童台帳画面: 「入所管理」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        
        # 153 児童台帳画面: 表示
        # Assert: 対象児童が旧施設は「退所」、新施設は「入所」となっていること。
        self.screen_shot("児童台帳画面_153")
        
