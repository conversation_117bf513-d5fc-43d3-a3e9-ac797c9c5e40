import time
import unittest
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAP010_28070701(KodomoSiteTestCaseBase):
    """TestQAP010_28070701"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()
    
    # 過誤情報を抽出することができることを確認する。
    def test_QAP010_28070701(self):
        """過誤情報抽出"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")
        
        # 3 メインメニュー画面: 「収納」クリック
        self.find_element_by_xpath("//*[@id='rootmenu_0']/li[contains(span,'収納')]/span").click()
        time.sleep(1)
        
        # 4 メインメニュー画面: 「収納消込結果」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'収納')]/ul/li[contains(span,'収納消込結果')]/span").click()
        time.sleep(1)
        
        # 5 メインメニュー画面: 「累積不一致」ダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'収納')]/ul/li[contains(span,'収納消込結果')]/ul/li[contains(span,'累積不一致')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)
        
        # 6 （累積不一致）検索条件入力画面: 表示
        self.screen_shot("（累積不一致）検索条件入力画面_5")
        
        # 7 （累積不一致）検索条件入力画面: 本税（料）：本税（料）　を選択
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JAAF301700_selRuisekiFuitchiZeiKbn_select"),"本税（料）")
        
        # 8 （累積不一致）検索条件入力画面: 不一致区分：過誤納　を選択
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JAAF301700_selRuisekiFuitchiJotaiKbn_select"),"過誤納")
        
        # 9 （累積不一致）検索条件入力画面: 処理状態：未処理　を選択
        # Assert: 記載のないパラメータは全て初期値を選択
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JAAF301700_selHuitiShoriJoutai_select"),"未処理")
        
        # 10 （累積不一致）検索条件入力画面: 「検索（Enter）」ボタンクリック
        self.find_element(By.ID,"tab01_JAAF301700_WrCmnBtn05_button").click()
        
        # 11 累積不一致一覧画面: 表示
        
        # 12 累積不一致一覧画面: 「修正」ボタンクリック
        self.find_element(By.ID,"tab01_JAAF301800_btnEditChg_button").click()
        
        # 13 累積不一致一覧画面: 「印刷」ボタンクリック
        self.find_element(By.ID,"tab01_JAAF301800_printbtn_button").click()
        
        # 14 収納共通印刷指示画面: 表示
        time.sleep(1)
        self.screen_shot("収納共通印刷指示画面_14")
        
        # 15 収納共通印刷指示画面: 「印刷」ボタンクリック
        self.find_element(By.ID,"tab01_JAPF001000_printBtn_button").click()
        
        # 16 印刷確認ダイアログ: 表示
        self.screen_shot("印刷確認ダイアログ_15")
        
        # 17 印刷確認ダイアログ: 「はい」ボタンクリック
        self.find_element(By.ID,"tempId__1").click()
        
        # 18 印刷確認ダイアログ: No「1」ボタンクリック
        time.sleep(15)
        self.find_element_by_xpath("//*[@id='tab01_ZZZ000000_WrBtnNo_1_1_button']").click()
        
        # 19 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_18")
        
        # 20 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 21 PDF「累積不一致一覧表」: 表示
        time.sleep(5)
        self.screen_shot("PDF「累積不一致一覧表」_20")
        
        # 22 印刷確認ダイアログ: 「閉じる」ボタンクリック
        self.click_button_by_label("閉じる")
        
        # 23 収納共通印刷指示画面: パンくず「累積不一致一覧」クリック
        self.find_element_by_xpath("//*[@id='tab01_JAPF001000_navi']/li[2]/a").click()
        
        # 24 累積不一致一覧画面: 表示
        
        # 25 累積不一致一覧画面: 「ファイル出力」ボタンクリック
        self.find_element(By.ID,"tab01_JAAF301800_btnFILEOutput_button").click()
        
        # 26 印刷確認ダイアログ: 表示
        self.screen_shot("印刷確認ダイアログ_25")
        
        # 27 印刷確認ダイアログ: 「はい」ボタンクリック
        
        # 28 印刷確認ダイアログ: No「1」ボタンクリック
        self.wait_page_loaded()
        time.sleep(1)
        self.driver.switch_to.frame(1)            
        self.find_element_by_xpath('//*[@id="FILE_LIST"]/table/tbody/tr/td[1]/input').click()
        time.sleep(2)
        self.driver.switch_to.default_content()
        
        # 29 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_28")
        
        # 30 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 31 CSV「累積不一致一覧ファイル」: 表示
        self.screen_shot("CSV「累積不一致一覧ファイル」_30")
        
        # 32 印刷確認ダイアログ: 「閉じる」ボタンクリック
        self.click_button_by_label("閉じる")
        
        # そのまま終了すると排他が掛かるので、照会ボタン押下
        self.find_element(By.ID,"tab01_JAAF301800_btnEditChg_button").click()
        
