import time
import unittest
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAP010_28070601(KodomoSiteTestCaseBase):
    """TestQAP010_28070601"""

    def setUp(self):
#        self.exec_sqlfile("QAP010_28070601_実行前スクリプト.sql")
        super().setUp()
    
    # 督促状発布対象者の抽出ができることを確認する。
    def test_QAP010_28070601(self):
        """対象者抽出"""
        driver = None
        case_data = self.test_data["case28070601"]

        self.do_login()
        # 1 メインメニュー画面: 「バッチ管理」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[2]/span").click()
        time.sleep(1)
        # 2 メインメニュー画面: 「即時実行」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[2]/ul/li[3]/span").click()
        time.sleep(1)
        # 3 メインメニュー画面: 「スケジュール個別追加」ダブルクリック
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[2]/ul/li[3]/ul/li[1]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)
        # 4 スケジュール個別追加画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加画面_4" , True)
        
        # 5 スケジュール個別追加画面: 業務名：収納・滞納 サブシステム名：日次 処理名：日次消込処理 処理区分：
        self.form_input_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select" , text="収納・滞納")
        self.form_input_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select" , text="児童")
        self.form_input_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select" , text="日次消込処理")
        # 6 スケジュール個別追加画面: 「検索」ボタンクリック
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()
        # 7 スケジュール個別追加画面: 「(JAABN06400) 日次消込本処理」Noボタンクリック
        # Assert: 後続テストのための事前準備として処理を実施
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_3_4_button").click()
        # 8 実行指示画面: 表示
        self.save_screenshot_migrate(driver, "実行指示画面_8" , True)
        
        # 9 実行指示画面: 取扱業務：（パラメータ化）　を選択
        toriatsukai_gyomu =   case_data.get("toriatsukai_gyomu")
        self.form_input_by_id("tab01_ZEAF002200_groupingKBN_select" , text=toriatsukai_gyomu)
        self.save_screenshot_migrate(driver, "実行指示画面_9" , True)

        # 10 実行指示画面: 「実行」ボタンクリック
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        # 11 実行結果管理画面: 表示
        # Assert: 「実行しました。」のメッセージのチェック
        self.save_screenshot_migrate(driver, "実行指示画面_11" , True)
        
        # 12 実行結果管理画面: 正常終了まで5分置きに「実行」ボタンクリック
        self.wait_job_finished_kodomo(3600, 20)
        self.save_screenshot_migrate(driver, "実行結果管理画面_12" , True)
        
        # 13 実行結果管理画面: パンくず「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li/a").click()
        # 14 スケジュール個別追加画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加画面_14" , True)
        
        # 15 スケジュール個別追加画面: 業務名：宛名情報管理 サブシステム名：宛名 処理名：宛名・口座バッチマスタ作成処理 処理区分：
        self.form_input_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select" , text="宛名情報管理")
        self.form_input_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select" , text="宛名")
        self.form_input_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select" , text="宛名・口座バッチマスタ作成処理")
        # 16 スケジュール個別追加画面: 「検索」ボタンクリック
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()
        # 17 スケジュール個別追加画面: 「 (FAABN00400) 宛名・口座バッチマスタ作成処理 」Noボタンクリック
        # Assert: 後続テストのための事前準備として処理を実施
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()
        # 18 実行指示画面: 表示
        self.save_screenshot_migrate(driver, "実行指示画面_18" , True)
        
        # 19 実行指示画面: 業務名〇：初期値
        # 20 実行指示画面: 業務別基準日〇：初期値
        # 21 実行指示画面: 口座基準日〇：初期値
        self.save_screenshot_migrate(driver, "実行指示画面_21" , True)
        
        # 22 実行指示画面: 「実行」ボタンクリック
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        # 23 実行結果管理画面: 表示
        # Assert: 「実行しました。」のメッセージのチェック
        self.save_screenshot_migrate(driver, "実行結果管理画面_23" , True)
        
        # 24 実行結果管理画面: 正常終了まで10分置きに「実行」ボタンクリック
        self.wait_job_finished_kodomo(3600, 20)
        self.save_screenshot_migrate(driver, "実行結果管理画面_24" , True)

        # 25 実行結果管理画面: パンくず「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li/a").click()
        # 26 スケジュール個別追加画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加画面_26" , True)
        # 27 スケジュール個別追加画面: 業務名：収納・滞納サブシステム名：マスタ作成処理名：収納マスタ＆収納用宛名口座マスタ作成処理（統合版）処理区分：
        self.form_input_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select" , text="収納・滞納")
        self.form_input_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select" , text="児童")
        self.form_input_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select" , text="収納マスタ＆収納用宛名口座マスタ作成処理（統合版）")
        # 28 スケジュール個別追加画面: 「検索」ボタンクリック
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()
        # 29 スケジュール個別追加画面: 「(JAZBN10100) 収納マスタ＆収納用宛名口座マスタ作成本処理(統合版) 」Noボタンクリック
        # Assert: 後続テストのための事前準備として処理を実施
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()
        # 30 実行指示画面: 表示
        self.save_screenshot_migrate(driver, "実行指示画面_30" , True)
        
        # 31 実行指示画面: 未納指定：全件　を選択
        # Assert: 記載のないパラメータは全て初期値を選択
        self.form_input_by_id("tab01_ZEAF002200_JAZBN00400_minoushitei_select" , text="全件")
        self.save_screenshot_migrate(driver, "実行指示画面_31" , True)
        # 32 実行指示画面: 「実行」ボタンクリック
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        # 33 実行結果管理画面: 表示
        # Assert: 「実行しました。」のメッセージのチェック
        self.save_screenshot_migrate(driver, "実行結果管理画面_33" , True)
        
        # 34 実行結果管理画面: 正常終了まで10分置きに「実行」ボタンクリック
        self.wait_job_finished_kodomo(3600, 20)
        self.save_screenshot_migrate(driver, "実行結果管理画面_34" , True)
        
        # 35 実行結果管理画面: パンくず「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li/a").click()
        # 36 スケジュール個別追加画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加画面_36" , True)
        # 37 スケジュール個別追加画面: 業務名：収納・滞納サブシステム名：月次処理名：督促状作成処理処理区分：
        self.form_input_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select" , text="収納・滞納")
        self.form_input_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select" , text="児童")
        self.form_input_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select" , text="督促状作成処理")
        # 38 スケジュール個別追加画面: 「検索」ボタンクリック
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()
        # 39 スケジュール個別追加画面: 「(JACBN00400) 督促状作成本処理」Noボタンクリック
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()
        # 40 実行指示画面: 表示
        self.save_screenshot_migrate(driver, "実行指示画面_40" , True)
        
        # 41 実行指示画面: 科目：(パラメータ化)　を選択調定発生年度開始：(パラメータ化)　を選択調定発生年度終了：(パラメータ化)　を選択納期限（開始）：(パラメータ化)　を入力納期限（終了）：(パラメータ化)　を入力発送日：(パラメータ化)　を入力収納現在日：(パラメータ化)　を入力
        # 0:普徴、1:特徴、2:法人、3:軽自、4:固定、5:事業、6:償却、7:病児、8:夜間、9:休保、10:介普、11:介特
        # 12:国特、13:年特、14:後普、15:後特、16:鉱山、17:入湯、18:保有、19:市た、20:特保、21:保育、22:延保、23:主食
        # 24:学童、25:一時、26:副食、27:学延、28:休保、29:共済、30:入園、31:教材、32:バス、33:延保、34:主食
        kamoku_list = case_data.get("kamoku")
        choteiHasseiNendoStr = case_data.get("choteiHasseiNendoStr")
        choteiHasseiNendoEnd = case_data.get("choteiHasseiNendoEnd")
        nokigenStr = case_data.get("nokigenStr")
        nokigenEnd = case_data.get("nokigenEnd")
        hassobi = case_data.get("hassobi")
        shiharaikigen = case_data.get("hassobi")
        shunogenzaibi = case_data.get("shiharaikigen")
        for kamoku in kamoku_list:
            self.find_element_by_id("tab01_ZEAF002200_gyomuCodechk" + kamoku).click()
        self.form_input_by_id("tab01_ZEAF002200_JACBN00200_TyoteihasseiNendoST_select" , text=choteiHasseiNendoStr)
        self.form_input_by_id("tab01_ZEAF002200_JACBN00200_TyoteihasseiNendoEN_select" , text=choteiHasseiNendoEnd)
        self.find_element_by_id("tab01_ZEAF002200_JACBN00200_NoukigenST_textboxInput"). send_keys(nokigenStr)
        self.find_element_by_id("tab01_ZEAF002200_JACBN00200_NoukigenEN_textboxInput"). send_keys(nokigenEnd)
        self.find_element_by_id("tab01_ZEAF002200_JACBN00200_Hassoubi_textboxInput"). send_keys(hassobi)
        self.find_element_by_id("tab01_ZEAF002200_JACBN00200_Genzaibi_Nohukakuninbi_textboxInput"). send_keys(shunogenzaibi)
        self.find_element_by_id("tab01_ZEAF002200_JACBN00200_Shiteibi_Shiharaikigen_textboxInput"). send_keys(shiharaikigen)
        # 42 実行指示画面: 対象納期以前未納出力要否：出力しない　を選択
        self.find_element_by_id("tab01_ZEAF002200_JACBN00200_Genzaibi_Nohukakuninbi_textboxInput").send_keys(Keys.TAB)
        self.form_input_by_id("tab01_ZEAF002200_JACBN00200_taisyoukiizennCyuusyutukuBunn_select" , text="出力しない")
        self.save_screenshot_migrate(driver, "実行指示画面_42" , True)
        
        # 43 実行指示画面: 「実行」ボタンクリック
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        # 44 実行結果管理画面: 表示
        # Assert: 「実行しました。」のメッセージのチェック
        self.save_screenshot_migrate(driver, "実行結果管理画面_44" , True)
        
        # 45 実行結果管理画面: 正常終了まで10分置きに「実行」ボタンクリック
        self.wait_job_finished_kodomo(3600, 20)
        self.save_screenshot_migrate(driver, "実行結果管理画面_45" , True)
        
        # 46 メインメニュー画面: 「納品物確認」ボタンダブルクリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[2]/ul/li[2]/span").click()
        time.sleep(1)
        menu = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[2]/ul/li[2]/ul/li[2]/span")
        self.actionChains.double_click(menu).perform()
        time.sleep(1)
        # 47 納品物管理画面: 表示
#        self.save_screenshot_migrate(driver, "納品物管理画面_47" , True)

        # 48 納品物管理画面: 業務名：収納・滞納 サブシステム名：月次 処理名：督促状作成処理 処理区分：
        self.form_input_by_id("tab02_ZEAF002700_SelKensakuGyomuNM_select" , text="収納・滞納")
        self.form_input_by_id("tab02_ZEAF002700_SelKensakuSubSystemNM_select" , text="児童")
        self.form_input_by_id("tab02_ZEAF002700_SelKensakuSyoriNM_select" , text="督促状作成処理")
        # 49 納品物管理画面: 「検索」ボタンクリック
        self.find_element_by_id("tab02_ZEAF002700_BtnKensaku_button").click()

        # 50 納品物管理画面: 「JACP100700_督促状発付一覧表.pdf」の「ダウンロード」ボタンクリック
#        self.find_element_by_id("tab02_ZZZ000000_BtnDownload_2_9_button").click()
        # 51 ファイルダウンロード確認ダイアログ: 表示
#        self.driver.switch_to.frame(1)
        # 52 ファイルダウンロード確認ダイアログ: 「JACP100700_督促状発付一覧表.pdf」のNoボタンクリック
        # 53 ファイルダウンロード確認ダイアログ: 「ファイルを開く」ボタンクリック
        # 54 ファイルダウンロード確認ダイアログ: 表示
        self.pdf_download("JACP100700_督促状発付一覧表.pdf", "ファイルダウンロード確認ダイアログ_54")
        # 55 ファイルダウンロード確認ダイアログ: 閉じるボタンクリック
        self.click_button_by_label("閉じる")

        # 56 納品物管理画面: 「JACP100900_督促状発付不能一覧表.CSV」の「ダウンロード」ボタンクリック
        #self.find_element_by_id("tab02_ZZZ000000_BtnDownload_1_9_button").click()
        # 57 ファイルダウンロード確認ダイアログ: 表示
        # 58 ファイルダウンロード確認ダイアログ: 「JACP100900_督促状発付不能一覧表.CSV」のNoボタンクリック
        # 59 ファイルダウンロード確認ダイアログ: 「ファイルを開く」ボタンクリック
        # 60 ファイルダウンロード確認ダイアログ: 表示
        self.pdf_download("JACP100900_督促状発付不能一覧表.CSV", "ファイルダウンロード確認ダイアログ_60")

        # 61 ファイルダウンロード確認ダイアログ: 閉じるボタンクリック
        self.click_button_by_label("閉じる")

        # データ戻す
#        self.exec_sqlfile("QAP010_28070601_実行後スクリプト.sql")
        
