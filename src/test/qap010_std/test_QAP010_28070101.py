from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010_28070101(KodomoSiteTestCaseBase):
    """TestQAP010_28070101"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params_list = case_data.get("sql_params")
        self.exec_sqlfile("test_QAP010_28070101_実施前スクリプト.sql", params=sql_params_list)
        super().setUp()
    
    # 調定対象者の抽出が行ることを確認する。
    def test_QAP010_28070101(self):
        """対象者抽出"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        self.do_login()
        
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「バッチ管理」ボタン押下
        # 4 メインメニュー画面: 「即時実行」ボタン押下
        # 5 メインメニュー画面: 「スケジュール個別追加」ボタンをダブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # 6 バッチ管理画面: 業務名：子ども子育て支援サブシステム名：賦課処理名：月次賦課処理
        # 7 バッチ管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名"), case_data.get("サブシステム名"),case_data.get("処理名"))
        self.screen_shot("バッチ管理画面_6")
        
        # 8 バッチ起動画面: 月次賦課処理「(QP7BN00280) 整合性チェック」のNoボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()

        # 9 バッチ起動画面: 所管区：                               　を選択支所：                               　を選択開始年月：                               　を入力終了年月：                               　を入力チェック項目設定：全項目                               　を選択並び順：世帯台帳番号、児童_宛名C、対象年月、影響する科目　を選択
        # Assert: パラメータ化
        params = [
            {"title":"所管区", "type": "select", "value": case_data.get("所管区")},
            {"title":"支所", "type": "select", "value": case_data.get("支所")},
            {"title":"開始年月", "type": "text", "value": case_data.get("開始年月")},
            {"title":"終了年月", "type": "text", "value": case_data.get("終了年月")},
            {"title":"チェック項目設定", "type": "select", "value": case_data.get("チェック項目設定")},
            {"title":"並び順", "type": "select", "value": case_data.get("並び順")},
        ]
        self.set_job_param_kodomo(params)
        self.wait_page_loaded()
        
        self.screen_shot("バッチ起動画面_9")
        self.click_button_by_label("実行")
        self.wait_page_loaded()
        
        # 10 実行結果画面: 表示
        # Assert: 「(QP7BN00280) 整合性チェック」の状態が正常終了となることを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)    
        self.screen_shot("実行結果画面_10")
        
        # # 11 実行結果画面: 「No1」ボタン押下
        # self.click_button_by_label("1")
    
        # # 12 結果確認画面: 表示
        # self.screen_shot("結果確認画面_12")

        # 13 結果確認画面: 表示
        
        # 14 納品物管理画面: 整合性チェック_エラー.csv「ダウンロード」ボタン押下
        # self.screen_shot("納品物管理画面_14")

        # 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：賦課 処理名：月次賦課処理  処理区分：
        # 納品物管理画面: 「検索」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        self.kennsakujyoukenn_nyuuryoku_confirm("2","子ども子育て支援", "賦課", "月次賦課処理")

        # 納品物管理画面: 「整合性チェック_エラー.csv」の「ダウンロード」ボタン押下
        # ファイルダウンロード画面: 表示
        # ファイルダウンロード画面: 「整合性チェック_エラー.csv」のNoボタン押下
        self.pdf_download("整合性チェック_エラー.csv","納品物管理画面_14")

        # ファイルダウンロード画面: 閉じるボタン押下
        self.click_button_by_label("閉じる")

        # 納品物管理画面: 「納品物管理」タブ×ボタン押下
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()

        # 15 ファイルダウンロード: 「No1」ボタン押下
        # 16 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 17 帳票（CSV）: 表示
        # self.screen_shot("帳票（CSV）_17")
        # 18 帳票（CSV）: ×ボタン押下でCSVファイルを閉じる
        # 19 ファイルダウンロード: 「閉じる」ボタン押下

        # 20 バッチ管理画面: パンくずリストの「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        
        # 21 バッチ管理画面: 業務名：子ども子育て支援サブシステム名：賦課処理名：月次賦課処理
        # 22 バッチ管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名"), case_data.get("サブシステム名"),case_data.get("処理名"))
        self.screen_shot("バッチ管理画面_21")
        
        # 23 バッチ起動画面: 月次賦課処理「(QP7BN00210) 月次賦課計算処理」のNoボタン押下
        self.click_button_by_label("2")
        
        # 24 バッチ起動画面: 対象年月：を入力 対象年度：を入力 基準年月：を入力
        # Assert: パラメータ化
        params = [
            {"title":"対象年月", "type": "text", "value": case_data.get("対象年月")},
            {"title":"対象年度", "type": "text", "value": case_data.get("対象年度")},
            {"title":"基準年月", "type": "text", "value": case_data.get("基準年月")},
        ]
        self.set_job_param_kodomo(params)
        self.screen_shot("バッチ起動画面_24")
        self.wait_page_loaded()
        self.click_button_by_label("実行")
        
        # 25 実行結果画面: 表示
        # Assert: 「(QP7BN00210) 月次賦課計算処理」の状態が正常終了となることを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds) 
        self.screen_shot("実行結果画面_25")
        
        # 26 実行結果画面: 「No1」ボタン押下
        self.click_button_by_label("1")
        
        # 27 結果確認画面: 表示
        self.screen_shot("結果確認画面_27")
        
