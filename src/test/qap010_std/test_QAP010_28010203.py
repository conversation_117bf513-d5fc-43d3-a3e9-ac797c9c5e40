import time
import unittest
from datetime import datetime, date, timedelta
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.select import Select
class TestQAP010_28010203(KodomoSiteTestCaseBase):
    """TestQAP010_28010203"""

    def setUp(self):
        settings = self.common_test_data
        self.test_data = settings.get("test_qap010_28010203")
        super().setUp()
    
    # 教育・保育給付認定変更申請にかかわる変更通知書の出力ができることを確認する。
    def test_QAP010_28010203(self):
        """変更通知書作成"""
        
        driver = None

        self.do_login()
        # 1 メインメニュー　画面: 表示
        self.save_screenshot_migrate(driver, "メインメニュー 画面_1" , True)
        
        # 2 メインメニュー　画面: 「バッチ管理」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        time.sleep(1)

        # 3 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 4 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)
        
        # 5 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '３号→２号切替処理']").click()

        # 6 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()
        
        # 7 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_7" , True)
        
        # 8 スケジュール個別追加　画面: 「№4」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_4_4_button").click()
        
        # 9 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_9" , True)
        
        # 10 実行指示　画面: 施設コード、児童宛名コード、発行年月日　を入力
        # Assert: パラメータ化
        # 施設コード
        self.find_element_by_id("tab01_ZEAF002200_Paratest23_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest23_textboxInput").send_keys(self.test_data.get("case_qap001_shisetsu_code"))
        # 児童宛名コード
        self.find_element_by_id("tab01_ZEAF002200_Paratest183_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest183_textboxInput").send_keys(self.test_data.get("case_qap001_jidou_atena_code"))
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest605_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest605_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest605_textboxInput").send_keys(formatted_date)

        # 11 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_11" , True)
        
        # 12 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 13 実行結果管理　画面: 表示
        # Assert: 「（QP1BN03300）支給認定職権変更通知書出力処理」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_13" , True)
        
        # 14 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 15 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_15" , True)
        
        # 16 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()
        
        # 17 ファイルダウンロード: 「1」ボタン押下
        # 18 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 19 ログ: 表示
        # 20 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_19" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 21 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()
        
        # 22 納品物管理　画面: 支給認定職権変更通知書.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()
        
        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：３号→２号切替処理 
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_1 = self.test_data.get("ShoriNM_1")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_1)

        # 23 ファイルダウンロード: 「1」ボタン押下
        # 24 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 25 帳票（PDF）: 表示
        # 26 帳票（PDF）: 閉じる
        self.pdf_download("QAPP160300_支給認定職権変更通知書.pdf", "帳票（PDF）_25")
        self.click_button_by_label("閉じる")
        time.sleep(2)       
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 27 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_27" , True)

        # 28 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # 29 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)
        
        # 30 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 31 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定変更決定通知書_一覧出力処理']").click()

        # 32 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()
        
        # 33 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_33" , True)
        
        # 34 スケジュール個別追加　画面: 「№2」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_2_4_button").click()
        
        # 35 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_35" , True)
        
        # 36 実行指示　画面: 施設コード、児童宛名コード　を入力
        # Assert: パラメータ化
        # 施設コード
        self.find_element_by_id("tab01_ZEAF002200_Paratest23_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest23_textboxInput").send_keys(self.test_data.get("case_qap001_shisetsu_code"))
        # 児童宛名コード
        self.find_element_by_id("tab01_ZEAF002200_Paratest58_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest58_textboxInput").send_keys(self.test_data.get("case_qap001_jidou_atena_code"))
        
        # 37 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_37" , True)
        
        # 38 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 39 実行結果管理　画面: 表示
        # Assert: 「（QP1BN03100）支給認定変更決定通知書出力」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_39" , True)
        
        # 40 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 41 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_41" , True)
        
        # 42 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()
        
        # 43 ファイルダウンロード: 「1」ボタン押下
        # 44 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 45 ログ: 表示
        # 46 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_45" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 47 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()
        
        # 48 納品物管理　画面: QAPP111400_教育・保育給付認定変更通知書.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定変更決定通知書_一覧出力処理 
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_2 = self.test_data.get("ShoriNM_2")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_2)

        # 49 ファイルダウンロード: 「1」ボタン押下
        # 50 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 51 帳票（PDF）: 表示
        # 52 帳票（PDF）: 閉じる
        self.pdf_download("QAPP111400_教育・保育給付認定変更通知書.pdf", "帳票（PDF）_51")
        self.click_button_by_label("閉じる")
        time.sleep(2)       
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 53 メインメニュー　画面: 表示
        #self.screen_shot("メインメニュー 画面_53")
        
        # 54 メインメニュー　画面: 「バッチ管理」ボタン押下
        
        # 55 メインメニュー　画面: 「即時実行」ボタンを押下
        
        # 56 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        
        # 57 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        
        # 58 スケジュール個別追加　画面: 「検索」ボタン押下
        
        # 59 スケジュール個別追加　画面: 表示
        #self.screen_shot("スケジュール個別追加 画面_59")
        
        # 60 スケジュール個別追加　画面: 「№2」ボタン押下
        
        # 61 実行指示　画面: 表示
        #self.screen_shot("実行指示 画面_61")
        
        # 62 実行指示　画面: 所管区、支所、出力帳票　を選択
        # Assert: パラメータ化
        
        # 63 実行指示　画面: 「入力チェック」ボタン押下
        #self.screen_shot("実行指示 画面_63")
        
        # 64 実行指示　画面: 「実行」ボタン押下
        
        # 65 実行結果管理　画面: 表示
        # Assert: 「（QP1BN04600）教育・保育給付認定変更通知書出力」の状態が正常終了することを確認
        #self.screen_shot("実行結果管理 画面_65")
        
        # 66 実行結果管理　画面: 「№」ボタン押下
        
        # 67 結果確認　画面: 表示
        #self.screen_shot("結果確認 画面_67")
        
        # 68 結果確認　画面: 「ダウンロード」ボタン押下
        
        # 69 ファイルダウンロード: 「1」ボタン押下
        
        # 70 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 71 ログ: 表示
        #self.screen_shot("ログ_71")
        
        # 72 ログ: 閉じる
        
        # 73 結果確認　画面: 「納品物確認」ボタン押下
        
        # 74 納品物管理　画面: 教育・保育給付認定変更通知書.pdf　「ダウンロード」ボタン押下
        
        # 75 納品物管理　画面: 教育・保育給付認定変更通知書兼支給認定証.pdf　「ダウンロード」ボタン押下
        
        # 76 ファイルダウンロード: 「1」ボタン押下
        
        # 77 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        
        # 78 帳票（PDF）: 表示
        #self.screen_shot("帳票（PDF）_78")
        
        # 79 帳票（PDF）: 閉じる

if __name__ == "__main__":
    unittest.main() 
        
