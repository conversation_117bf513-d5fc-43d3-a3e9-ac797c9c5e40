from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010_28040103(KodomoSiteTestCaseBase):
    """TestQAP010_28040103"""

    def setUp(self):
        super().setUp()
    
    # 支給認定証の作成ができることを確認する。
    def test_QAP010_28040103(self):
        """支給認定証作成"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("スケジュール個別追加画面_2")
        # 3 メインメニュー画面: バッチ管理クリック
        # 4 メインメニュー画面: 即時実行クリック
        # 5 メインメニュー画面: 「スケジュール個別追加」ボタンダブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])

        # 6 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_6")
        
        # 7 スケジュール個別追加: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_通知書_一覧出力処理 処理区分：
        # 8 スケジュール個別追加: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("スケジュール_業務名_1"), case_data.get("スケジュール_サブシステム名_1"), case_data.get("スケジュール_処理名_1"))

        self.screen_shot("スケジュール個別追加_8")
        
        # 9 スケジュール個別追加: 「(QP1BN00200) 支給認定証出力 」のNoボタン押下
        self.click_button_by_label("2")

        # 10 実行支持画面: 表示
        self.screen_shot("実行支持画面_10")
        
        # 11 実行支持画面: 所管区：空白のまま
        
        # 12 実行支持画面: 文書番号：初期値のまま
        
        # 13 実行支持画面: 「実行」ボタン押下
        self.screen_shot("実行支持画面_13")
        self.click_button_by_label("実行")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 14 実行結果管理画面: 「検索」ボタン押下
        # 15 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_15")
        
        # 16 メインメニュー画面: 「結果管理」クリック
        # 17 メインメニュー画面: 「納品物確認」ボタンダブルクリック
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 18 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_通知書_一覧出力処理 処理区分：
        # 19 納品物管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("納品物_業務名_1"), case_data.get("納品物_サブシステム名_1"), case_data.get("納品物_処理名_1"))

        self.screen_shot("納品物管理画面_19")
        
        # 20 納品物管理画面: 「QAPP105900_支給認定証.pdf」の「ダウンロード」ボタン押下
        # 21 ファイルダウンロード画面: 表示
        # 22 ファイルダウンロード画面: 「QAPP105900_支給認定証.pdf」のNoボタン押下
        self.pdf_download("QAPP105900_支給認定証.pdf","ファイルダウンロード画面_21")
        
        
        # 23 ファイルダウンロード画面: 保存ボタン押下
        
        # 24 ファイルダウンロード画面: ×ボタン押下
        
        # 25 ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        # 26 納品物管理画面: 「QAPP106000_支給認定証交付通知書.pdf」の「ダウンロード」ボタン押下
        # 27 ファイルダウンロード画面: 表示
        # 28 ファイルダウンロード画面: 「QAPP106000_支給認定証交付通知書.pdf」のNoボタン押下
        #self.pdf_download("QAPP106000_支給認定証交付通知書.pdf","ファイルダウンロード画面_27")
        self.pdf_download("QAPP106000_教育・保育給付認定決定通知書.pdf","ファイルダウンロード画面_27")

        # 29 ファイルダウンロード画面: 保存ボタン押下
        
        # 30 ファイルダウンロード画面: ×ボタン押下
        
        # 31 ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        # 32 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()
        
        # 34 実行結果管理画面: パンくずの「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()

        # 35 スケジュール個別追加: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_交付通知書_一覧(代理申請)出力処理 処理区分：
        # 36 スケジュール個別追加: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("スケジュール_業務名_2"), case_data.get("スケジュール_サブシステム名_2"), case_data.get("スケジュール_処理名_2"))
        self.screen_shot("スケジュール個別追加_36")
        
        # 37 スケジュール個別追加: 「(QP1BN01000) 支給認定証データ（代理申請）抽出」のNoボタン押下
        self.click_button_by_label("1")

        # 38 実行支持画面: 表示
        self.screen_shot("実行支持画面_38")
        
        # 39 実行支持画面: 所管区：空白のまま
        
        # 40 実行支持画面: 発行年月日：初期値のまま（当日）
        
        # 41 実行支持画面: 処理年月日：初期値のまま（当日）
        
        # 42 実行支持画面: 支給決定開始日：「280401-02」で登録した対象者の支給決定開始日
        case_data_02 = self.common_test_data.get("TestQAP010_28040102", {})
        params = [
            {"title":"支給決定開始日", "type": "text", "value": case_data_02.get("認定期間（開始）")},
        ]
        self.set_job_param_kodomo(params)
        # 43 実行支持画面: 支給決定終了日：空白のまま
        
        # 44 実行支持画面: 事業所番号：空白のまま
        
        # 45 実行支持画面: 申請事由：空白のまま
        
        # 46 実行支持画面: 認定区分：空白のまま
        
        # 47 実行支持画面: 支給認定証番号：空白のまま
        
        # 48 実行支持画面: クラス年齢算出基準年度：次年度
        
        # 49 実行支持画面: 施設種類：空白のまま
        
        # 50 実行支持画面: 施設コード：空白のまま
        
        # 51 実行支持画面: 申込区分：空白のまま
        
        # 52 実行支持画面: 入所基準日：初期値のまま（当日）
        
        # 53 実行支持画面: 郵便区内特別有無：初期値のまま（無）
        
        # 54 実行支持画面: 並び順：初期値のまま
        
        # 55 実行支持画面: 再発行区分：初期値のまま（無）
        
        # 56 実行支持画面: 「実行」ボタン押下
        self.screen_shot("実行支持画面_56")
        self.click_button_by_label("実行")
        
        # 57 実行結果管理画面: 「検索」ボタン押下
        # 58 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_58")
        
        # 59 メインメニュー画面: 「結果管理」クリック
        # 60 メインメニュー画面: 「納品物確認」ダブルクリック
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 61 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_交付通知書_一覧(代理申請)出力処理 処理区分：
        # 62 納品物管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("納品物_業務名_2"), case_data.get("納品物_サブシステム名_2"), case_data.get("納品物_処理名_2"))

        self.screen_shot("納品物管理画面_62")
        
        # 63 納品物管理画面: 「QAPP110400_支給認定証交付一覧表（代理申請）.pdf」の「ダウンロード」ボタン押下
        # 64 ファイルダウンロード画面: 表示
        # 65 ファイルダウンロード画面: 「QAPP110400_支給認定証交付一覧表（代理申請）.pdf」のNoボタン押下
        self.pdf_download("QAPP110400_支給認定証交付一覧表（代理申請）.pdf","ファイルダウンロード画面_64")
  
        # 66 ファイルダウンロード画面: 保存ボタン押下
        
        # 67 ファイルダウンロード画面: ×ボタン押下
        
        # 68 ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        # 69 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()
        
        # 71 実行結果管理画面: パンくずの「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()

        # 72 スケジュール個別追加: 「(QP1BN01100) 支給認定証(代理申請)出力」のNoボタン押下
        self.click_button_by_label("2")

        # 73 実行支持画面: 表示
        self.screen_shot("実行支持画面_73")
        
        # 74 実行支持画面: 所管区：空白のまま
        
        # 75 実行支持画面: 文書番号：初期値のまま
        
        # 76 実行支持画面: 「実行」ボタン押下
        self.screen_shot("実行支持画面_76")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        self.click_button_by_label("実行")
        
        # 77 実行結果管理画面: 「検索」ボタン押下
        # 78 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理画面_78")
        
        # 79 メインメニュー画面: 「結果管理」クリック
        # 80 メインメニュー画面: 「納品物確認」ボタンダブルクリック
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 81 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：支給認定証_交付通知書_一覧(代理申請)出力処理 処理区分：
        # 82 納品物管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("納品物_業務名_2"), case_data.get("納品物_サブシステム名_2"), case_data.get("納品物_処理名_2"))

        self.screen_shot("納品物管理画面_82")
        
        # 83 納品物管理画面: 「QAPP105900_支給認定証.pdf」の「ダウンロード」ボタン押下
        # 84 ファイルダウンロード画面: 表示
        # 85 ファイルダウンロード画面: 「QAPP105900_支給認定証.pdf」のNoボタン押下
        self.pdf_download("QAPP105900_支給認定証.pdf","ファイルダウンロード画面_84")
        
        # 86 ファイルダウンロード画面: 保存ボタン押下
        
        # 87 ファイルダウンロード画面: ×ボタン押下
        
        # 88 ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()

        # 89 納品物管理画面: 「QAPP110300_支給認定証交付通知書（代理申請）.pdf」の「ダウンロード」ボタン押下
        # 90 ファイルダウンロード画面: 表示
        # 91 ファイルダウンロード画面: 「QAPP110300_支給認定証交付通知書（代理申請）.pdf」のNoボタン押下
        self.pdf_download("QAPP110300_支給認定証交付通知書（代理申請）.pdf","ファイルダウンロード画面_90")

        # 92 ファイルダウンロード画面: 保存ボタン押下
        
        # 93 ファイルダウンロード画面: ×ボタン押下
        
        # 94 ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()
        
        # 95 納品物管理画面: 「QAPP110200_送付票.pdf」の「ダウンロード」ボタン押下
        # 96 ファイルダウンロード画面: 表示 
        # 97 ファイルダウンロード画面: 「QAPP110200_送付票.pdf」のNoボタン押下
        self.pdf_download("QAPP110200_送付票.pdf","ファイルダウンロード画面_96")
        
        # 98 ファイルダウンロード画面: 保存ボタン押下
        
        # 99 ファイルダウンロード画面: ×ボタン押下
        
        # 100 ファイルダウンロード画面: 閉じるボタン押下
        self.find_element_by_id("tab02_ZACF000300_btnClose").click()
        # 101 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//li[@id='02']/span[2]").click()

        # 102 スケジュール個別追加画面: 「スケジュール個別追加」タブ×ボタンクリック
        self.find_element_by_xpath("//li[@id='01']/span[2]").click()
        
        # 103 対象画面名: 操作内容
        # Assert: アサート内容
        self.screen_shot("対象画面名_103")
        
        # 104 メインメニュー画面: 「子ども子育て支援」をクリック
        # 105 メインメニュー画面: 「世帯情報」をクリック
        # 106 メインメニュー画面: 「検索」をダブルクリック
        self.goto_menu(["子ども子育て支援","世帯情報","検索"])

        # 107 検索画面: 住民コード：<パラメータ>入力
        # Assert: パラメータ化
        self.find_element_by_id(u"tab01_QAZF100001_txtJuminCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAZF100001_txtJuminCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAZF100001_txtJuminCD_textboxInput").send_keys(case_data.get("児童_宛名C"))
        self.driver.set_window_size(1450,2000)

        # 108 検索画面: 「検索（Enter）」ボタンクリック
        self.screen_shot("検索画面_108")
        self.click_button_by_label("検索(Enter)")
        
        # 109 世帯履歴画面: 表示
        self.screen_shot("世帯履歴画面_109")
        
        # 110 世帯履歴画面: 「No1」ボタン押下
        self.click_button_by_label("1")

        # 111 世帯台帳画面: 表示
        self.screen_shot("世帯台帳画面_111")
        
        # 112 世帯台帳画面: 「No1」ボタン押下
        self.click_button_by_label("1")

        # 113 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_113")
        
        # 114 児童台帳画面: 「No1」ボタン押下
        self.click_button_by_label("1")

        # 115 支給認定情報: 表示
        self.screen_shot("支給認定情報_115")
        
        # 116 支給認定情報画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 117 印刷指示画面: 表示
        self.screen_shot("印刷指示画面_117")

        # 118 印刷指示画面: 「支給認定証交付通知書」のチェックボックスをクリック
        self.find_element_by_id(u"tab01_QAPF900100_baseCheckBox1chk0").click()

        # 119 印刷指示画面: 「支給認定証交付通知書」タブをクリック
        self.find_element_by_xpath("//li[@id='#tab01_QAPF900100_guide1']").click()

        # 120 印刷指示画面: 発行年月日：初期値のまま（当日）
        
        # 121 印刷指示画面: 「印刷」ボタン押下
        self.find_element_by_id(u"tab01_QAPF900100_printBtn_button").click()

        # 122 印刷指示画面: 印刷してよろしいですか？：「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()

        # 123 印刷指示画面: 「閉じる」ボタン押下
        self.find_element_by_id(u"tab01_ZEBF002400_btnClose").click()

        # 124 印刷指示画面: 「検索」タブの×ボタン押下
        
        # 125 メインメニュー画面: ×ボタン押下
        
