from base.kodomo_case import KodomoSiteTestCaseBase

from selenium.webdriver.support.ui import Select

class TestQAP010_28070104(KodomoSiteTestCaseBase):
    """TestQAP010_28070104"""

    def setUp(self):
        super().setUp()
    
    # 調定根拠となる利用情報の修正が行ることを確認する。
    def test_QAP010_28070104(self):
        """利用情報修正"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「子ども子育て支援」ボタン押下
        
        # 4 メインメニュー画面: 「入所管理」ボタン押下
        
        # 5 メインメニュー画面: 「児童検索」ボタンダブルクリック
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        # 6 児童検索画面: 表示
        self.screen_shot("児童検索画面_6")
        
        # 7 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("児童宛名C"))
        # 8 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        # 9 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_9")
        
        # 10 児童台帳画面（支給認定登録・履歴タブ）: 「No.1」ボタン押下
        self.click_button_by_label("1")
        # 11 支給認定情報画面: 表示
        self.screen_shot("支給認定情報画面_11")
        
        # 12 支給認定情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        
        # 13 支給認定情報画面: 「支給認定結果」タブをクリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF103600_sikyuninteikekka_li']/a/span").click()
        self.screen_shot("支給認定情報画面_13")
        
        # 14 支給認定情報画面: 時間区分（保育必要量）：
        self.find_element_by_id(u"tab01_QAPF103600_selNinteiKekkaTimeKbnHoikuHitsuyoryo_select").click()
        Select(self.find_element_by_id(u"tab01_QAPF103600_selNinteiKekkaTimeKbnHoikuHitsuyoryo_select")).select_by_visible_text(case_data.get("保育必要量"))
        # 15 支給認定情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.wait_page_loaded()
        # 16 支給認定情報画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        # 17 支給認定情報画面: 表示
        # Assert: 「更新しました。」のメッセージのチェック
        self.assert_message_area("tab01_QAPF103600_msg_span","更新しました。") 
        self.screen_shot("支給認定情報画面_17")
        
        # 18 支給認定情報画面: 「戻る」ボタン押下
        self.find_element_by_id(u"btn_back").click()
        
        # 19 児童台帳画面: 「入所管理」タブをクリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a").click()
        # 20 児童台帳画面: 「契約情報」ボタン押下
        self.click_button_by_label("契約情報")
        # 21 契約情報画面: 表示
        self.screen_shot("契約情報画面_21")
        
        # 22 契約情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        # 23 契約情報画面: 保育必要量：
        self.find_element_by_id(u"tab01_QAPF103800_selKeiyakuHoikuHitsuyoryo_select").click()
        Select(self.find_element_by_id(u"tab01_QAPF103800_selKeiyakuHoikuHitsuyoryo_select")).select_by_visible_text(case_data.get("保育必要量"))
        # 24 契約情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.wait_page_loaded()
        # 25 契約情報画面: 「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        # 26 契約情報画面: 表示
        # Assert: 「更新しました。」のメッセージのチェック
        self.assert_message_area("tab01_QAPF103800_msg_span","更新しました。") 
        self.screen_shot("契約情報画面_26")
        self.find_element_by_xpath("//li[@id='01']/span[2]").click()

        # 27 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_27")
        
        # 28 メインメニュー画面: 「バッチ管理」ボタン押下
        
        # 29 メインメニュー画面: 「即時実行」ボタン押下
        
        # 30 メインメニュー画面: 「スケジュール個別追加」ボタンをダブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        self.wait_page_loaded()
        # 31 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_31")
        
        # 32 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：賦課処理名：月次賦課処理
        
        # 33 スケジュール個別追加画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名"), case_data.get("サブシステム名"),case_data.get("処理名"))

        # 34 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()
       
       # 35 バッチ起動画面: 対象年月：                               　を入力対象年度：                               　を入力基準年月：                               　を入力
        # Assert: パラメータ化
        params = [
            {"title":"対象年月", "type": "text", "value": case_data.get("対象年月")},
            {"title":"対象年度", "type": "text", "value": case_data.get("対象年度")},
            {"title":"基準年月", "type": "text", "value": case_data.get("基準年月")}
        ]
        self.set_job_param_kodomo(params)
        self.wait_page_loaded()
        
        self.screen_shot("バッチ起動画面_35")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        self.click_button_by_label("実行")
        self.wait_page_loaded()
        
        # 36 実行結果画面: 表示
        # Assert: 「(QP7BN00210) 月次賦課計算処理」の状態が正常終了となることを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds) 
        self.screen_shot("実行結果画面_36")
        
        # 37 実行結果画面: 「検索」タブをクリック
        ##self.find_element_by_xpath("//ul[@id='work_tab']/li/span[2]").click()
        self.find_element_by_xpath("//li[@id='01']/span[2]").click()

        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        
        # 児童検索画面: 住民コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("児童宛名C"))
        # 児童検索画面: 「検索」ボタン押下
        self.driver.set_window_size(1450,2000)
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()

        # 39 児童台帳画面: 「賦課」ボタンを押下
        self.click_button_by_label("賦課") 

        # 40 児童賦課情報画面: 表示
        self.screen_shot("児童賦課情報画面_40")
        
        # 41 児童賦課情報画面: 賦課年度：
        self.find_element_by_id(u"tab01_QAPF107000_selFukaYear_select").send_keys(case_data.get("賦課年度"))
        # 42 児童賦課情報画面: 表示
        # Assert: 保育料タブの保育必要量が変更した保育必要量になっていること
        self.screen_shot("児童賦課情報画面_42")
        
