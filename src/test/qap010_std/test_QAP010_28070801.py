import time
import unittest
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAP010_28070801(KodomoSiteTestCaseBase):
    """TestQAP010_28070801"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()
    
    # 滞納繰越登録ができることを確認する。
    def test_QAP010_28070801(self):
        """滞納繰越登録"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「バッチ管理」クリック
        self.find_element_by_xpath("//*[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        time.sleep(1)
        
        # 4 メインメニュー画面: 「即時実行」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)
        
        # 5 メインメニュー画面: 「スケジュール個別追加」ダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        
        # 6 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_6")
        
        # 7 スケジュール個別追加画面: 業務名：収納・滞納 サブシステム名：年次 処理名：滞繰年度切替処理 処理区分：
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuGyomuNM_select"),"収納・滞納")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuSubSystemNM_select"),"児童")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuShoriNM_select"),"滞繰年度切替処理")
        
        # 8 スケジュール個別追加画面: 「検索」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF000400_BtnKensaku_button").click()
        
        # 9 スケジュール個別追加画面: 「(JAFBN00500) 滞繰年度切替処理」Noボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_BtnMode32_1_4_button").click()
        
        # 10 実行指示画面: 表示
        self.screen_shot("実行指示画面_10")
        
        # 11 実行指示画面: 「実行」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF002200_executebtn_button").click()
        
        # 12 実行結果管理画面: 表示
        # Assert: 「実行しました。」のメッセージのチェック
        self.screen_shot("実行結果管理画面_12")
        
        # 13 実行結果管理画面: 正常終了まで5分置きに「実行」ボタンクリック
        self.wait_job_finished_kodomo(3600, 20)
        self.screen_shot("実行結果管理画面_13")
        
