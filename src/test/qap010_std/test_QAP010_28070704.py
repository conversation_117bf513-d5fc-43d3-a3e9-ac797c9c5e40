import time
import unittest
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAP010_28070704(KodomoSiteTestCaseBase):
    """TestQAP010_28070704"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()
    
    # 還付する口座情報を登録することができることを確認する。
    def test_QAP010_28070704(self):
        """還付口座登録"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        settings = self.common_test_data
        self.test_data = settings.get("test_qap010_28070704")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")
        
        # 3 メインメニュー画面: 「収納」クリック
        self.find_element_by_xpath("//*[@id='rootmenu_0']/li[contains(span,'収納')]/span").click()
        time.sleep(1)
        
        # 4 メインメニュー画面: 「還付・充当処理」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'収納')]/ul/li[contains(span,'還付・充当処理')]/span").click()
        time.sleep(1)
        
        # 5 メインメニュー画面: 「還付・充当処理」ダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'収納')]/ul/li[contains(span,'還付・充当処理')]/ul/li[contains(span,'還付・充当処理')]/span")
        self.actionChains.double_click(searchBtn).perform()
        
        # 6 （還付充当）検索条件入力画面: 表示
        self.screen_shot("（還付充当）検索条件入力画面_5")
        
        # 7 （還付充当）検索条件入力画面: 住民コード：（パラメータ化）　を入力
        self.find_element(By.ID,"tab01_JABF100100_txtJuminCD_textboxInput").send_keys("")
        self.find_element(By.ID,"tab01_JABF100100_txtJuminCD_textboxInput").send_keys(self.test_data.get("qap010_28070704_atena_code",""))
        
        # 8 （還付充当）検索条件入力画面: 「検索」ボタンクリック
        self.find_element(By.ID,"tab01_JABF100100_WrCmnBtn05_button").click()
        
        # 9 （還付充当）該当者一覧画面: 表示
        
        # 10 （還付充当）該当者一覧画面: No「1」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_btnNo_1_1_button").click()
        
        # 11 還付更正管理画面: 表示
        
        # 12 還付更正管理画面: 「還付振込口座」タブクリック
        self.find_element_by_xpath("//*[@id='tab01_JABF100500_4_li']/a/span").click()
        self.find_element(By.ID,"tab01_JABF100500_btnEditChg_button").click()
        self.find_element(By.ID,"tab01_JABF100500_btnKozaINFOClear_button").click()
        
        # 13 還付更正管理画面：還付振込口座タブ: 表示
        self.screen_shot("還付更正管理画面：還付振込口座タブ_12")
        
        # 14 還付更正管理画面：還付振込口座タブ: 「金融機関選択」ボタンクリック
        self.find_element(By.ID,"tab01_JABF100500_zaaf090btn_button").click()
        
        # 15 金融機関選択画面ダイアログ: 表示
        
        # 16 金融機関選択画面ダイアログ: 銀行名（カナ）：（パラメータ化）　を入力支店名（カナ）：（パラメータ化）　を入力
        # 銀行名（カナ）
        self.find_element(By.ID,"tab01_ZAAF000900_txtGinkoNMKana_textboxInput").send_keys("")
        self.find_element(By.ID,"tab01_ZAAF000900_txtGinkoNMKana_textboxInput").send_keys(self.test_data.get("qap010_28070704_ginko",""))
        
        # 支店名（カナ）
        self.find_element(By.ID,"tab01_ZAAF000900_txtShitenNMKana_textboxInput").send_keys("")
        self.find_element(By.ID,"tab01_ZAAF000900_txtShitenNMKana_textboxInput").send_keys(self.test_data.get("qap010_28070704_shiten",""))
        
        # 17 金融機関選択画面ダイアログ: 「検索」ボタンクリック
        self.find_element(By.ID,"tab01_ZAAF000900_btnKensaku_button").click()
        
        # 18 金融機関選択画面ダイアログ: 表示される金融機関コードボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_btnCode_1_1_button").click()
        
        # 19 還付更正管理画面：還付振込口座タブ: 表示
        
        # 20 還付更正管理画面：還付振込口座タブ: 預金種別：普通
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JABF100500_selKozaTorokuYokinShubetsu_select"),"普通")
        
        # 21 還付更正管理画面：還付振込口座タブ: 口座番号：1234567　を入力
        self.find_element(By.ID,"tab01_JABF100500_txtKozaTorokuKozaNo_textboxInput").send_keys("1234567")
        
        # 22 還付更正管理画面：還付振込口座タブ: 口座名義人カナ：ﾃｽﾄﾄｳﾛｸ　を入力
        self.find_element(By.ID,"tab01_JABF100500_txtKozaTorokuYokinshaKanaShimei_textboxInput").send_keys("ﾃｽﾄﾄｳﾛｸ")
        
        # 23 還付更正管理画面：還付振込口座タブ: 口座名義人漢字：テスト登録　を入力
        self.find_element(By.ID,"tab01_JABF100500_txtKozaTorokuYokinshaShimei_textboxInput").send_keys("テスト登録")
        
        # 24 還付更正管理画面: 「更新」ボタンクリック
        self.find_element(By.ID,"tab01_JABF100500_regbtn_button").click()
        
        # 25 確認ダイアログ: 表示
        
        # 26 確認ダイアログ: 「はい」ボタンクリック
        self.find_element(By.ID,"tempId__1").click()
        
        # 27 還付更正管理画面: 表示
        # Assert: 「更新しました。」のメッセージのチェック
        self.screen_shot("還付更正管理画面_26")
        
