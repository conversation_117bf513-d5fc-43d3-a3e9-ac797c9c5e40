import time
import unittest
from datetime import datetime, date, timedelta
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.select import Select
class TestQAP010_28010302(KodomoSiteTestCaseBase):
    """TestQAP010_28010302"""

    def setUp(self):
        settings = self.common_test_data
        self.test_data = settings.get("test_qap010_28010302")
        super().setUp()
    
    # 教育・保育給付認定取消情報にかかわる通知書が出力できることを確認する。
    def test_QAP010_28010302(self):
        """取消通知書作成"""
        
        driver = None

        self.do_login()
        # 1 メインメニュー　画面: 表示
        self.save_screenshot_migrate(driver, "メインメニュー 画面_1" , True)
        
        # 2 メインメニュー　画面: 「バッチ管理」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        time.sleep(1)

        # 3 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 4 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)
        
        # 5 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '教育・保育給付認定取消通知書']").click()

        # 6 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 7 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_7" , True)
        
        # 8 スケジュール個別追加　画面: 「№1」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_1_4_button").click()

        # 9 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_9" , True)
        
        # 10 実行指示　画面: 発行年月日、取消年月日開始、取消年月日終了、支給認定証番号　を入力
        # Assert: パラメータ化
        # 発行年月日
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Paratest05_textboxInput").send_keys(formatted_date)
        # 取消年月日開始
        self.find_element_by_id("tab01_ZEAF002200_Para_QP1BJ04700_01_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Para_QP1BJ04700_01_textboxInput").clear()
        self.find_element_by_id("tab01_ZEAF002200_Para_QP1BJ04700_01_textboxInput").send_keys(self.test_data.get("case_qap001_torikeshi_nenngappi_kaishi"))
        # 取消年月日終了
        self.find_element_by_id("tab01_ZEAF002200_Para_QP1BJ04700_02_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Para_QP1BJ04700_02_textboxInput").clear()
        current_date = datetime.today()
        formatted_date = current_date.strftime("%Y%m%d")
        self.find_element_by_id("tab01_ZEAF002200_Para_QP1BJ04700_02_textboxInput").send_keys(formatted_date)
        # 支給認定証番号
        self.find_element_by_id("tab01_ZEAF002200_Paratest11_textboxInput").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest11_textboxInput").send_keys(self.test_data.get("case_qap001_shikyuu_ninteishou_bangou"))

        # 11 実行指示　画面: 申請事由、認定区分、並び順、再発行区分、郵便区内特別有無　を選択
        # Assert: パラメータ化
        # 申請事由
        self.find_element_by_id("tab01_ZEAF002200_Paratest10_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest10_select").send_keys(self.test_data.get("case_qap001_shinsei_jiyuu"))
        # 認定区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest09_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest09_select").send_keys(self.test_data.get("case_qap001_nintei_kubun"))
        # 並び順
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest14_select").send_keys(self.test_data.get("case_qap001_narabi_jun"))
        # 再発行区分
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest12_select").send_keys(self.test_data.get("case_qap001_saihakkou_kubun"))
        # 郵便区内特別有無
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").click()
        self.find_element_by_id("tab01_ZEAF002200_Paratest13_select").send_keys(self.test_data.get("case_qap001_yuubinkunai_tokubetsuumu"))

        # 12 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.save_screenshot_migrate(driver, "実行指示 画面_12" , True)
        
        # 13 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 14 実行結果管理　画面: 表示
        # Assert: 「（QP1BN04700）教育・保育給付認定取消対象者一覧出力」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_14" , True)
        
        # 15 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 16 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_16" , True)
        
        # 17 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()
        
        # 18 ファイルダウンロード: 「1」ボタン押下
        # 19 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 20 ログ: 表示
        # 21 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_20" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 22 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()
        
        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：教育・保育給付認定取消通知書
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_1 = self.test_data.get("ShoriNM_1")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_1)
        
        # 23 納品物管理　画面: QAPP120800_教育・保育給付認定取消対象者一覧.pdf　「ダウンロード」ボタン押下
        # 24 ファイルダウンロード: 「1」ボタン押下→#24~27は手動で実施。
        #time.sleep(20)
        # 25 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 26 帳票（PDF）: 表示
        #self.save_screenshot_migrate(driver, "帳票（PDF）_26" , True)
        # 27 帳票（PDF）: 閉じる

        # 28 納品物管理　画面: 教育・保育給付認定取消対象者一覧.csv　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # 29 ファイルダウンロード: 「1」ボタン押下
        # 30 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 31 帳票（CSV）: 表示
        # 32 帳票（CSV）: 閉じる
        self.pdf_download("教育・保育給付認定取消対象者一覧.CSV", "帳票（CSV）_26")
        self.click_button_by_label("閉じる")
        time.sleep(2)       
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 33 メインメニュー　画面: 表示
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.save_screenshot_migrate(driver, "メインメニュー 画面_28" , True)
        
        # 34 メインメニュー　画面: 「バッチ管理」ボタン押下
        #self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        #time.sleep(1)

        # 35 メインメニュー　画面: 「即時実行」ボタンを押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)

        # 36 メインメニュー　画面: 「スケジュール個別追加」ボタン　タブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        time.sleep(1)

        # 37 スケジュール個別追加　画面: 業務名、サブシステム名、処理名　を選択
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuGyomuNM_select").click()
        self.find_element_by_xpath("//option[. = '子ども子育て支援']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuSubSystemNM_select").click()
        self.find_element_by_xpath("//option[. = '支給認定']").click()
        self.find_element_by_id("tab01_ZEAF000400_SelKensakuShoriNM_select").click()
        self.find_element_by_xpath("//option[. = '教育・保育給付認定取消通知書']").click()

        # 38 スケジュール個別追加　画面: 「検索」ボタン押下
        self.find_element_by_id("tab01_ZEAF000400_BtnKensaku_button").click()

        # 39 スケジュール個別追加　画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加 画面_34" , True)
        
        # 40 スケジュール個別追加　画面: №「2」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnMode32_2_4_button").click()

        # 41 実行指示　画面: 表示
        self.save_screenshot_migrate(driver, "実行指示 画面_36" , True)
        
        # 42 実行指示　画面: 「入力チェック」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_checkbtn_button").click()
        self.screen_shot("実行指示 画面_37")
        
        # 43 実行指示　画面: 「実行」ボタン押下
        self.find_element_by_id("tab01_ZEAF002200_executebtn_button").click()
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 44 実行結果管理　画面: 表示
        # Assert: 「（QP1BN04800）教育・保育給付認定取消通知書出力」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理 画面_39" , True)
        
        # 45 実行結果管理　画面: 「№」ボタン押下
        self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()
        
        # 46 結果確認　画面: 表示
        self.save_screenshot_migrate(driver, "結果確認 画面_41" , True)
        
        # 47 結果確認　画面: 「ダウンロード」ボタン押下
        self.find_element_by_id("tab01_ZEAF002400_BtnLogDown_1_8_button").click()
        
        # 48 ファイルダウンロード: 「1」ボタン押下
        # 49 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 50 ログ: 表示
        # 51 ログ: 閉じる
        self.save_screenshot_migrate(driver, "ログ_45" , True)
        self.driver.switch_to.frame(1)
        self.find_element_by_xpath("//*[@id='FILE_LIST']/table/tbody/tr/td[1]/input").click()
        self.driver.switch_to.default_content()
        self.click_button_by_label("閉じる")
        time.sleep(2)
        
        # 52 結果確認　画面: 「納品物確認」ボタン押下
        #self.find_element_by_id("tab01_ZEAF002400_BtnNohinbutsuKakunin_button").click()
        
        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援 サブシステム名：支給認定 処理名：教育・保育給付認定取消通知書
        # 納品物確認画面: 「検索」ボタン押下
        gyomuNM = self.test_data.get("GyomuNM")
        subSystemNM = self.test_data.get("SubSystemNM")
        shoriNM_1 = self.test_data.get("ShoriNM_1")
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_1)

        # 53 納品物管理　画面: QAPP120900_教育・保育給付認定取消通知書.pdf　「ダウンロード」ボタン押下
        #self.find_element_by_id("tab01_ZZZ000000_BtnDownload_1_9_button").click()

        # 54 ファイルダウンロード: 「1」ボタン押下
        # 55 ファイルダウンロード: 「ファイルを開く(O)」ボタン押下
        # 56 帳票（PDF）: 表示
        # 57 帳票（PDF）: 閉じる
        self.pdf_download("QAPP120900_教育・保育給付認定取消通知書.pdf", "帳票（PDF）_51")
        self.click_button_by_label("閉じる")
        time.sleep(2)       
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

if __name__ == "__main__":
    unittest.main() 
