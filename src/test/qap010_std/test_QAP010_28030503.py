import time
from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010_28030503(KodomoSiteTestCaseBase):
    """TestQAP010_28030503"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params_list = case_data.get("sql_params")
        self.exec_sqlfile("test_QAP010_28030503_実施前スクリプト.sql", params=sql_params_list)
        super().setUp()
    
    # 副食費免除、非免除にかかわる各種帳票の出力ができることを確認する。
    def test_QAP010_28030503(self):
        """各種帳票作成"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        self.do_login()
        # 2 メインメニュー 画面: 表示
        self.screen_shot("メインメニュー画面_2")

        # 3 メインメニュー 画面: 「バッチ管理」ボタン押下
        # 4 メインメニュー 画面: 「即時実行」ボタンを押下
        # 5 メインメニュー 画面: 「スケジュール個別追加」ボタン タブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])

        # 6 スケジュール個別追加 画面: 業務名、サブシステム名、処理名 を選択
        # 7 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_1"), case_data.get("サブシステム名_1"), case_data.get("処理名_1"))
        
        # 8 スケジュール個別追加 画面: 表示
        self.screen_shot("スケジュール個別追加 画面_8")
        
        # 9 スケジュール個別追加 画面: 「№1」ボタン押下
        self.click_button_by_label("1")
        # 10 実行指示 画面: 表示
        self.screen_shot("実行指示 画面_10")
        
        # 11 実行指示 画面: 対象年月 を入力
        # Assert: パラメータ化
        params = [
            {"title":"対象年月", "type": "text", "value": case_data.get("食材料費徴収免除(取消)_対象年月")},
            {"title":"児童宛名コード", "type": "text", "value": case_data.get("食材料費徴収免除(取消)_児童宛名コード")},
            {"title":"再発行区分", "type": "select", "value": case_data.get("再発行区分")}
        ]
        self.set_job_param_kodomo(params)
        # 12 実行指示 画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.screen_shot("実行指示 画面_12")
        self.wait_page_loaded()
        # 13 実行指示 画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()
        time.sleep(1)
        
        # 14 実行結果管理 画面: 表示
        # Assert: 「(QP2BN13500) 食材料費徴収免除（取消）通知書　抽出」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理 画面_14")
        
        # 15 実行結果管理 画面: 「No.1」ボタン押下

        # 16 結果確認画面: 表示
        # self.screen_shot("結果確認画面_16")
        
        # 17 結果確認画面: 「納品物確認」ボタン押下

        # 18 納品物管理画面: 表示
        #self.screen_shot("納品物管理画面_18")
        
        # 19 納品物管理画面: 食材料費徴収免除対象者一覧.pdf 「ダウンロード」ボタン押下
        # 20 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_20")
        
        # 21 ファイルダウンロード画面: 「No.1」ボタン押下
        
        # 22 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        
        # 23 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_23")
        
        # 24 PDF: ×ボタン押下で閉じる
        
        # 25 ファイルダウンロード画面: 「閉じる」ボタン押下

        # 26 納品物管理画面: 食材料費徴収免除取消対象者一覧.pdf 「ダウンロード」ボタン押下
        
        # 27 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_27")
        
        # 28 ファイルダウンロード画面: 「No.1」ボタン押下
        
        # 29 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        
        # 30 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_30")
        
        # 31 PDF: ×ボタン押下で閉じる
        
        # 32 ファイルダウンロード画面: 「閉じる」ボタン押下
        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名、サブシステム名、処理名 を選択
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_1"), case_data.get("サブシステム名_1"), case_data.get("処理名_1"))
        self.wait_page_loaded()
        
        # 納品物確認画面: QAPP113500_食材料費徴収免除対象者一覧.pdf「ダウンロード」ボタン押下
        self.pdf_download("QAPP113500_食材料費徴収免除対象者一覧.pdf", "ファイルダウンロード画面_20")
        self.click_button_by_label("閉じる")
        self.pdf_download("QAPP113600_食材料費徴収免除取消対象者一覧.pdf", "ファイルダウンロード画面_27")

        # ファイルダウンロード画面: 閉じるボタン押下
        self.click_button_by_label("閉じる")
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # 33 実行結果管理 画面: パンくず「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        self.wait_page_loaded()

        # 34 スケジュール個別追加 画面: 業務名、サブシステム名、処理名 を選択
        # 35 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_1"), case_data.get("サブシステム名_1"), case_data.get("処理名_1"))
        
        # 36 スケジュール個別追加 画面: 表示
        self.screen_shot("スケジュール個別追加 画面_36")
        
        # 37 スケジュール個別追加 画面: 「№2」ボタン押下
        self.click_button_by_label("2")

        # 38 実行指示 画面: 表示
        self.screen_shot("実行指示 画面_38")
        
        # 39 実行指示 画面: 出力帳票を入力
        # Assert: パラメータ化
        params = [
            {"title":"出力帳票", "type": "select", "value": case_data.get("食材料費徴収免除(取消)_出力帳票_1")},
        ]
        self.set_job_param_kodomo(params)

        # 40 実行指示 画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.screen_shot("実行指示 画面_40")
        
        # 41 実行指示 画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()
        time.sleep(1)

        # 42 実行結果管理 画面: 表示
        # Assert: 「(QP2BN13600) 食材料費徴収免除（取消）通知書　出力」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理 画面_42")
        
        # 43 実行結果管理 画面: 「No.1」ボタン押下
        # self.find_element_by_id("tab01_ZZZ000000_BtnNo_1_2_button").click()

        # 44 結果確認画面: 表示
        # self.screen_shot("結果確認画面_44")
        
        # 45 結果確認画面: 「納品物確認」ボタン押下

        
        # 46 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_46")
        
        # 47 納品物管理画面: 「ダウンロード」ボタンを押下
 

        # 48 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_48")
        
        # 49 ファイルダウンロード画面: 「No.1」ボタン押下
        
        # 50 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        
        # 51 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_51")
        
        # 52 PDF: ×ボタン押下で閉じる
        
        # 53 ファイルダウンロード画面: 「閉じる」ボタン押下

        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        self.screen_shot("ファイルダウンロード画面_48")

        # 納品物確認画面:  業務名、サブシステム名、処理名 を選択
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_1"), case_data.get("サブシステム名_1"), case_data.get("処理名_1"))
        
        # 納品物確認画面: QAPP113700_食材料費徴収免除通知書.pdf「ダウンロード」ボタン押下
        self.pdf_download("QAPP113700_食材料費徴収免除通知書.pdf", "ファイルダウンロード画面_48")

        # ファイルダウンロード画面: 閉じるボタン押下
        self.click_button_by_label("閉じる")
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # 54 実行結果管理 画面: パンくず「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()

        # 55 スケジュール個別追加 画面: 業務名、サブシステム名、処理名 を選択
        # 56 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名_1"), case_data.get("サブシステム名_1"), case_data.get("処理名_1"))
        
        # 57 スケジュール個別追加 画面: 表示
        self.screen_shot("スケジュール個別追加 画面_57")
        
        # 58 スケジュール個別追加 画面: 「№2」ボタン押下
        self.click_button_by_label("2")

        # 59 実行指示 画面: 表示
        self.screen_shot("実行指示 画面_59")
        
        # 60 実行指示 画面: 出力帳票を入力
        # Assert: パラメータ化
        params = [
            {"title":"出力帳票", "type": "select", "value": case_data.get("食材料費徴収免除(取消)_出力帳票_2")},
        ]
        self.set_job_param_kodomo(params)
        # 61 実行指示 画面: 「入力チェック」ボタン押下
        self.click_button_by_label("入力チェック")
        self.screen_shot("実行指示 画面_61")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        # 62 実行指示 画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()
        time.sleep(1)
        
        # 63 実行結果管理 画面: 表示
        # Assert: 「(QP2BN13600) 食材料費徴収免除（取消）通知書　出力」の状態が正常終了することを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果管理 画面_63")
        
        # 64 実行結果管理 画面: 「No.1」ボタン押下
  

        # 65 結果確認画面: 表示
        # self.screen_shot("結果確認画面_65")
        
        # 66 結果確認画面: 「納品物確認」ボタン押下
      
        # 67 納品物管理画面: 表示
        # self.screen_shot("納品物管理画面_67")
        
        # 68 納品物管理画面: 「ダウンロード」ボタンを押下


        # 69 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_69")
        
        # 70 ファイルダウンロード画面: 「No.1」ボタン押下
        
        # 71 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        
        # 72 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_72")
        
        # 73 PDF: ×ボタン押下で閉じる
        # メインメニュー画面: 「バッチ管理」ボタン押下
        # メインメニュー画面: 「結果管理」ボタン押下
        # メインメニュー画面: 「納品物確認」ボタン押下
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        self.screen_shot("納品物管理画面_67")

        # 納品物確認画面:  業務名、サブシステム名、処理名 を選択
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",case_data.get("業務名_1"), case_data.get("サブシステム名_1"), case_data.get("処理名_1"))
        
        # 納品物確認画面: QAPP113800_食材料費徴収免除取消通知書.pdf「ダウンロード」ボタン押下
        self.pdf_download("QAPP113800_食材料費徴収免除取消通知書.pdf", "ファイルダウンロード画面_69")

        # ファイルダウンロード画面: 閉じるボタン押下
        self.click_button_by_label("閉じる")
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
