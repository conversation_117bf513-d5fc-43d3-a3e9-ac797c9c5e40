import time
import unittest
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAP010_28070703(KodomoSiteTestCaseBase):
    """TestQAP010_28070703"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()
    
    # 還付・充当登録に関わる各種通知書を作成することができることを確認する。
    def test_QAP010_28070703(self):
        """還付・充当通知書作成"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        settings = self.common_test_data
        self.test_data = settings.get("test_qap010_28070703")

        self.do_login()
        # 2 還付充当通知書一括作成: None
        
        # 3 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 4 メインメニュー画面: 「収納」クリック
        self.find_element_by_xpath("//*[@id='rootmenu_0']/li[contains(span,'収納')]/span").click()
        time.sleep(1)
        
        # 5 メインメニュー画面: 「還付帳票発行」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'収納')]/ul/li[contains(span,'還付帳票発行')]/span").click()
        time.sleep(1)
        
        # 6 メインメニュー画面: 「還付通知書発行」ダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'収納')]/ul/li[contains(span,'還付帳票発行')]/ul/li[contains(span,'還付通知帳票発行')]/span")
        self.actionChains.double_click(searchBtn).perform()
        
        # 7 （累積不一致）検索条件入力画面: 表示
        self.screen_shot("（累積不一致）検索条件入力画面_6")
        
        # 8 （還付充当）検索条件入力画面: 科目：（パラメータ化）　を選択
        # Assert: 記載のないパラメータは全て初期値
        # 行政区を選ばないと印刷できない？
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JABF101500_selGyoseiku_select"),self.test_data.get("qap010_28070703_selGyoseiku",""))
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JABF101500_selKamoku_select"),self.test_data.get("qap010_28070703_selKamoku",""))
        
        # 9 （還付充当）検索条件入力画面: 還付状態：一部充当　を選択
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JABF101500_selKampuJotaiKbn_select"),self.test_data.get("qap010_28070703_selKampuJotaiKbn",""))
        
        # 10 （還付充当）検索条件入力画面: 還付年度：（パラメータ化）　を選択
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JABF101500_selKampuNendoStart_select"),self.test_data.get("qap010_28070703_selKampuNendoStart",""))
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JABF101500_selKampuNendoEnd_select"),self.test_data.get("qap010_28070703_selKampuNendoEnd",""))
        
        # 11 （還付充当）検索条件入力画面: 還付通知日：（パラメータ化）
        self.find_element(By.ID,"tab01_JABF101500_txtKampuTsuchibiStart_textboxInput").send_keys("")
        self.find_element(By.ID,"tab01_JABF101500_txtKampuTsuchibiStart_textboxInput").send_keys(self.test_data.get("qap010_28070703_txtKampuTsuchibiStart",""))
        
        self.find_element(By.ID,"tab01_JABF101500_txtKampuTsuchibiEnd_textboxInput").send_keys("")
        self.find_element(By.ID,"tab01_JABF101500_txtKampuTsuchibiEnd_textboxInput").send_keys(self.test_data.get("qap010_28070703_txtKampuTsuchibiEnd",""))
        
        # 12 （還付充当）検索条件入力画面: 発行対象：未発行
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JABF101500_selKampuHakkoTaishoKbn_select"),self.test_data.get("qap010_28070703_selKampuHakkoTaishoKbn",""))
        
        # 13 （還付充当）検索条件入力画面: 「印刷」ボタンクリック
        self.find_element(By.ID,"tab01_JABF101500_btnPrint_button").click()
        
        # 14 ワーニングダイアログ: 表示
        
        # 15 ワーニングダイアログ: 「はい」ボタンクリック
        self.find_element(By.ID,"tempId__1").click()
        
        # 16 （還付充当）印刷指示画面: 表示
        
        # 17 （還付充当）印刷指示画面: 過誤納金還付(充当)通知書　にチェック
        self.find_element(By.ID,"tab01_JAPF000200_baseCheckBox0chk0").click()
        
        # 18 （還付充当）印刷指示画面: 過誤納金還付(充当)請求書　にチェック
        self.find_element(By.ID,"tab01_JAPF000200_baseCheckBox1chk0").click()
        
        # 19 （還付充当）印刷指示画面: 過誤納金還付(充当)領収書　にチェック
        self.find_element(By.ID,"tab01_JAPF000200_baseCheckBox3chk0").click()
        
        # 20 （還付充当）印刷指示画面: 過誤納金還付(充当)命令書　にチェック
        self.find_element(By.ID,"tab01_JAPF000200_baseCheckBox2chk0").click()
        
        # 21 （還付充当）印刷指示画面: 文書番号：1234　を入力
        self.find_element(By.ID,"tab01_JAPF000200_txtBunshoNo_textboxInput").send_keys("")
        self.find_element(By.ID,"tab01_JAPF000200_txtBunshoNo_textboxInput").send_keys("1234")
        
        # 22 （還付充当）印刷指示画面: 「印刷」ボタンクリック
        self.find_element(By.ID,"tab01_JAPF000200_printBtn_button").click()
        
        # 23 印刷確認ダイアログ: 表示
        self.screen_shot("印刷確認ダイアログ_22")
        
        # 24 印刷確認ダイアログ: 「はい」ボタンクリック
        self.find_element(By.ID,"tempId__3").click()
        
        # 25 印刷確認ダイアログ: No「1」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_WrBtnNo_1_1_button").click()
        
        # 26 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_25")
        
        # 27 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 28 PDF「過誤納金還付(充当)通知書」: 表示
        self.screen_shot("PDF「過誤納金還付(充当)通知書」_27")
        
        # 29 印刷確認ダイアログ: No「2」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_WrBtnNo_2_1_button").click()
        
        # 30 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_29")
        
        # 31 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 32 PDF「過誤納金還付(充当)請求書」: 表示
        self.screen_shot("PDF「過誤納金還付(充当)請求書」_31")
        
        # 33 印刷確認ダイアログ: No「3」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_WrBtnNo_3_1_button").click()
        
        # 34 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_33")
        
        # 35 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 36 PDF「過誤納金還付(充当)領収書」: 表示
        self.screen_shot("PDF「過誤納金還付(充当)領収書」_35")
        
        # 37 印刷確認ダイアログ: No「4」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_WrBtnNo_4_1_button").click()
        
        # 38 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_37")
        
        # 39 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 40 PDF「過誤納金還付(充当)命令書」: 表示
        self.screen_shot("PDF「過誤納金還付(充当)命令書」_39")
        
        # 41 還付充当通知書個別作成: None
        self.find_element(By.ID,"tab01_ZEBF002400_btnClose").click()
        
        # 42 メインメニュー画面: 表示
        time.sleep(1)
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.screen_shot("メインメニュー画面_41")
        
        # 43 メインメニュー画面: 「収納」クリック
        # self.find_element_by_xpath("//*[@id='rootmenu_0']/li[contains(span,'収納')]/span").click()
        # time.sleep(1)
        
        # 44 メインメニュー画面: 「還付・充当処理」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'収納')]/ul/li[contains(span,'還付・充当処理')]/span").click()
        time.sleep(1)
        
        # 45 メインメニュー画面: 「還付・充当処理」ダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'収納')]/ul/li[contains(span,'還付・充当処理')]/ul/li[contains(span,'還付・充当処理')]/span")
        self.actionChains.double_click(searchBtn).perform()
        
        # 46 （還付充当）検索条件入力画面: 表示
        self.screen_shot("（還付充当）検索条件入力画面_45")
        
        # 47 （還付充当）検索条件入力画面: 住民コード：（パラメータ化）　を入力
        self.find_element(By.ID,"tab01_JABF100100_txtJuminCD_textboxInput").send_keys("")
        self.find_element(By.ID,"tab01_JABF100100_txtJuminCD_textboxInput").send_keys(self.test_data.get("qap010_28070703_txtJuminCD",""))
        
        # 48 （還付充当）検索条件入力画面: 「検索」ボタンクリック
        self.find_element(By.ID,"tab01_JABF100100_WrCmnBtn05_button").click()
        
        # 49 （還付充当）該当者一覧画面: 表示
        
        # 50 （還付充当）該当者一覧画面: No「1」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_btnNo_1_1_button").click()
        
        # 51 還付更正管理画面: 表示
        
        # 52 還付更正管理画面: 「印刷」ボタンクリック
        self.find_element(By.ID,"tab01_JABF100500_printbtn_button").click()
        
        # 53 （還付充当）印刷指示画面: 表示
        
        # 54 （還付充当）印刷指示画面: 過誤納金還付(充当)通知書にチェック
        self.find_element(By.ID,"tab01_JAPF000200_baseCheckBox0chk0").click()
        
        # 55 （還付充当）印刷指示画面: 過誤納金還付(充当)請求書にチェック
        self.find_element(By.ID,"tab01_JAPF000200_baseCheckBox1chk0").click()
        
        # 56 （還付充当）印刷指示画面: 過誤納金還付(充当)領収書にチェック
        self.find_element(By.ID,"tab01_JAPF000200_baseCheckBox3chk0").click()
        
        # 57 （還付充当）印刷指示画面: 過誤納金還付(充当)命令書にチェック
        self.find_element(By.ID,"tab01_JAPF000200_baseCheckBox2chk0").click()
        
        # 58 （還付充当）印刷指示画面: 文書番号：1234
        self.find_element(By.ID,"tab01_JAPF000200_txtBunshoNo_textboxInput").send_keys("")
        self.find_element(By.ID,"tab01_JAPF000200_txtBunshoNo_textboxInput").send_keys("1234")
        
        # 59 （還付充当）印刷指示画面: 「印刷」ボタンクリック
        self.find_element(By.ID,"tab01_JAPF000200_printBtn_button").click()
        
        # 60 印刷確認ダイアログ: 表示
        self.screen_shot("印刷確認ダイアログ_59")
        
        # 61 印刷確認ダイアログ: 「はい」ボタンクリック
        self.find_element(By.ID,"tempId__5").click()
        
        # 62 印刷確認ダイアログ: No「1」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_WrBtnNo_1_1_button").click()
        
        # 63 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_62")
        
        # 64 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 65 PDF「過誤納金還付(充当)通知書」: 表示
        self.screen_shot("PDF「過誤納金還付(充当)通知書」_64")
        
        # 66 印刷確認ダイアログ: No「2」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_WrBtnNo_2_1_button").click()
        
        # 67 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_66")
        
        # 68 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 69 PDF「過誤納金還付(充当)請求書」: 表示
        self.screen_shot("PDF「過誤納金還付(充当)請求書」_68")
        
        # 70 印刷確認ダイアログ: No「3」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_WrBtnNo_3_1_button").click()
        
        # 71 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_70")
        
        # 72 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 73 PDF「過誤納金還付(充当)領収書」: 表示
        self.screen_shot("PDF「過誤納金還付(充当)領収書」_72")
        
        # 74 印刷確認ダイアログ: No「4」ボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_WrBtnNo_4_1_button").click()
        
        # 75 画面下部ファイル操作ダイアログ: 表示
        self.screen_shot("画面下部ファイル操作ダイアログ_74")
        
        # 76 画面下部ファイル操作ダイアログ: 「ファイルを開く」ボタンクリック
        # 手動操作
        
        # 77 PDF「過誤納金還付(充当)命令書」: 表示
        self.screen_shot("PDF「過誤納金還付(充当)命令書」_76")
        
