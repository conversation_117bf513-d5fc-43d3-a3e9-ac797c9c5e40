from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010_28110106(KodomoSiteTestCaseBase):
    """TestQAP010_28110106"""

    def setUp(self):
        super().setUp()
    
    # 支払に関わる各種通知書が作成できることを確認する。
    def test_QAP010_28110106(self):
        """支払通知書作成"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: バッチ管理クリック
        # 4 メインメニュー画面: 即時実行クリック
        # 5 メインメニュー画面: 「スケジュール個別追加」ボタンダブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # 6 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_6")
        
        # 7 スケジュール個別追加画面: 業務名：子ども子育て支援 サブシステム名：認可外支払管理 処理名：補助金支払通知書出力処理 処理区分：
        # 8 スケジュール個別追加: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM_1 = case_data.get("ShoriNM_1")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM_1)
        self.screen_shot("スケジュール個別追加_8")
        
        # 9 スケジュール個別追加画面: 「(QPNBN01400) 補助金支払対象一覧出力」のNoボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()
        
        # 10 実行指示画面: 表示
        self.screen_shot("実行指示画面_10")
        
        # 11 実行指示画面: 処理区分:初期値のまま（両方）
        # 12 実行指示画面: 所管区：空白のまま
        # 13 実行指示画面: 支所：空白のまま
        # 14 実行指示画面: 発行年月日：初期値のまま（当日）
        # 15 実行指示画面: 振込年月日：初期値のまま（当日）
        # 16 実行指示画面: 郵便区内特別有無：初期値のまま（無）
        
        # 17 実行指示画面: 「実行」ボタン押下
        self.screen_shot("実行指示画面_17")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        self.click_button_by_label("実行")
        
        # 18 実行結果管理画面: 「検索」ボタン押下
        #self.click_button_by_label("検索")
        
        # 19 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_19")
        
        # 20 メインメニュー画面: 「結果管理」クリック
        # 21 メインメニュー画面: 「納品物確認」ボタンダブルクリック
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])
        
        # 22 納品物確認画面: 表示
        self.screen_shot("納品物確認画面_22")
        
        # 23 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：認可外支払管理 処理名：補助金支払通知書出力処理 処理区分：
        # 24 納品物管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_1)
        
        # 25 納品物管理画面: 「施設等利用給付費支払対象一覧.csv」の「ダウンロード」ボタン押下
        # 26 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_26")
        # 27 ファイルダウンロード画面: 「施設等利用給付費支払対象一覧.csv」のNoボタン押下
        # 28 ファイルダウンロード画面: 保存ボタン押下
        # 29 ファイルダウンロード画面: ×ボタン押下
        # 30 ファイルダウンロード画面: 閉じるボタン押下
        self.pdf_download("施設等利用給付費支払対象一覧.csv", "ファイルダウンロード画面_26")
        self.click_button_by_label("閉じる")
        
        # 31 納品物管理画面: 「QAPP701000_施設等利用給付費支払対象一覧.pdf」の「ダウンロード」ボタン押下
        # 32 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_32")
        # 33 ファイルダウンロード画面: 「QAPP701000_施設等利用給付費支払対象一覧.pdf」のNoボタン押下
        # 34 ファイルダウンロード画面: 保存ボタン押下
        # 35 ファイルダウンロード画面: ×ボタン押下
        # 36 ファイルダウンロード画面: 閉じるボタン押下
        self.pdf_download("QAPP701000_施設等利用給付費支払対象一覧.pdf", "ファイルダウンロード画面_32")
        self.click_button_by_label("閉じる")

        # 37 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 39 実行結果管理画面: パンくずの「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        self.wait_page_loaded()
        # 40 スケジュール個別追加画面: 「(QPNBN01500) 補助金支払通知書出力 」のNoボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()
        
        # 41 実行指示画面: 表示
        self.screen_shot("実行指示画面_41")
        
        # 42 実行指示画面: 所管区：空白のまま
        # 43 実行指示画面: 支所：空白のまま
        # 44 実行指示画面: 文書番号：空白のまま

        # 45 実行指示画面: 「実行」ボタン押下
        self.screen_shot("実行指示画面_45")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        self.click_button_by_label("実行")
        
        # 46 実行結果管理画面: 「検索」ボタン押下
        #self.click_button_by_label("検索")
        
        # 47 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_47")
        
        # 48 メインメニュー画面: 「結果管理」クリック
        # 49 メインメニュー画面: 「納品物確認」ボタンダブルクリック
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 50 納品物確認画面: 表示
        self.screen_shot("納品物確認画面_50")
        
        # 51 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：認可外支払管理 処理名：補助金支払通知書出力処理 処理区分：
        # 52 納品物管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_1)
        
        # 53 納品物管理画面: 「QAPP701100_施設等利用費支払決定通知書（償還）.pdf」の「ダウンロード」ボタン押下
        # 54 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_54")
        # 55 ファイルダウンロード画面: 「QAPP701100_施設等利用費支払決定通知書（償還）.pdf」のNoボタン押下
        # 56 ファイルダウンロード画面: 保存ボタン押下
        # 57 ファイルダウンロード画面: ×ボタン押下
        # 58 ファイルダウンロード画面: 閉じるボタン押下
        self.pdf_download("QAPP701100_施設等利用費支払決定通知書（償還）.pdf", "ファイルダウンロード画面_54")
        self.click_button_by_label("閉じる")
        
        # 59 納品物管理画面: 「QAPP701400_施設等利用費請求明細.pdf」の「ダウンロード」ボタン押下
        # 60 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_60")
        # 61 ファイルダウンロード画面: 「QAPP701400_施設等利用費請求明細.pdf」のNoボタン押下
        # 62 ファイルダウンロード画面: 保存ボタン押下
        # 63 ファイルダウンロード画面: ×ボタン押下
        # 64 ファイルダウンロード画面: 閉じるボタン押下
        self.pdf_download("QAPP701400_施設等利用費請求明細.pdf", "ファイルダウンロード画面_60")
        self.click_button_by_label("閉じる")
        
        # 65 納品物管理画面: 「QAPP701600_施設等利用費支払決定通知書（法定代理受領）.pdf」の「ダウンロード」ボタン押下
        # 66 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_66")
        # 67 ファイルダウンロード画面: 「QAPP701600_施設等利用費支払決定通知書（法定代理受領）.pdf」のNoボタン押下
        # 68 ファイルダウンロード画面: 保存ボタン押下
        # 69 ファイルダウンロード画面: ×ボタン押下
        # 70 ファイルダウンロード画面: 閉じるボタン押下
        self.pdf_download("QAPP701600_施設等利用費支払決定通知書（法定代理受領）.pdf", "ファイルダウンロード画面_66")
        self.click_button_by_label("閉じる")
        
        # 71 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 73 実行結果管理画面: パンくずの「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        
        # 74 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_74")
        
        # 75 スケジュール個別追加画面: 業務名：子ども子育て支援 サブシステム名：認可外支払管理 処理名：補助金支払却下通知書出力処理 処理区分：
        # 76 スケジュール個別追加: 「検索」ボタン押下
        gyomuNM = case_data.get("GyomuNM")
        subSystemNM = case_data.get("SubSystemNM")
        shoriNM_2 = case_data.get("ShoriNM_2")
        self.kennsakujyoukenn_nyuuryoku(gyomuNM, subSystemNM, shoriNM_2)
        self.screen_shot("スケジュール個別追加_76")
        
        # 77 スケジュール個別追加画面: 「(QPNBN01600) 補助金支払却下対象一覧出力」のNoボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()
        
        # 78 実行指示画面: 表示
        self.screen_shot("実行指示画面_78")
        
        # 79 実行指示画面: 所管区：空白のまま
        # 80 実行指示画面: 支所：空白のまま
        # 81 実行指示画面: 決定年月日開始：空白のまま
        # 82 実行指示画面: 決定年月日終了：空白のまま
        # 83 実行指示画面: 代理施設有無：空白のまま
        # 84 実行指示画面: 発行年月日：初期値のまま（当日）
        # 85 実行指示画面: 並び順：初期値のまま
        # 86 実行指示画面: 再発行区分：有
        # 87 実行指示画面: 郵便区内特別有無：初期値のまま（無）
        params = [
            {"title":"再発行区分", "type": "select", "value": case_data.get("再発行区分")}
        ]
        self.set_job_param_kodomo(params)

        # 88 実行指示画面: 「実行」ボタン押下
        self.screen_shot("実行指示画面_88")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        self.click_button_by_label("実行")
        
        # 89 実行結果管理画面: 「検索」ボタン押下
        #self.click_button_by_label("検索")

        # 90 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_90")
        
        # 91 メインメニュー画面: 「結果管理」クリック
        # 92 メインメニュー画面: 「納品物確認」ボタンダブルクリック
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 93 納品物確認画面: 表示
        self.screen_shot("納品物確認画面_93")
        
        # 94 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：認可外支払管理 処理名：補助金支払却下通知書出力処理 処理区分：
        # 95 納品物管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_2)

        # 96 納品物管理画面: 「QAPP701200_補助金支払却下対象一覧.pdf」の「ダウンロード」ボタン押下
        # 97 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_97")
        # 98 ファイルダウンロード画面: 「QAPP701200_補助金支払却下対象一覧.pdf」のNoボタン押下
        # 99 ファイルダウンロード画面: 保存ボタン押下
        # 100 ファイルダウンロード画面: ×ボタン押下
        # 101 ファイルダウンロード画面: 閉じるボタン押下
        self.pdf_download("QAPP701200_補助金支払却下対象一覧.pdf", "ファイルダウンロード画面_97")
        self.click_button_by_label("閉じる")
        
        # 102 納品物管理画面: 「補助金支払却下対象一覧.csv」の「ダウンロード」ボタン押下
        # 103 ファイルダウンロード画面: 表示
        # self.screen_shot("ファイルダウンロード画面_103")
        # 104 ファイルダウンロード画面: 「補助金支払却下対象一覧.csv」のNoボタン押下
        # 105 ファイルダウンロード画面: 保存ボタン押下
        # 106 ファイルダウンロード画面: ×ボタン押下
        # 107 ファイルダウンロード画面: 閉じるボタン押下
        self.pdf_download("補助金支払却下対象一覧.csv", "ファイルダウンロード画面_103")
        self.click_button_by_label("閉じる")
        
        # 108 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 110 実行結果管理画面: パンくずの「スケジュール個別追加」クリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        self.wait_page_loaded()

        # 111 スケジュール個別追加画面: 「(QPNBN01700) 補助金支払却下通知書出力」のNoボタン押下
        self.click_button_by_label("2")
        self.wait_page_loaded()

        # 112 実行指示画面: 表示
        self.screen_shot("実行指示画面_112")
        
        # 113 実行指示画面: 所管区：空白のまま
        # 114 実行指示画面: 支所：空白のまま
        
        # 115 実行指示画面: 「実行」ボタン押下
        self.screen_shot("実行指示画面_115")
        #self.driver.fullscreen_window()
        self.driver.set_window_size(1450,2000)
        self.click_button_by_label("実行")
        
        # 116 実行結果管理画面: 「検索」ボタン押下
        #self.click_button_by_label("検索")

        # 117 実行結果管理画面: 表示
        # Assert: 処理結果「正常終了」を確認
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.screen_shot("実行結果管理画面_117")
        
        # 118 メインメニュー画面: 「結果管理」クリック
        # 119 メインメニュー画面: 「納品物確認」ボタンダブルクリック
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 120 納品物確認画面: 表示
        self.screen_shot("納品物確認画面_120")
        
        # 121 納品物管理画面: 業務名：子ども子育て支援 サブシステム名：認可外支払管理 処理名：補助金支払却下通知書出力処理 処理区分：
        # 122 納品物管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2",gyomuNM, subSystemNM, shoriNM_2)

        # 123 納品物管理画面: 「QAPP701300_補助金支払却下通知書.pdf」の「ダウンロード」ボタン押下
        # 124 ファイルダウンロード画面: 表示
        self.screen_shot("ファイルダウンロード画面_124")
        # 125 ファイルダウンロード画面: 「QAPP701300_補助金支払却下通知書.pdf」のNoボタン押下
        # 126 ファイルダウンロード画面: 保存ボタン押下
        # 127 ファイルダウンロード画面: ×ボタン押下
        # 128 ファイルダウンロード画面: 閉じるボタン押下
        self.pdf_download("QAPP701300_補助金支払却下通知書.pdf", "ファイルダウンロード画面_124")
        self.click_button_by_label("閉じる")

        # 129 納品物管理画面: 「納品物確認」タブ×クリック
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # 130 スケジュール個別追加画面: 「スケジュール個別追加」タブ×ボタンクリック
        # 131 メインメニュー画面: ×ボタン押下
        
