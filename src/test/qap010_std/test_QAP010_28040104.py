import datetime
from ntpath import join
import unicodedata
from base.kodomo_case import KodomoSiteTestCaseBase



class TestQAP010_28040104(KodomoSiteTestCaseBase):
    """TestQAP010_28040104"""

    def setUp(self):
        super().setUp()
    
    # 支給認定証の返還にかかわる情報の登録を行えることを確認する。
    def test_QAP010_28040104(self):
        """支給認定証返還情報登録"""

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「子ども子育て支援」をクリック
        
        # 4 メインメニュー画面: 「世帯情報」をクリック
        
        # 5 メインメニュー画面: 「検索」をダブルクリック
        self.goto_menu(["子ども子育て支援","世帯情報","検索"])

        # 6 検索画面: 住民コード：<パラメータ>入力
        # Assert: パラメータ化
        case_data_02 = self.common_test_data.get("TestQAP010_28040102", {})
        self.find_element_by_id(u"tab01_QAZF100001_txtJuminCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAZF100001_txtJuminCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAZF100001_txtJuminCD_textboxInput").send_keys(case_data_02.get("sql_params").get("児童_宛名C"))
           
        # 7 検索画面: 「検索（Enter）」ボタンクリック
        self.screen_shot("検索画面_7")
        self.click_button_by_label("検索(Enter)")
        
        # 8 世帯履歴画面: 表示
        self.screen_shot("世帯履歴画面_8")
        
        # 9 世帯履歴画面: 「No1」ボタン押下
        self.click_button_by_label("1")

        # 10 世帯台帳画面: 表示
        self.screen_shot("世帯台帳画面_10")
        
        # 11 世帯台帳画面: 「No1」ボタン押下
        self.click_button_by_label("1")

        # 12 児童台帳画面: 表示
        self.screen_shot("児童台帳画面_12")
        
        # 13 児童台帳画面: 「No1」ボタン押下
        self.click_button_by_label("1")
        
        # 14 支給認定情報: 表示
        self.screen_shot("支給認定情報_14")
        
        # 15 支給認定情報: 「支給認定結果」タブをクリック
        self.find_element_by_xpath("//li[@id='tab01_QAPF103600_sikyuninteikekka_li']/a").click()
        
        # 16 支給認定情報: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 17 支給認定情報: 備考：「返還受付日」（当日）、「返還期限」（翌日）を設定
        txtShinseiNaiyoBiko="「返還受付日」（令和６年６月６日）、「返還期限」（令和６年６月７日）"
        self.find_element_by_id(u"tab01_QAPF103600_txtNinteiKekkaBiko_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF103600_txtNinteiKekkaBiko_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF103600_txtNinteiKekkaBiko_textboxInput").send_keys(txtShinseiNaiyoBiko)
      
        
        # 18 支給認定情報: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.screen_shot("支給認定情報_18")
        
        # 19 支給認定情報: 「更新してよろしいですか？」「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()

        # 20 支給認定情報: 「検索」タブ×ボタン押下
        
        # 21 メインメニュー画面: ×ボタン押下
        
