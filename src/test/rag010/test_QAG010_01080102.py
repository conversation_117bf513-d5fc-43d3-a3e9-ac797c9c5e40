import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG010_01080102(FukushiSiteTestCaseBase):
    """TestQAG010_01080102"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code")
        params = {"DELETE_ATENA_CODE": atena_code}
        self.exec_sqlfile("RAG010.sql", params=params)
        super().setUp()

    # 18歳到達している対象者に対して、申請事由「新規」、申請理由「新規申請」の申請を登録できることを確認する。
    def test_QAG010_01080102(self):
        """新規申請情報登録"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG010")
        # 1 自立支援医療(更生医療)資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 2 自立支援医療(更生医療)資格管理画面: 申請事由「新規」選択申請理由「新規申請」選択
        time.sleep(2)
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=case_data.get("ShinseiShubetsu", ""))
        time.sleep(2)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("ShinseiRiyuu", ""))
        shinsei_ymd = case_data.get("shinsei_ymd", "")
        self.screen_shot("自立支援医療(更生医療)資格管理画面_2")

        # 3 自立支援医療(更生医療)資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 4 自立支援医療(更生医療)資格管理画面: 申請日「20230701」入力
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinsei_ymd)

        # 5 自立支援医療(更生医療)資格管理画面: 管理場所「○○」選択
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text=case_data.get("tanto_shokatsuku", ""))

        # 6 自立支援医療(更生医療)資格管理画面: 担当支所「○○」選択
        time.sleep(2)
        self.form_input_by_id(idstr="TantoShiShoCmb", value=case_data.get("tanto_shisho", ""))

        # 7 自立支援医療(更生医療)資格管理画面: 職権「チェック」入力
        self.form_input_by_id(idstr="ShokkenChkBox", value="1")

        # 8 自立支援医療(更生医療)資格管理画面: 受付場所「○○」選択
        self.form_input_by_id(idstr="UketsukeBashoCmb", value=case_data.get("uketsuke_basho", ""))

        # 9 自立支援医療(更生医療)資格管理画面: 担当場所「○○」選択
        self.form_input_by_id(idstr="TantoBashoCmb", value=case_data.get("tanto_basho", ""))

        # 10 自立支援医療(更生医療)資格管理画面: 所得判定年度「令和05年」を選択   
        self.form_input_by_id(idstr="ShotokuHanteiNendoCmb", text=case_data.get("shotoku_hantei_nendo", ""))
        time.sleep(2)

        # 11 自立支援医療(更生医療)資格管理画面: 交付方法「○○」選択
        self.form_input_by_id(idstr="KofuHohoCmb", value=case_data.get("kofu_hoho", ""))
        time.sleep(2)

        # 12 自立支援医療(更生医療)資格管理画面: 添付書類有無1「チェック」入力 # NG
        self.form_input_by_id(idstr="TempShoruiChkBox", value="1")

        # 13 自立支援医療(更生医療)資格管理画面: 予定期間「12」入力
        self.form_input_by_id(idstr="TxtYoteiKikan", value="12")

        # 14 自立支援医療(更生医療)資格管理: 受付番号「12345」入力
        # self.form_input_by_id(idstr="TxtUketsukeBango", value="12345")

        # 15 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_15")

        # 16 自立支援医療(更生医療)資格管理画面: 「届出保険情報」ボタン押下
        self.click_button_by_label("届出保険情報")

        # 17 届出保険情報画面: 表示
        self.screen_shot("届出保険情報画面_17")

        # 18 届出保険情報画面: 「保険情報取得」ボタン押下
        self.click_button_by_label("保険情報取得")

        # 19 届出保険情報画面: 表示
        self.screen_shot("届出保険情報画面_19")

        # 20 届出保険情報画面: 保険者番号を削除
        self.form_input_by_id(idstr="TxtHokenshaBango", value="")

        # 21 届出保険情報画面: 加入状況「保険加入」選択
        self.form_input_by_id(idstr="CmbKanyuJokyo1", text=case_data.get("kanyu_jokyo1", ""))

        # 22 届出保険情報画面: 保険の種類「健保組合」選択 ※保険者検索で保険者が決まると対応する値が入る。

        # 23 届出保険情報画面: 「保険者検索」ボタン押下
        self.click_button_by_label("保険者検索")

        # 24 保険者検索画面: 保険者漢字「○○」入力
        self.form_input_by_id(idstr="txtKanjiMeisho", value=case_data.get("txt_kanji_meisho", ""))

        # 25 保険者検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 26 保険者検索画面: 検索結果である保険者情報より「1」ボタン押下
        self.click_button_by_label("1")

        # 27 保険者検索画面: 扶養者区分「扶養者」選択
        self.form_input_by_id(idstr="CmbFuyousha", text=case_data.get("fuyousha", ""))

        # 28 保険者検索画面: 「被保険者検索」ボタン押下
        self.click_button_by_label("被保険者検索")

        # 29 保険者検索画面: 世帯員一覧より「1」ボタン押下
        self.click_button_by_label("1")

        # 30 保険者検索画面: 記号「１２３」入力
        self.form_input_by_id(idstr="TxtKigo", value=case_data.get("txt_kigo", ""))

        # 31 保険者検索画面: 番号「４５６７」入力
        self.form_input_by_id(idstr="TxtBango", value=case_data.get("txt_bango", ""))

        # 32 保険者検索画面: 個人番号「１２」入力
        self.form_input_by_id(idstr="TxtKojinShikiBango", value=case_data.get("txt_kojin_shiki_bango", ""))

        # 33 保険者検索画面: 資格取得日「20230701」入力
        self.form_input_by_id(idstr="TxtShikakuShutokuYMD", value=case_data.get("shikaku_shutoku_ymd", ""))

        # 34 保険者検索画面: 「追加」ボタン押下
        self.click_button_by_label("追加")
        self.screen_shot("保険者検索画面_34")

        # 35 保険者検索画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        self.alert_ok()

        # 36 自立支援医療(更生医療)資格管理画面: 「支給認定基準世帯員作成ボタン」ボタン押下
        self.click_button_by_label("支給認定基準世帯作成")

        # 37 保険世帯情報画面: 表示
        self.screen_shot("保険世帯情報画面_37")

        # 38 保険世帯情報画面: 「保険情報」ボタン押下
        self.click_button_by_label("保険情報")

        # 39 保険情報一覧画面: 表示
        self.screen_shot("保険情報一覧画面_39")

        # 40 保険情報一覧画面: 「戻る」ボタン押下
        self.return_click()

        # 41 保険世帯情報画面: 保険の種類「国民健康保険」選択 # NG
        self.form_input_by_id(idstr="CmbHokenKbn",text=case_data.get("hokenshurui1", ""))

        # 42 保険世帯情報画面: 「保険情報取得」ボタン押下
        self.click_button_by_label("保険情報取得")

        # 43 保険世帯情報画面: 表示
        self.screen_shot("保険世帯情報画面_43")

        # 44 保険世帯情報画面: 保険の種類「健康保険等」選択 # NG
        self.form_input_by_id(idstr="CmbHokenKbn",text=case_data.get("hokenshurui2", ""))

        # 45 保険世帯情報画面: 1人目（本人）該当日「20230701」入力本人から見た続柄「本人」選択受給者との関係「本人」選択被用者区分「被扶養者」選択公的年金等の種類「障害年金」選択所得確定区分「確定」選択 # NG
        self.form_input_by_id(idstr="CmbHZoku_1",value='本人')
        self.form_input_by_id(idstr="CmbJKankei_1",value='本人')
        self.form_input_by_id(idstr="CmbKazeiKbn_1",value='課税')
        self.form_input_by_id(idstr="CmbNenkinSyurui_1",value='障害年金')
        self.form_input_by_id(idstr="CmbHiyosya_1",value='被扶養者')
        self.form_input_by_id(idstr="CmbShotokuKbn_1",value='確定')

        # # 46 保険世帯情報画面: 2人目（父）該当日「20230701」入力本人から見た続柄「父」選択受給者との関係「その他」選択被用者区分「被保険者」選択所得確定区分「確定」選択 # NG
        self.form_input_by_id(idstr="CmbHZoku_2",value='父')
        self.form_input_by_id(idstr="CmbJKankei_2",value='その他')
        self.form_input_by_id(idstr="CmbKazeiKbn_2",value='課税')
        self.form_input_by_id(idstr="CmbHiyosya_2",value='被保険者')
        self.form_input_by_id(idstr="CmbShotokuKbn_2",value='確定')

        self.click_by_id("ChkDel_2")

        # # 47 保険世帯情報画面: 3人目（母）該当日「20230701」入力本人から見た続柄「母」選択受給者との関係「その他」選択被用者区分「（空白）」所得確定区分「確定」選択 # NG
        # self.form_input_by_id(idstr="CmbHZoku_3",value='母')
        # self.form_input_by_id(idstr="CmbJKankei_3",value='その他')
        # self.form_input_by_id(idstr="CmbKazeiKbn_3",value='市町村民税課税')
        # self.form_input_by_id(idstr="CmbHiyosya_3",value='被保険者')
        # self.form_input_by_id(idstr="CmbShotokuKbn_3",value='確定')

        # # ※世帯員の人数により以下を適宜追加
        # self.form_input_by_id(idstr="CmbHZoku_4",value='その他')
        # self.form_input_by_id(idstr="CmbJKankei_4",value='その他')
        # self.form_input_by_id(idstr="CmbKazeiKbn_4",value='市町村民税課税')
        # self.form_input_by_id(idstr="CmbHiyosya_4",value='被保険者')

        # self.form_input_by_id(idstr="CmbHZoku_5",value='その他')
        # self.form_input_by_id(idstr="CmbJKankei_5",value='その他')
        # self.form_input_by_id(idstr="CmbKazeiKbn_5",value='市町村民税課税')
        # self.form_input_by_id(idstr="CmbHiyosya_5",value='被保険者')

        # 48 保険世帯情報画面: 「所得情報」ボタン押下
        self.click_button_by_label("所得情報")

        # 49 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_49")

        # 50 住民税世帯情報画面: 対象者世帯一覧「1」ボタン押下
        self.click_button_by_label("1")

        # 51 住民税個人情報画面: 表示
        self.screen_shot("住民税個人情報画面_51")

        # 52 住民税個人情報画面: 「入力」ボタン押下
        self.click_button_by_label("入力")

        # 53 住民税個人情報画面: 特定扶養人数(入力)「0」入力 # NG
        self.form_input_by_id(idstr="Ctrl_7_3_6",value='0')

        # 54 住民税個人情報画面: 「控除廃止前想定税額 」ボタン押下
        self.click_button_by_label("控除廃止前想定所得割額 ")

        # 55 住民税個人情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 56 住民税個人情報画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("住民税個人情報画面_56")

        # 57 住民税個人情報画面: 「戻る」ボタン押下
        self.return_click()

        # 58 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_58")

        # 59 住民税世帯情報画面: 「戻る」ボタン押下
        self.return_click()

        # 60 保険世帯情報画面: 「税情報取得」ボタン押下
        self.click_button_by_label("税情報取得")

        # 61 保険世帯情報画面: 「収入計算」ボタン押下
        self.click_button_by_label("収入計算")

        # 62 保険世帯情報画面: 表示
        self.screen_shot("保険世帯情報画面_62")

        # 63 保険世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        self.alert_ok()

        # 64 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_64")

        # 65 自立支援医療(更生医療)資格管理画面: 「世帯所得反映」ボタン押下
        self.click_button_by_label("世帯所得反映")

        # 66 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_66")

        # 67 自立支援医療(更生医療)資格管理画面: 特例世帯適用「チェック」入力、高額療養費多数回該当「チェック」入力、重度かつ継続「チェック」入力
        self.form_input_by_id(idstr="ChkTokuSetaiTekiyo", value="1")
        self.form_input_by_id(idstr="ChkKogakuRyoyoHi", value="1")
        self.form_input_by_id(idstr="ChkJyudo", value="1")

        # 68 自立支援医療(更生医療)資格管理画面: 「所得区分」ボタン押下
        self.click_button_by_label("所得区分")

        # 69 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_69")

        # 70 自立支援医療(更生医療)資格管理画面: 減免区分「０円」入力
        self.form_input_by_id(idstr="GenmenKubunCmb", text="０円")
        time.sleep(2)

        # 71 自立支援医療(更生医療)資格管理画面: 生保移行防止減免対象区分コード「チェック」入力
        self.form_input_by_id(idstr="ChkSeihoIkoBoshi", value="1")

        # 72 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_72")

        # 73 自立支援医療(更生医療)資格管理画面: 受診機関管理＞「追加」ボタン押下
        self.click_button_by_label("追加")

        # 74 自立支援医療(更生医療)資格管理画面: 「医療機関」ボタン押下
        self.click_button_by_label("医療機関")

        # 75 医療機関検索画面: 所在地区分「指定なし」選択点数表「医科」選択医療機関名漢字「○○」入力
        self.form_input_by_id(idstr="Shozaichi_"+str(case_data.get("iryoukikan_shozaichi_kubun_2", "")), value="1")
        self.form_input_by_id(idstr="CmbTensuhyo",value=case_data.get("tensuhyo", ""))
        self.form_input_by_id(idstr="TxtKanjiMeisho", value=case_data.get("txt_kanji_meisho2", ""))

        # 76 医療機関検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 77 医療機関検索画面: 検索結果である医療機関一覧より「1」ボタン押下
        self.click_button_by_label("1")

        # 78 自立支援医療(更生医療)資格管理画面: 認定決定お知らせ有無「チェック」入力
        self.form_input_by_id(idstr="ChkOshiraseUmu_1", value="1")

        # 79 自立支援医療(更生医療)資格管理画面: 有効開始日「20230701」入力有効終了日「20240630」入力
        self.form_input_by_id(idstr="TxtJushinYukokikanKaishi_1", value="20230701")
        self.form_input_by_id(idstr="TxtJushinYukokikanShuryo_1", value="20240630")

        # 80 自立支援医療(更生医療)資格管理画面: 入通院区分「入院外」選択
        self.form_input_by_id(idstr="NyugaiKubunCmb_1", text="入院外")


        # 81 自立支援医療(更生医療)資格管理画面: 「医療機関」ボタン押下
        self.click_button_by_label("追加")
        self.click_by_id(idstr="CmdIryoKensaku_2")

        # 82 医療機関検索画面: 所在地区分「指定なし」選択点数表「保険薬局」選択医療機関名漢字「○○」入力
        self.form_input_by_id(idstr="Shozaichi_"+str(case_data.get("iryoukikan_shozaichi_kubun_3", "")), value="1")
        self.form_input_by_id(idstr="CmbTensuhyo",value=case_data.get("tensuhyo2", ""))
        self.form_input_by_id(idstr="TxtKanjiMeisho", value=case_data.get("txt_kanji_meisho3", ""))

        # 83 医療機関検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 84 医療機関検索画面: 検索結果である医療機関一覧より「1」ボタン押下
        self.click_button_by_label("1")

        # 85 自立支援医療(更生医療)資格管理画面: 認定決定お知らせ有無「チェック」入力
        self.form_input_by_id(idstr="ChkOshiraseUmu_2", value="1")

        # 86 自立支援医療(更生医療)資格管理画面: 有効開始日「20230701」入力有効終了日「20240630」入力
        self.form_input_by_id(idstr="TxtJushinYukokikanKaishi_2", value="20230701")
        self.form_input_by_id(idstr="TxtJushinYukokikanShuryo_2", value="20240630")

        # 87 自立支援医療(更生医療)資格管理画面: 入通院区分「入院外」選択
        self.form_input_by_id(idstr="NyugaiKubunCmb_2", text="入院外")

        # 88 医療機関検索画面: 所在地区分「指定なし」選択点数表「訪問看護ステーション」選択医療機関名漢字「○○」入力
        self.click_button_by_label("追加")
        self.click_by_id(idstr="CmdIryoKensaku_3")
        self.form_input_by_id(idstr="Shozaichi_"+str(case_data.get("iryoukikan_shozaichi_kubun_4", "")), value="1")
        self.form_input_by_id(idstr="CmbTensuhyo",value=case_data.get("tensuhyo3", ""))
        self.form_input_by_id(idstr="TxtKanjiMeisho", value=case_data.get("txt_kanji_meisho4", ""))

        # 89 医療機関検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 90 医療機関検索画面: 検索結果である医療機関一覧より「1」ボタン押下
        self.click_button_by_label("1")

        # 91 自立支援医療(更生医療)資格管理画面: 認定決定お知らせ有無「チェック」入力
        self.form_input_by_id(idstr="ChkOshiraseUmu_3", value="1")

        # 92 自立支援医療(更生医療)資格管理画面: 有効開始日「20230701」入力有効終了日「20240630」入力
        self.form_input_by_id(idstr="TxtJushinYukokikanKaishi_3", value="20230701")
        self.form_input_by_id(idstr="TxtJushinYukokikanShuryo_3", value="20240630")

        # 93 自立支援医療(更生医療)資格管理画面: 入通院区分「入院外」選択
        self.form_input_by_id(idstr="NyugaiKubunCmb_3", text="入院外")

        # 94 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_94")

        # 95 自立支援医療(更生医療)資格管理画面: 公費負担の対象となる障害「じん臓機能障害（透析）」選択
        self.form_input_by_id(idstr="ShogaiCmb", text="じん臓機能障害（透析）")

        # 96 自立支援医療(更生医療)資格管理画面: 医療の方針「○○」選択
        self.form_input_by_id(idstr="HoshinCmb", value=case_data.get("hoshin", ""))

        # 97 自立支援医療(更生医療)資格管理画面: 医療の具体的方針「医療の具体的方針入力テスト４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★」入力
        self.form_input_by_id(idstr="TxtAreaHoshinShosai", value="医療の具体的方針入力テスト４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★")

        # 98 自立支援医療(更生医療)資格管理画面: 特定疾病療養受給者証の有無「チェック」入力入院日数「123」入力通院日数「123」入力医療費概算額「1234567」入力障害の状況「障害の状況入力テスト★２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★」
        self.form_input_by_id(idstr="ChkShippei", value="1")
        self.form_input_by_id(idstr="TxtNyuinNissu", value="123")
        self.form_input_by_id(idstr="TxtTsuinNissu", value="123")
        self.form_input_by_id(idstr="TxtIryohiGaisangaku", value="1234567")
        self.form_input_by_id(idstr="TxtAreaShogaiNoJokyo", value="障害の状況入力テスト★２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★")

        # 99 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_99")

        # 100 自立支援医療(更生医療)資格管理画面: 「住記情報」ボタン押下
        self.click_button_by_label("住記情報")

        # 101 世帯一覧画面: 表示
        self.screen_shot("世帯一覧画面_101")

        # 102 世帯一覧画面: 世帯一覧「1」Noボタン押下
        self.click_button_by_label("1")

        # 103 住記情報画面: 表示
        self.screen_shot("住記情報画面_103")

        # 104 住記情報画面: 「戻る」ボタン押下
        self.return_click()

        # 105 世帯一覧画面: 表示
        self.screen_shot("世帯一覧画面_105")

        # 106 世帯一覧画面: 「戻る」ボタン押下
        self.return_click()

        # 107 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_107")

        # 108 自立支援医療(更生医療)資格管理画面: 「所得情報」ボタン押下
        self.click_button_by_label("所得情報")

        # 109 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_109")

        # 110 住民税世帯情報画面: 対象者世帯一覧「1」ボタン押下
        self.click_button_by_label("1")

        # 111 住民税個人情報画面: 表示
        self.screen_shot("住民税個人情報画面_111")

        # 112 住民税個人情報画面: 「戻る」ボタン押下
        self.return_click()

        # 113 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_113")

        # 114 住民税世帯情報画面: 「戻る」ボタン押下
        self.return_click()

        # 115 自立支援医療(更生医療)資格管理画面: 「生活保護情報」ボタン押下
        self.click_button_by_label("生活保護情報")

        # 116 生活保護情報画面: 表示
        self.screen_shot("生活保護情報画面_116")

        # 117 世帯一覧画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 118 生活保護情報画面: ケース番号「12345」入力 マスタ区分「入力」選択   生保開始日「20230701」入力生保喪失日「（空白）」
        self.form_input_by_id(idstr="TbxCase", value="12345")
        self.form_input_by_id(idstr="CmbMstKbn", text="入力")
        self.form_input_by_id(idstr="TbxDayBegin", value=case_data.get("Seiho_Begin_ymd", ""))
        self.form_input_by_id(idstr="TbxDayEnd", value=case_data.get("Seiho_End_ymd", ""))

        # 119 生活保護情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(2)
        self.alert_ok()

        # 120 生活保護情報画面: 表示
        # Assert: 画面上部に「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("生活保護情報画面_120")

        # 121 生活保護情報画面: 「戻る」ボタン押下
        self.return_click()

        # 122 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_122")

        # 123 自立支援医療(更生医療)資格管理画面: 「手帳情報」ボタン押下
        self.click_button_by_label("手帳情報")

        # 124 手帳情報画面: 表示
        self.screen_shot("手帳情報画面_124")

        # 125 手帳情報画面: 「戻る」ボタン押下
        self.return_click()

        # 126 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_126")

        # 127 自立支援医療(更生医療)資格管理画面: 「住所管理」ボタン押下
        self.click_button_by_label("住所管理")

        # 128 住所管理画面: 表示
        self.screen_shot("住所管理画面_128")

        # 129 住所管理画面: 「送付先」ボタン押下
        self.click_button_by_label("送付先")

        # 130 住所管理画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        self.click_by_id(idstr="RadioJigyo")

        # 131 住所管理画面: 「住記情報索引」ボタン押下
        self.click_button_by_label("住記情報索引")
        time.sleep(2)
        self.alert_ok()

        self.form_input_by_id(idstr="TxtKanaShimei_Uji", value="ア")
        self.form_input_by_id(idstr="TxtKanaShimei_Na", value="イ")
        self.form_input_by_id(idstr="TxtShimei_Uji", value="阿")
        self.form_input_by_id(idstr="TxtShimei_Na", value="伊")

        self.screen_shot("住所管理画面_131")

        # 132 住所管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 133 住所管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_base_header("登録しました")
        self.screen_shot("住所管理画面_133")

        # 134 住所管理画面: 「戻る」ボタン押下
        self.return_click()

        # 135 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_135")

        # 136 自立支援医療(更生医療)資格管理画面: 「連絡先管理」ボタン押下
        self.click_button_by_label("連絡先管理")

        # 137 連絡先管理画面: 表示
        self.screen_shot("連絡先管理画面_137")

        # 138 連絡先管理画面: 本人連絡先の「追加」ボタン押下
        self.click_button_by_label("追加")

        # 139 連絡先管理画面: 業務区分　？？電話番号公開範囲　？？優先順位「携帯電話番号」選択公開/非公開「公開」チェック自宅電話番号 「111-1111-1111」入力留守電「チェック」入力携帯電話番号 「222-2222-2222」入力FAX番号「333-3333-3333」入力勤務先名「勤務先入力テスト」入力勤務先電話番号「444-4444-4444」入力勤務先内線番号「123」入力勤務先FAX番号「555-5555-5555」入力   メールアドレス1「<EMAIL>」メールアドレス2「<EMAIL>」備考「備考テスト入力１２３４５６７８９０」種別「その他」選択連絡先「種別連絡先入力テスト１２３４５６７８９０」備考「種別備入力テスト１２３４５６７８９０」
        self.form_input_by_id(idstr="CmbYusenTEL", text="携帯電話番号")
        self.form_input_by_id(idstr="kokai0", value="1")
        self.form_input_by_id(idstr="TxtTelJitaku", value="111-1111-1111")
        self.form_input_by_id(idstr="ChkRusuden", value="1")
        self.form_input_by_id(idstr="TxtTelKeitai", value="222-2222-2222")
        self.form_input_by_id(idstr="TxtFaxJitaku", value="333-3333-3333")
        self.form_input_by_id(idstr="TxtKinmuSaki", value="勤務先入力テスト")
        self.form_input_by_id(idstr="TxtTelKinmu", value="444-4444-4444")
        self.form_input_by_id(idstr="TxtFaxKinmu", value="555-5555-5555")
        self.form_input_by_id(idstr="TxtMail", value="<EMAIL>")
        self.form_input_by_id(idstr="TxtMail2", value="<EMAIL>")
        self.form_input_by_id(idstr="TxtBikou", value="ビコウ")
        self.form_input_by_id(idstr="SelectHanyo1", text="その他")
        self.form_input_by_id(idstr="TxtHanyoRenraku1", value="種別連絡先入力テスト１２３４５６７８９０")
        self.form_input_by_id(idstr="TxtHanyoBiko1", value="種別備入力テスト１２３４５６７８９０")
        self.screen_shot("連絡先管理画面_139")

        # 140 連絡先管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")

        # 141 連絡先管理画面: 表示
        self.screen_shot("連絡先管理画面_141")

        # 142 連絡先管理画面: 緊急連絡先入力の「追加」ボタン押下
        self.click_by_id(idstr="BtnTsuika_Kinkyu")

        # 143 連絡先管理画面: 「個人検索」ボタン押下
        self.click_button_by_label("個人検索")

        # 144 個人検索画面: 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=case_data.get("renrakuatena", ""))

        # 145 個人検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 146 連絡先管理画面: 続柄「その他」選択自宅電話番号 「111-1111-1111」入力留守電「チェック」入力携帯電話番号 「222-2222-2222」入力FAX番号「333-3333-3333」入力勤務先「勤務先入力テスト１２３４５６７８９０」入力勤務先電話番号「444-4444-4444」入力
        self.form_input_by_id(idstr="CmbTsudukigara", text="その他")
        self.form_input_by_id(idstr="TxtTelJitaku_Kinkyu", value="111-1111-1111")
        self.form_input_by_id(idstr="ChkRusuden_Kinkyu", value="1")
        self.form_input_by_id(idstr="TxtTelKeitai_Kinkyu", value="222-2222-2222")
        self.form_input_by_id(idstr="TxtFaxJitaku_Kinkyu", value="333-3333-3333")
        self.form_input_by_id(idstr="TxtKinmuSaki_Kinkyu", value="勤務先入力テスト１２３４５６７８９０")
        self.form_input_by_id(idstr="TxtTelKinmu_Kinkyu", value="444-4444-4444")

        # 147 連絡先管理画面: 「登録」ボタン押下
        self.click_by_id("BtnTouroku_Kinkyu")
        self.click_by_id_and_ok("BtnTouroku_Kinkyu")
        
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")

        # 148 連絡先管理画面: 表示
        self.screen_shot("連絡先管理画面_148")

        # 149 連絡先管理画面: 「戻る」ボタン押下
        self.return_click()

        # 150 自立支援医療(更生医療)資格管理画面: 「メモ情報」ボタン押下
        self.click_button_by_label("メモ情報")

        # 151 メモ情報画面: 表示
        self.screen_shot("メモ情報画面_151")

        # 152 メモ情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 153 メモ情報画面: 内容「メモ入力テスト１２３４５６７８９０」
        self.form_input_by_id(idstr="TxtNaiyo", value="メモ入力テスト１２３４５６７８９０")

        # 154 メモ情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")

        # 155 メモ情報画面: 表示
        self.screen_shot("メモ情報画面_155")

        # 156 メモ情報画面: 「戻る」ボタン押下
        self.return_click()

        # 157 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_157")

        # 158 自立支援医療(更生医療)資格管理画面: 「保険情報」ボタン押下
        self.click_button_by_label("保険情報")

        # 159 保険情報画面: 表示
        self.screen_shot("保険情報画面_159")

        # 160 保険情報画面: 「戻る」ボタン押下
        self.return_click()

        # 161 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_161")

        # 162 自立支援医療(更生医療)資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 163 自立支援医療(更生医療)資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("自立支援医療(更生医療)資格管理画面_163")

        # 164 自立支援医療(更生医療)資格管理画面: 「資格履歴」ボタン押下
        self.click_button_by_label("資格履歴")

        # 165 資格履歴画面: 表示
        self.screen_shot("資格履歴画面_165")

        # 166 資格履歴画面: 「戻る」ボタン押下
        self.return_click()
