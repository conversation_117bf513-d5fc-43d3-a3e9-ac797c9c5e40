import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG010_01089906(FukushiSiteTestCaseBase):
    """TestQAG010_01089906"""
    
    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 副本データができることを確認する。
    def test_QAG010_01089906(self):
        """更生医療_住記異動自動喪失処理・住記異動自動喪失処理"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        gyomu_select = case_data.get("gyomu_select", "")
        jigyo_select = case_data.get("jigyo_select", "")
        shoriKubun_select = case_data.get("shoriKubun_select", "")
        shoriBunrui_select = case_data.get("shoriBunrui_select", "")
        chiho_nm = case_data.get("chiho_nm", "")
        chiho_nm1 = case_data.get("chiho_nm1", "")
        ymd_1 = case_data.get("ymd_1", "")
        ymd_2 = case_data.get("ymd_2", "")
        self.do_login()
        # 1 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 2 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_2")

        # 3 バッチ起動画面: 業務：障害 | 事業：自立支援医療(更生医療) | 処理区分：バッチ処理 | 処理分類：月次処理
        self.form_input_by_id(idstr="GyomuSelect", text=gyomu_select)
        self.form_input_by_id(idstr="JigyoSelect", text=jigyo_select)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=shoriKubun_select)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=shoriBunrui_select)
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 「更生医療_副本登録用データ抽出処理」のNoボタン押下
        # NG: 更生医療_副本登録用データ抽出処理が見当たりません。
        self.click_batch_job_button_by_label(chiho_nm)
         # 5 バッチ起動画面: 更新年月日（はじめ）「○○」を入力
        self.find_element(By.ID, "item6").send_keys(ymd_1)
        # 「更新年月日（おわり）」入力
        self.find_element(By.ID, "item7").send_keys(ymd_2)
        # バッチ起動画面表示(エビデンス取得)
        self.screen_shot("バッチ起動画面_4")

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")

        # バッチ起動画面表示(エビデンス取得)
        self.screen_shot("バッチ起動画面_5")

        # 7 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 8 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_8")

        # 9 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 10 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_10")

        # 11 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 12 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_13")   

        # 14 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_14")

        # 15 ジョブ帳票履歴画面: 「実績調査（別添様式3自立支援医療（更生医療）の実績）」のNoボタン押下
        # 14 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 16 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 17 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_17")

        # 18 帳票（PDF）: ×ボタン押下でPDFを閉じる
        self.screen_shot("ジョブ帳票履歴画面_18")

        #######################	住記異動自動喪失処理##################################

        # 19 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 20 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 21 バッチ起動画面: 「住記異動自動喪失処理」のNoボタン押下
        self.click_batch_job_button_by_label(chiho_nm1)
        #住記異動日（はじめ）        
        self.find_element(By.ID, "item5").send_keys(ymd_1)
        #住記異動日（おわり）
        self.find_element(By.ID, "item6").send_keys(ymd_2)

        # 22 バッチ起動画面: 「処理開始」ボタン押下
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 23 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 24 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_24")

        # 25 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 26 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_26")

