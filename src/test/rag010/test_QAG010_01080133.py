import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

#転用元シナリオ:TestQAG010_01080113
class TestQAG010_01080133(FukushiSiteTestCaseBase):
    """TestQAG010_01080133"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 新規申請した住民に対し決定登録ができることを確認する。受給者番号が付与されていることを確認する。
    def test_QAG010_01080133(self):
        """認定結果の登録_決定_"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        kettei_ymd = case_data.get("kettei_ymd", "")
        kettei_kekka = case_data.get("kettei_kekka", "")
        kaishi_ymd = case_data.get("kaishi_ymd", "")
        shuryo_ymd = case_data.get("shuryo_ymd", "")
        tokureikaishi_ymd = case_data.get("tokureikaishi_ymd", "")
        tokureishuryo_ymd = case_data.get("tokureishuryo_ymd", "")
        tekiyo_ymd = case_data.get("tekiyo_ymd", "")
        kofu_ymd = case_data.get("kofu_ymd", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG010")

        # 1 自立支援医療(更生医療)資格管理: 「修正」ボタン押下
        self.click_by_id(idstr="CmdButton1_1")
        self.click_button_by_label("修正")

        # 2 自立支援医療(更生医療)資格管理: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理_2")

        # 3 自立支援医療(更生医療)資格管理: 却下理由削除
        # self.find_element_by_id("TxtAreaKyakkaRiyu").clear()
        
        # 4 自立支援医療(更生医療)資格管理: 決定日「20230701」入力決定結果「決定」選択
        self.form_input_by_id(idstr="TxtKetteiYMD", value=kettei_ymd)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=kettei_kekka)

        # 5 自立支援医療(更生医療)資格管理: 有効期間開始日「20230701」入力経過的特例有効期間開始日「（空白）」経過的特例有効期間終了日「（空白）」受給者証適用開始日「20230701」入力交付日「20230701」入力
        self.form_input_by_id(idstr="TxtKaishiYMD", value=kaishi_ymd)
        self.form_input_by_id(idstr="TxtShuryoYMD", value=shuryo_ymd)
        self.form_input_by_id(idstr="TxtKeikaTokureiKaishiYMD", value=tokureikaishi_ymd)
        self.form_input_by_id(idstr="TxtKeikaTokureiShuryoYMD", value=tokureishuryo_ymd)
        self.form_input_by_id(idstr="TxtJukyushashoTekiyoYMD", value=tekiyo_ymd)
        self.form_input_by_id(idstr="TxtKofuYMD", value=kofu_ymd)

        # 6 自立支援医療(更生医療)資格管理: 備考「備考入力テスト８９★１２３４５６７８９★」入力
        self.form_input_by_id(idstr="TxtHanyoBiko1", value="備考入力テスト８９★１２３４５６７８９★")

        # 7 自立支援医療(更生医療)資格管理: 直近５年間の給付状況「直近５年間の給付状況１２３４５６７８９★」入力
        self.form_input_by_id(idstr="TxtAreaTyokkin5Nenkan", value="直近５年間の給付状況１２３４５６７８９★")

        # 8 自立支援医療(更生医療)資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 9 自立支援医療(更生医療)資格管理: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("自立支援医療(更生医療)資格管理_9")

        # 10 自立支援医療(更生医療)資格管理: 表示
        # Assert: 受給者番号が発番されたことを確認する。
        self.screen_shot("自立支援医療(更生医療)資格管理_10")
