import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01070301(FukushiSiteTestCaseBase):
    """TestQAJ010_01070301"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01070301"]
        sql_params = {"TARGET_GYOMU_CODE":"QAJ010",
                      "TARGET_JUKYUSYA_CODE":case_data.get("JukyushaNo", "")}
        self.exec_sqlfile("TestQAJ010_01070301.sql", params=sql_params)
        super().setUp()
    
    # 過誤申立情報を登録できることを確認する。
    def test_QAJ010_01070301(self):
        """過誤申立情報登録_者_"""
        
        case_data = self.test_data["TestQAJ010_01070301"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「支払処理」ボタン押下
        self.find_element(By.ID,"CmdProcess28_4").click()
        
        # 4 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_4")
        
        # 5 サブメニュー画面: 「過誤申立書情報管理」ボタン押下
        self.find_element(By.ID,"CmdButton11").click()
        
        # 6 過誤申立書情報検索画面: 表示
        self.screen_shot("過誤申立書情報検索画面_6")
        
        # 7 過誤申立書情報検索画面: 事業「障害者総合支援」選択
        self.find_element(By.ID,"JigyoCmb").send_keys("障害者総合支援")
        
        # 8 過誤申立書情報検索画面: 表示
        self.screen_shot("過誤申立書情報検索画面_8")
        
        # 9 過誤申立書情報検索画面: 「サービス提供年月」入力「様式」選択「請求年月」入力「事業所」入力
        self.find_element(By.ID,"TxtServiceTeikyoYM").send_keys("")
        self.find_element(By.ID,"TxtServiceTeikyoYM").send_keys(case_data.get("ServiceTeikyoYM",""))
        self.find_element(By.ID,"CmbYoshiki").send_keys(case_data.get("Yoshiki",""))
        self.find_element(By.ID,"TxtSeikyuYM").send_keys("")
        self.find_element(By.ID,"TxtSeikyuYM").send_keys(case_data.get("SeikyuYM",""))
        self.find_element(By.ID,"TxtJigyoshaCd").send_keys("")
        self.find_element(By.ID,"TxtJigyoshaCd").send_keys(case_data.get("Jigyosha",""))
        
        # 10 過誤申立書情報検索画面: 「事業所」ボタン押下
        self.find_element(By.ID,"CmdJigyosho").click()
        
        # 11 過誤申立書情報検索画面: 表示
        self.screen_shot("過誤申立書情報検索画面_11")
        
        # 12 過誤申立書情報検索画面: 「受給者証番号」入力
        self.find_element(By.ID,"TxtJukyushaNo").send_keys("")
        self.find_element(By.ID,"TxtJukyushaNo").send_keys(case_data.get("JukyushaNo",""))

        # 13 過誤申立書情報検索画面: 「検索」ボタン押下
        self.find_element(By.ID,"CmdKensaku").click()
        
        # 14 過誤申立書情報検索画面: 表示
        self.screen_shot("過誤申立書情報検索画面_14")
        
        # 15 過誤申立書情報検索画面: 「追加」ボタン押下
        self.find_element(By.ID,"CmdTsuika").click()
        
        # 16 過誤申立書情報登録画面: 表示
        self.screen_shot("過誤申立書情報登録画面_16")
        
        # 17 過誤申立書情報登録画面: 申立理由「台帳誤り修正による市区町村申立の過誤調整」選択過誤区分コード「通常過誤」選択
        self.find_element(By.ID,"CmbMoushitate").send_keys("台帳誤り修正による市区町村申立の過誤調整")
        self.find_element(By.ID,"CmbKagoKubun").send_keys("通常過誤")
        
        # 18 過誤申立書情報登録画面: 「登録」ボタン押下
        self.find_element(By.ID,"CmdTouroku").click()
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())

        # 19 過誤申立書情報登録画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("過誤申立書情報登録画面_19")
        
        # 20 過誤申立書情報登録画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click()
        
        # 21 過誤申立書情報検索画面: 表示
        self.screen_shot("過誤申立書情報検索画面_21")
        
        # 22 過誤申立書情報検索画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click()
        
        # 23 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_23")
        
        # 24 サブメニュー画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click()
        
        # 25 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_25")
        
