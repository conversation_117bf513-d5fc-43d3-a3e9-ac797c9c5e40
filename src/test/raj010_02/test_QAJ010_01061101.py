import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01061101(FukushiSiteTestCaseBase):
    """TestQAJ010_01061101"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01061101"]
        super().setUp()
    
    # バッチ処理で更新申請勧奨（有効期間終了対象者一覧処理）を実行できることを確認する。以下の帳票を出力できることを確認する。・有効期間終了対象者一覧・障害支援区分更新のお知らせ・支給期間更新のお知らせ（介護給付費等）・利用者負担適用期間更新のお知らせ（介護給付費等）・支給申請書兼利用者負担額減額・免除等申請書・世帯状況・収入等申告書（介護給付費等）・計画相談支援給付費・障害児相談支援給付費支給申請書・計画相談支援・障害児相談支援依頼（変更）届出書・利用計画案提出依頼書
    def test_QAJ010_01061101(self):
        """お知らせ通知・支給申請書等出力_者_"""
        
        case_data = self.test_data["TestQAJ010_01061101"]
        atena_code = case_data.get("atena_code", "")
        date = datetime.date.today()
        today = format(date, '%Y%m%d')

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")
        
        # 4 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_4")
        
        # 5 バッチ起動画面: 業務「障害」選択
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        
        # 6 バッチ起動画面: 事業「障害者総合支援」選択
        self.form_input_by_id(idstr="JigyoSelect", text="障害者総合支援")
        
        # 7 バッチ起動画面: 処理区分「随時処理」選択
        self.form_input_by_id(idstr="ShoriKubunSelect", text="随時処理")
        
        # 8 バッチ起動画面: 処理分類「有効期間終了対象者一覧」選択
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="有効期間終了対象者一覧")
        
        # 9 バッチ起動画面: No「１」　有効期間終了対象者一覧　ボタン押下
        self.click_button_by_label("1")
        self.screen_shot("バッチ起動画面_9")
        
        # 10 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_10")
        
        # 11 バッチ起動画面: 「対象年月開始」入力「対象年月終了」入力
        params = [
            {"title": "対象年月開始", "type": "text", "value": case_data.get("start_month", "")},
            {"title": "対象年月終了", "type": "text", "value": case_data.get("end_month", "")},
            {"title": "申請期限_障害支援区分更新のお知らせ", "type": "text", "value": today},
            {"title": "申請期限_支給期間更新のお知らせ", "type": "text", "value": today},
            {"title": "申請期限_利用者負担適用期間更新のお知らせ", "type": "text", "value": today}
        ]
        self.set_job_params(params)
        
        # 12 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        
        # 13 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_13")
        
        # 14 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        
        # 15 ジョブ実行履歴画面: 「検索」ボタン押下
        self.click_button_by_label("検索")
        
        # 16 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_16")
        
        # 17 ジョブ実行履歴画面: No.1 有効期間終了対象者一覧の状態が「正常終了」となったらエビデンス取得
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("ジョブ実行履歴画面_17")
        
        # 18 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()
        
        # 19 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_19")
        
        # 20 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.click_button_by_label("検索")
        
        # 21 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_21")
        
        # 22 ジョブ帳票履歴画面: 有効期間終了対象者一覧　のNoボタンを押下
        # self.find_element(By.ID,"Sel1").click()
        self.click_button_by_label("1")
        
        # 23 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        # self.screen_shot("帳票（PDF）_23")
        
        # 24 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_24")
        
        # 25 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_25")
        
        # 26 ジョブ帳票履歴画面: 障害支援区分更新のお知らせ　のNoボタンを押下
        # self.find_element(By.ID,"Sel2").click()
        self.click_button_by_label("2")
        
        # 27 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        # self.screen_shot("帳票（PDF）_27")
        
        # 28 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_28")
        
        # 29 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_29")
        
        # 30 ジョブ帳票履歴画面: 支給期間更新のお知らせ（介護給付費等）　のNoボタンを押下
        # self.find_element(By.ID,"Sel3").click()
        self.click_button_by_label("3")
        
        # 31 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        # self.screen_shot("帳票（PDF）_31")
        
        # 32 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_32")
        
        # 33 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_33")
        
        # 34 ジョブ帳票履歴画面: 利用者負担適用期間更新のお知らせ（介護給付費等）のNoボタンを押下
        # self.find_element(By.ID,"Sel4").click()
        self.click_button_by_label("4")
        
        # 35 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        # self.screen_shot("帳票（PDF）_35")
        
        # 36 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_36")
        
        # 37 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_37")
        
        # 38 ジョブ帳票履歴画面: 支給申請書兼利用者負担額減額・免除等申請書のNoボタンを押下
        # self.find_element(By.ID,"Sel5").click()
        self.click_button_by_label("5")
        
        # 39 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        # self.screen_shot("帳票（PDF）_39")
        
        # 40 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_40")
        
        # 41 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_41")
        
        # 42 ジョブ帳票履歴画面: 世帯状況・収入等申告書（介護給付費等）のNoボタンを押下
        # self.find_element(By.ID,"Sel6").click()
        self.click_button_by_label("6")
        
        # 43 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        # self.screen_shot("帳票（PDF）_43")
        
        # 44 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_44")
        
        # 45 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_45")
        
        # 46 ジョブ帳票履歴画面: 計画相談支援給付費・障害児相談支援給付費支給申請書のNoボタンを押下
        # self.find_element(By.ID,"Sel7").click()
        self.click_button_by_label("7")
        
        # 47 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        # self.screen_shot("帳票（PDF）_47")
        
        # 48 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_48")
        
        # 49 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_49")
        
        # 50 ジョブ帳票履歴画面: 計画相談支援・障害児相談支援依頼（変更）届出書のNoボタンを押下
        # self.find_element(By.ID,"Sel8").click()
        self.click_button_by_label("8")
        
        # 51 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        # self.screen_shot("帳票（PDF）_51")
        
        # 52 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_52")
        
        # 53 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_53")
        
        # 54 ジョブ帳票履歴画面: 利用計画案提出依頼書のNoボタンを押下
        # self.find_element(By.ID,"Sel9").click()
        self.click_button_by_label("9")
        
        # 55 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        # self.screen_shot("帳票（PDF）_55")
        
        # 56 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_56")
        
        # 57 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_57")
        
        # 58 ジョブ帳票履歴画面: 「戻る」ボタン押下
        self.return_click()
        
        # 59 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_59")
        
