import time
from datetime import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01061702(FukushiSiteTestCaseBase):
    """TestQAJ010_01061702"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01061702"]
        super().setUp()
    
    # 審査会審査結果を登録できることを確認する。二次判定結果を登録できることを確認する。
    def test_QAJ010_01061702(self):
        """二次判定結果登録"""
        
        case_data = self.test_data["TestQAJ010_01061702"]
        atena_code = case_data.get("atena_code", "")
        #システム日付を変数に設定
        date = datetime.now()
        today = format(date, '%Y%m%d')
        today_date = date.strftime("%d").lstrip("0")
        Day = '{0}'.format(today_date)

        self.do_login()
        # 2 サブメニュー画面: 「審査会結果登録」ボタン押下
        self.click_button_by_label("市町村審査会管理")
        self.click_button_by_label("審査会結果登録")
        
        # 3 審査会検索画面: 表示
        self.screen_shot("審査会検索画面_3")
        
        # 4 審査会検索画面: 数値ボタン押下
        Day = "Day_" + str(today_date)
        self.click_by_id(Day)
        
        # 5 審査会検索画面: 「結果登録」ボタン押下
        self.click_by_id("Target1")
        
        # 6 審査会結果登録画面: 表示
        self.screen_shot("審査会結果登録画面_6")
        
        # 7 審査会結果登録画面: No「１」ボタン押下
        self.click_button_by_label("1")
        
        # 8 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_8")
        
        # 9 障害福祉サービス申請管理画面: 二次判定年月日「20230701」入力二次判定結果「区分６」選択認定有効期間「36」入力認定年月日「20230601」入力認定期間開始日「20230601」入力変更理由「特記・その他 」選択認定理由「その他」選択認定理由テキスト「その他認定理由入力テスト」入力審査会意見「審査会意見入力テスト」入力
        self.form_input_by_id(idstr="TxtNinteiYMD", value=today)
        self.form_input_by_id(idstr="ShinsaHanteiKekkaCmb", text="区分６")
        self.form_input_by_id(idstr="TxtNinteiGekkan", value="36")
        self.form_input_by_id(idstr="TxtNinteiYMD2", value=today)
        self.form_input_by_id(idstr="TxtNinteikikanStartYMD", value=today)
        self.form_input_by_id(idstr="HenkoRiyuCmb", text="特記・その他")
        self.form_input_by_id(idstr="HenkoRiyuChkBox1", value="1")
        self.form_input_by_id(idstr="NinteiRiyuCmb", text="その他")
        self.form_input_by_id(idstr="TxtNinteiRiyu", value="その他認定理由入力テスト")
        self.form_input_by_id(idstr="TxtShinsakaiIken", value="審査会意見入力テスト")
        
        # 10 障害福祉サービス申請管理画面: 「認定期間開始日」ボタン押下
        self.click_button_by_label("認定期間開始日")
        
        # 11 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_11")
        
        # 12 障害福祉サービス申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 13 審査会結果登録画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("審査会結果登録画面_13")
        
        # 14 審査会結果登録画面: 「戻る」ボタン押下
        # self.return_click()
        self.find_element(By.ID,"GOBACK").click()
        time.sleep(3)
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        
        # 15 審査会検索画面: 表示
        self.screen_shot("審査会検索画面_15")
        
        # 16 審査会検索画面: 「戻る」ボタン押下
        self.return_click()
        
        # 17 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_17")
        
        # 18 サブメニュー画面: 「戻る」ボタン押下
        self.return_click()
        
        # 19 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_19")
        
        # 20 メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")
        
        # 21 個人検索画面: 表示
        self.screen_shot("個人検索画面_21")
        
        # 22 個人検索画面: 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=case_data.get("atena_code",""))
        
        # 23 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        
        # 24 受給状況画面: 表示
        self.screen_shot("受給状況画面_24")
        
        # 25 受給状況画面: 「障害者総合支援」ボタン押下
        self.click_button_by_label("障害者総合支援")
        
        # 26 障害福祉サービス受給者台帳画面: 表示
        self.screen_shot("障害福祉サービス受給者台帳画面_26")
        
        # 27 障害福祉サービス受給者台帳画面: 「障害者総合支援申請管理」ボタン押下
        
        # 28 障害福祉サービス申請管理画面: 表示
        # self.screen_shot("障害福祉サービス申請管理画面_28")
        
