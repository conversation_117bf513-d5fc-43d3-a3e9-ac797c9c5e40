import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01070105(FukushiSiteTestCaseBase):
    """TestQAJ010_01070105"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01070105"]
        super().setUp()
    
    # 障害者総合支援　二次審査結果情報を登録できることを確認する。取込済の一次審査結果票情報に対して二次審査日を登録できることを確認する。
    def test_QAJ010_01070105(self):
        """一次審査結果票情報登録_者_"""
        
        case_data = self.test_data["TestQAJ010_01070105"]
        atena_code = case_data.get("atena_code", "")
        seikyu_year_month_start = case_data.get("seikyu_year_month_start", "")
        seikyu_year_month_end = case_data.get("seikyu_year_month_end", "")
        niji_shinsa_ymd = case_data.get("niji_shinsa_ymd", "")
        sql_params = {"SEIKYU_YEAR_MONTH_START": case_data.get("seikyu_year_month_start", "")}
        sql_params = {"SEIKYU_YEAR_MONTH_END": case_data.get("seikyu_year_month_end", "")}
        self.exec_sqlfile("TestQAJ010_01070105.sql", params=sql_params)

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 障害者総合支援メニュー内の「支払処理」ボタン押下
        self.click_button_by_label("支払処理")
        
        # 4 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_4")
        
        # 5 サブメニュー画面: 「一次審査結果票情報管理」ボタン押下
        self.click_button_by_label("一次審査結果票情報管理")
        
        # 6 一次審査結果票情報検索画面: 表示
        self.screen_shot("一次審査結果票情報検索画面_6")
        
        # 7 一次審査結果票情報検索画面: 事業「障害者総合支援」選択「請求年月」入力「請求年月」入力
        self.form_input_by_id(idstr="CmbJigyo", text="障害者総合支援")
        self.form_input_by_id(idstr="TxtKaishiYM", value=seikyu_year_month_start)
        self.form_input_by_id(idstr="TxtShuryoYM", value=seikyu_year_month_end)
        
        # 8 一次審査結果票情報検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")
        
        # 9 一次審査結果票情報検索画面: 表示
        self.screen_shot("一次審査結果票情報検索画面_9")
        
        # 10 一次審査結果票情報検索画面: No.「1」ボタン押下
        self.click_button_by_label("1")
        
        # 11 一次審査結果票情報登録画面: 表示
        self.screen_shot("一次審査結果票情報登録画面_11")
        
        # 12 一次審査結果票情報登録画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        
        # 13 一次審査結果票情報登録画面: 表示
        self.screen_shot("一次審査結果票情報登録画面_13")
        
        # 14 一次審査結果票情報登録画面: 「二次審査日」入力
        self.form_input_by_id(idstr="TxtShinsaYMD", value=case_data.get("niji_shinsa_ymd",""))
        
        # 15 一次審査結果票情報登録画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 16 一次審査結果票情報登録画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("一次審査結果票情報登録画面_16")
        
        # 17 一次審査結果票情報登録画面: 「戻る」ボタン押下
        self.return_click()
        
        # 18 一次審査結果票情報検索画面: 表示
        self.screen_shot("一次審査結果票情報検索画面_18")
        
        # 19 一次審査結果票情報検索画面: 「戻る」ボタン押下
        self.return_click()
        
        # 20 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_20")
        
        # 21 サブメニュー画面: 「戻る」ボタン押下
        self.return_click()
        
        # 22 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_22")
        
