import datetime
import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01070101(FukushiSiteTestCaseBase):
    """TestQAJ010_01070101"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01070101"]
        super().setUp()
    
    # 障害者総合支援　一次審査結果資料（CSV）を取込できることを確認する。
    def test_QAJ010_01070101(self):
        """一次審査結果資料等取込_者_"""
        
        case_data = self.test_data["TestQAJ010_01070101"]
        atena_code = case_data.get("atena_code", "")
        seikyu_year_month = case_data.get("seikyu_year_month", "")
        seikyu_ymd = case_data.get("seikyu_ymd", "")
        sql_params = {"SEIKYU_YEAR_MONTH": seikyu_year_month,
                      "SEIKYU_YMD": seikyu_ymd}
        self.exec_sqlfile("TestQAJ010_01070101.sql", params=sql_params)

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")
        
        # 4 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_4")
        
        # 5 バッチ起動画面: 業務「障害」選択
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        
        # 6 バッチ起動画面: 事業「障害者総合支援」選択
        self.form_input_by_id(idstr="JigyoSelect", text="障害者総合支援")
        
        # 7 バッチ起動画面: 処理区分「国保連連携　支払業務」選択
        self.form_input_by_id(idstr="ShoriKubunSelect", text="国保連連携　支払業務")
        
        # 8 バッチ起動画面: 処理分類「一次審査結果票情報取込」選択
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="一次審査結果票情報取込")
        
        # 9 バッチ起動画面: No「１」　一次審査結果票情報取込処理 　ボタン押下
        self.click_button_by_label("1")
        self.screen_shot("バッチ起動画面_9")
        
        # 10 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_10")
        
        # 11 バッチ起動画面: 「請求年月」入力
        # ジョブパラ定義
        params = [
            {"title": "請求年月日", "type": "text", "value": seikyu_ymd}
        ]
        # ジョブパラに入力
        self.set_job_params(params)
        
        # 12 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        
        # 13 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_13")
        
        # 14 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        
        # 15 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        
        # 16 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_16")
        
        # 17 ジョブ実行履歴画面: No.1 一次審査結果票情報取込処理の状態が「正常終了」となったらエビデンス取得
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 18 ジョブ実行履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()
        
        # 19 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_19")
        
        # 20 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 21 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_21")
        
        # 22 バッチ起動画面: No「2」　一次審査結果票情報取込エラーリスト出力処理　ボタン押下
        self.click_button_by_label("2")
        
        # 23 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_23")
        
        # 24 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        
        # 25 バッチ起動画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: 対象データ有の場合メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。対象データ無の場合 メッセージエリアに「プレビュー対象の帳票がありません」と表示されていることを確認する。
        # self.assert_message_base_header("プレビューを表示しました")
        self.screen_shot("バッチ起動画面_25")
        
        # 26 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_26")
        
        # 27 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_27")
        
        # 28 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 29 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_29")
        
        # 30 バッチ起動画面: 処理分類「給付費請求書情報取込」選択
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="給付費請求書情報取込")
        
        # 31 バッチ起動画面: No「１」　給付費請求書情報取込処理 　ボタン押下
        self.click_button_by_label("1")
        self.screen_shot("バッチ起動画面_31")
        
        # 32 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_32")
        
        # 33 バッチ起動画面: 「請求年月日」入力
        # ジョブパラ定義
        params = [
            {"title":"請求年月日", "type": "text", "value": seikyu_ymd}
        ]
        # ジョブパラに入力
        self.set_job_params(params)
        
        # 34 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        
        # 35 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_35")
        
        # 36 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        
        # 37 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        
        # 38 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_38")
        
        # 39 ジョブ実行履歴画面: No.1 給付費請求書情報取込処理の状態が「正常終了」となったらエビデンス取得
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 40 ジョブ実行履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()
        
        # 41 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_41")
        
        # 42 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 43 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_43")
        
        # 44 バッチ起動画面: No「2」　給付費請求書情報取込エラーリスト出力処理　ボタン押下
        self.click_button_by_label("2")
        
        # 45 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_45")
        
        # 46 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        
        
        # 47 バッチ起動画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: 対象データ有の場合メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。対象データ無の場合 メッセージエリアに「プレビュー対象の帳票がありません」と表示されていることを確認する。
        # self.assert_message_base_header("プレビューを表示しました")
        self.screen_shot("バッチ起動画面_47")
        
        # 48 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_48")
        
        # 49 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_49")
        
        # 50 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 51 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_51")
        
        # 52 バッチ起動画面: No「3」　給付費請求書情報取込一覧表出力処理　ボタン押下
        self.click_button_by_label("3")
        
        # 53 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_53")
        
        # 54 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        
        
        # 55 バッチ起動画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: 対象データ有の場合メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。対象データ無の場合 メッセージエリアに「プレビュー対象の帳票がありません」と表示されていることを確認する。
        # self.assert_message_base_header("プレビューを表示しました")
        self.screen_shot("バッチ起動画面_55")
        
        # 56 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_56")
        
        # 57 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_57")
        
        # 58 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 59 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_59")
        
        # 60 バッチ起動画面: No「4」　特例給付費請求書情報取込処理 　ボタン押下
        self.click_button_by_label("4")
        self.screen_shot("バッチ起動画面_60")
        
        # 61 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_61")
        
        # 62 バッチ起動画面: 「請求年月日」入力
        # ジョブパラ定義
        params = [
            {"title":"請求年月日", "type": "text", "value": seikyu_ymd}
        ]
        # ジョブパラに入力
        self.set_job_params(params)
        
        # 63 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        
        
        # 64 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_64")
        
        # 65 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        
        # 66 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        
        # 67 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_67")
        
        # 68 ジョブ実行履歴画面: No.1　特例給付費請求書情報取込処理の状態が「正常終了」となったらエビデンス取得
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 69 ジョブ実行履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()
        
        # 70 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_70")
        
        # 71 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 72 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_72")
        
        # 73 バッチ起動画面: No「5」　特例給付費請求書情報取込エラーリスト出力処理　ボタン押下
        self.click_button_by_label("5")
        
        # 74 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_74")
        
        # 75 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        
        
        # 76 バッチ起動画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: 対象データ有の場合メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。対象データ無の場合 メッセージエリアに「プレビュー対象の帳票がありません」と表示されていることを確認する。
        # self.assert_message_base_header("プレビューを表示しました")
        self.screen_shot("バッチ起動画面_76")
        
        # 77 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_77")
        
        # 78 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_78")
        
        # 79 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 80 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_80")
        
        # 81 バッチ起動画面: No「6」　特例給付費請求書情報取込一覧表出力処理　ボタン押下
        self.click_button_by_label("6")
        
        # 82 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_82")
        
        # 83 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        
        
        # 84 バッチ起動画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: 対象データ有の場合メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。対象データ無の場合 メッセージエリアに「プレビュー対象の帳票がありません」と表示されていることを確認する。
        # self.assert_message_base_header("プレビューを表示しました")
        self.screen_shot("バッチ起動画面_84")
        
        # 85 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_85")
        
        # 86 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_86")
        
        # 87 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 88 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_88")
        
        # 89 バッチ起動画面: 処理分類「相談支援給付費請求書情報取込」選択
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="相談支援給付費請求書情報取込")
        
        # 90 バッチ起動画面: No「１」　相談支援給付費請求書情報取込処理 　ボタン押下
        self.click_button_by_label("1")
        self.screen_shot("バッチ起動画面_90")
        
        # 91 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_91")
        
        # 92 バッチ起動画面: 「請求年月日」入力
        # ジョブパラ定義
        params = [
            {"title":"請求年月日", "type": "text", "value": seikyu_ymd}
        ]
        # ジョブパラに入力
        self.set_job_params(params)
        
        # 93 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        
        
        # 94 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_94")
        
        # 95 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        
        # 96 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        
        # 97 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_97")
        
        # 98 ジョブ実行履歴画面: No.1 相談支援給付費請求書情報取込処理の状態が「正常終了」となったらエビデンス取得
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 99 ジョブ実行履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()
        
        # 100 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_100")
        
        # 101 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 102 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_102")
        
        # 103 バッチ起動画面: No「2」　相談支援給付費請求書情報取込エラーリスト出力処理　ボタン押下
        self.click_button_by_label("2")
        
        # 104 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_104")
        
        # 105 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        
        # 106 バッチ起動画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: 対象データ有の場合メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。対象データ無の場合 メッセージエリアに「プレビュー対象の帳票がありません」と表示されていることを確認する。
        # self.assert_message_base_header("プレビューを表示しました")
        self.screen_shot("バッチ起動画面_106")
        
        # 107 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_107")
        
        # 108 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_108")
        
        # 109 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 110 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_110")
        
        # 111 バッチ起動画面: No「3」　相談支援給付費請求書情報取込一覧表出力処理　ボタン押下
        self.click_button_by_label("3")
        
        # 112 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_112")
        
        # 113 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        
        # 114 バッチ起動画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: 対象データ有の場合メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。対象データ無の場合 メッセージエリアに「プレビュー対象の帳票がありません」と表示されていることを確認する。
        # self.assert_message_base_header("プレビューを表示しました")
        self.screen_shot("バッチ起動画面_114")

        # バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 115 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_115")
        # 帳票履歴画面へ遷移
        self.click_report_log()
        # 今回の処理で作成したPDFのDL（戻値はDLしたファイル数）
        report_dl_count = self.get_job_report_pdf(exec_datetime=exec_datetime)
        # self.return_click()
        
        # 116 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_116")
        
        # 117 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_list()
        
        # 118 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_118")
        
        # 119 バッチ起動画面: 処理分類「明細書等情報取込」選択
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="明細書等情報取込")
        
        # 120 バッチ起動画面: No「１」　明細書情報取込処理 　ボタン押下
        self.click_button_by_label("1")
        self.screen_shot("バッチ起動画面_120")
        
        # 121 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_121")
        
        # 122 バッチ起動画面: 「請求年月日」入力
        # ジョブパラ定義
        params = [
            {"title":"請求年月日", "type": "text", "value": seikyu_ymd}
        ]
        # ジョブパラに入力
        self.set_job_params(params)
        
        # 123 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        
        # 124 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_124")
        
        # 125 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        
        # 126 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        
        # 127 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_127")
        
        # 128 ジョブ実行履歴画面: No.1明細書情報取込処理の状態が「正常終了」となったらエビデンス取得
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 129 ジョブ実行履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()
        
        # 130 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_130")
        
        # 131 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 132 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_132")
        
        # 133 バッチ起動画面: No「2」　明細書情報取込エラーリスト出力処理 　ボタン押下
        self.click_button_by_label("2")
        
        # 134 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_134")
        
        # 135 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        
        # 136 バッチ起動画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: 対象データ有の場合メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。対象データ無の場合 メッセージエリアに「プレビュー対象の帳票がありません」と表示されていることを確認する。
        # self.assert_message_base_header("プレビューを表示しました")
        self.screen_shot("バッチ起動画面_136")
        
        # 137 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_137")
        
        # 138 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_138")
        
        # 139 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 140 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_140")
        
        # 141 バッチ起動画面: No「3」　明細書情報取込一覧表出力処理　ボタン押下
        self.click_button_by_label("3")
        
        # 142 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_142")
        
        # 143 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        
        # 144 バッチ起動画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: 対象データ有の場合メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。対象データ無の場合 メッセージエリアに「プレビュー対象の帳票がありません」と表示されていることを確認する。
        # self.assert_message_base_header("プレビューを表示しました")
        self.screen_shot("バッチ起動画面_144")
        
        # 145 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_145")
        
        # 146 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_146")
        
        # 147 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 148 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_148")
        
        # 149 バッチ起動画面: No「４」　上限額管理結果票情報取込処理　ボタン押下
        self.click_button_by_label("4")
        self.screen_shot("バッチ起動画面_149")
        
        # 150 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_150")
        
        # 151 バッチ起動画面: 「請求年月日」入力
        # ジョブパラ定義
        params = [
            {"title":"請求年月日", "type": "text", "value": seikyu_ymd}
        ]
        # ジョブパラに入力
        self.set_job_params(params)
        
        # 152 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        
        # 153 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_153")
        
        # 154 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        
        # 155 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        
        # 156 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_156")
        
        # 157 ジョブ実行履歴画面: No.1上限額管理結果票情報取込処理の状態が「正常終了」となったらエビデンス取得
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 158 ジョブ実行履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()
        
        # 159 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_159")
        
        # 160 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 161 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_161")
        
        # 162 バッチ起動画面: No「5」　上限額管理結果票情報取込エラーリスト出力処理 　ボタン押下
        self.click_button_by_label("5")
        
        # 163 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_163")
        
        # 164 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        
        # 165 バッチ起動画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: 対象データ有の場合メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。対象データ無の場合 メッセージエリアに「プレビュー対象の帳票がありません」と表示されていることを確認する。
        # self.assert_message_base_header("プレビューを表示しました")
        self.screen_shot("バッチ起動画面_165")
        
        # 166 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_166")
        
        # 167 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_167")
        
        # 168 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 169 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_169")
        
        # 170 バッチ起動画面: No「6」　上限額管理結果票情報取込一覧表出力処理　ボタン押下
        self.click_button_by_label("6")
        
        # 171 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_171")
        
        # 172 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        
        # 173 バッチ起動画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: 対象データ有の場合メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。対象データ無の場合 メッセージエリアに「プレビュー対象の帳票がありません」と表示されていることを確認する。
        # self.assert_message_base_header("プレビューを表示しました")
        self.screen_shot("バッチ起動画面_173")
        
        # 174 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_174")
        
        # 175 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_175")
        
        # 176 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 177 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_177")
        
        # 178 バッチ起動画面: No「7」　実績記録票情報取込処理　ボタン押下
        self.click_button_by_label("7")
        self.screen_shot("バッチ起動画面_178")
        
        # 179 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_179")
        
        # 180 バッチ起動画面: 「請求年月日」入力事業者提供サービスチェック有無のチェックボックスのチェックを外す
        # ジョブパラ定義
        params = [
            {"title":"請求年月日", "type": "text", "value": seikyu_ymd}
        ]
        # ジョブパラに入力
        self.set_job_params(params)
        
        # 181 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 182 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_182")
        
        # 183 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        
        # 184 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        
        # 185 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_185")
        
        # 186 ジョブ実行履歴画面: No.1実績記録票情報取込処理の状態が「正常終了」となったらエビデンス取得
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 187 ジョブ実行履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()
        
        # 188 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_188")
        
        # 189 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 190 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_190")
        
        # 191 バッチ起動画面: No「8」　実績記録票データエラーリスト出力処理 　ボタン押下
        self.click_button_by_label("8")
        
        # 192 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_192")
        
        # 193 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        
        # 194 バッチ起動画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: 対象データ有の場合メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。対象データ無の場合 メッセージエリアに「プレビュー対象の帳票がありません」と表示されていることを確認する。
        # self.assert_message_base_header("プレビューを表示しました")
        self.screen_shot("バッチ起動画面_194")
        
        # 195 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_195")
        
        # 196 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("帳票（PDF）_196")
        
        # 197 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        
        # 198 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_198")
        
        # 199 バッチ起動画面: 「戻る」ボタン押下
        self.return_click()
        
        # 200 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_200")
        
