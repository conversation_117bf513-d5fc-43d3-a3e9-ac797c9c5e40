import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01070103(FukushiSiteTestCaseBase):
    """TestQAJ010_01070103"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01070103"]
        super().setUp()
    
    # 障害者総合支援　取込済の一次審査結果を画面参照できることを確認する。
    def test_QAJ010_01070103(self):
        """一次審査結果確認_者_"""
        
        case_data = self.test_data["TestQAJ010_01070103"]
        atena_code = case_data.get("atena_code", "")
        JukyushaNo = case_data.get("JukyushaNo", "")
        txtgyosya = case_data.get("txtgyosya", "")
        txtgyosya2 = case_data.get("txtgyosya2", "")
        txtgyosya3 = case_data.get("txtgyosya3", "")
        seikyu_year_month = case_data.get("seikyu_year_month", "")
        seikyu_year_month2 = case_data.get("seikyu_year_month2", "")
        seikyu_year_month3 = case_data.get("seikyu_year_month3", "")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 障害者総合支援メニュー内の「支払処理」ボタン押下
        self.click_button_by_label("支払処理")
        
        # 4 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_4")
        
        # 5 サブメニュー画面: 「上限額管理結果一括登録」ボタン押下
        self.click_button_by_label("上限額管理結果一括登録")
        
        # 6 事業所・請求年月検索指定画面: 表示
        self.screen_shot("事業所・請求年月検索指定画面_6")
        
        # 7 事業所・請求年月検索指定画面: 事業「障害者総合支援」選択「事業所番号」入力
        self.form_input_by_id(idstr="JigyoCmb", text="障害者総合支援")
        self.form_input_by_id(idstr="TxtServiceJigyoshaCode", value=txtgyosya)
        
        # 8 事業所・請求年月検索指定画面: 「事業所」ボタン押下
        self.click_button_by_label("事業所")
        
        # 9 事業所・請求年月検索指定画面: 表示
        self.screen_shot("事業所・請求年月検索指定画面_9")
        self.click_button_by_label("1")
        
        # 10 事業所・請求年月検索指定画面: 「請求年月」入力
        self.form_input_by_id(idstr="TxtSeikyuYM", value=seikyu_year_month)
        
        # 11 事業所・請求年月検索指定画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        
        # 12 事業所請求年月一覧画面: 表示
        self.screen_shot("事業所請求年月一覧画面_12")
        
        # 13 事業所請求年月一覧画面: No.「1」ボタン押下
        self.click_button_by_label("1")
        
        # 14 上限額管理結果登録画面: 表示
        self.screen_shot("上限額管理結果登録画面_14")
        
        # 15 上限額管理結果登録画面: 「戻る」ボタン押下
        self.return_click()
        
        # 16 事業所請求年月一覧画面: 表示
        self.screen_shot("事業所請求年月一覧画面_16")
        
        # 17 事業所請求年月一覧画面: 「戻る」ボタン押下
        self.return_click()
        
        # 18 事業所・請求年月検索指定画面: 表示
        self.screen_shot("事業所・請求年月検索指定画面_18")
        
        # 19 事業所・請求年月検索指定画面: 「戻る」ボタン押下
        self.return_click()
        
        # 20 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_20")
        
        # 21 サブメニュー画面: 「給付費明細書登録」ボタン押下
        self.click_button_by_label("給付費明細書登録")
        
        # 22 事業所・請求年月検索指定画面: 表示
        self.screen_shot("事業所・請求年月検索指定画面_22")
        
        # 23 事業所・請求年月検索指定画面: 事業「障害者総合支援」選択「事業所番号」入力
        self.form_input_by_id(idstr="JigyoCmb", text="障害者総合支援")
        self.form_input_by_id(idstr="TxtServiceJigyoshaCode", value=txtgyosya)
        
        # 24 事業所・請求年月検索指定画面: 「事業所」ボタン押下
        self.click_button_by_label("事業所")
        
        # 25 事業所・請求年月検索指定画面: 表示
        self.screen_shot("事業所・請求年月検索指定画面_25")
        self.click_button_by_label("1")
        
        # 26 事業所・請求年月検索指定画面: 「請求年月」入力
        self.form_input_by_id(idstr="TxtSeikyuYM", value=seikyu_year_month3)
        
        # 27 事業所・請求年月検索指定画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        
        # 28 事業所請求年月一覧画面: 表示
        self.screen_shot("事業所請求年月一覧画面_28")
        
        # 29 事業所請求年月一覧画面: No.「1」ボタン押下
        self.click_button_by_label("1")
        
        # 30 給付費明細書登録画面: 表示
        self.screen_shot("給付費明細書登録画面_30")
        
        # 31 給付費明細書登録画面: 「戻る」ボタン押下
        self.return_click()
        
        # 32 事業所請求年月一覧画面: 表示
        self.screen_shot("事業所請求年月一覧画面_32")
        
        # 33 事業所請求年月一覧画面: 「戻る」ボタン押下
        self.return_click()
        
        # 34 事業所・請求年月検索指定画面: 表示
        self.screen_shot("事業所・請求年月検索指定画面_34")
        
        # 35 事業所・請求年月検索指定画面: 「戻る」ボタン押下
        self.return_click()
        
        # 36 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_36")
        
        # 37 サブメニュー画面: 「特例給付費明細書登録」ボタン押下
        self.click_button_by_label("特例給付費明細書登録")
        
        # 38 事業所・請求年月検索指定画面: 事業「障害者総合支援」選択「事業所番号」入力
        self.form_input_by_id(idstr="JigyoCmb", text="障害者総合支援")
        self.form_input_by_id(idstr="TxtServiceJigyoshaCode", value=txtgyosya2)
        
        # 39 事業所・請求年月検索指定画面: 「事業所」ボタン押下
        self.click_button_by_label("事業所")
        
        # 40 事業所・請求年月検索指定画面: 表示
        self.screen_shot("事業所・請求年月検索指定画面_40")
        self.click_button_by_label("1")
        
        # 41 事業所・請求年月検索指定画面: 「請求年月」入力
        self.form_input_by_id(idstr="TxtSeikyuYM", value=seikyu_year_month2)
        
        # 42 事業所・請求年月検索指定画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        
        # 43 事業所請求年月一覧画面: 表示
        self.screen_shot("事業所請求年月一覧画面_43")
        
        # 44 事業所請求年月一覧画面: No.「1」ボタン押下
        self.click_button_by_label("1")
        
        # 45 給付費明細書登録画面: 表示
        self.screen_shot("給付費明細書登録画面_45")
        
        # 46 給付費明細書登録画面: 「戻る」ボタン押下
        self.return_click()
        
        # 47 事業所請求年月一覧画面: 表示
        self.screen_shot("事業所請求年月一覧画面_47")
        
        # 48 事業所請求年月一覧画面: 「戻る」ボタン押下
        self.return_click()
        
        # 49 事業所・請求年月検索指定画面: 表示
        self.screen_shot("事業所・請求年月検索指定画面_49")
        
        # 50 事業所・請求年月検索指定画面: 「戻る」ボタン押下
        self.return_click()
        
        # 51 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_51")
        
        # 52 サブメニュー画面: 「相談支援給付費請求書登録」ボタン押下
        self.click_button_by_label("相談支援給付費請求書登録")
        
        # 53 事業所・請求年月検索指定画面: 表示
        self.screen_shot("事業所・請求年月検索指定画面_53")
        
        # 54 事業所・請求年月検索指定画面: 事業「障害者総合支援」選択「事業所番号」入力
        self.form_input_by_id(idstr="JigyoCmb", text="障害者総合支援")
        self.form_input_by_id(idstr="TxtServiceJigyoshaCode", value=txtgyosya3)
        
        # 55 事業所・請求年月検索指定画面: 「事業所」ボタン押下
        self.click_button_by_label("事業所")
        
        # 56 事業所・請求年月検索指定画面: 表示
        self.screen_shot("事業所・請求年月検索指定画面_56")
        self.click_button_by_label("1")
        
        # 57 事業所・請求年月検索指定画面: 「請求年月」入力
        self.form_input_by_id(idstr="TxtSeikyuYM", value=seikyu_year_month)
        
        # 58 事業所・請求年月検索指定画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        
        # 59 事業所請求年月一覧画面: 表示
        self.screen_shot("事業所請求年月一覧画面_59")
        
        # 60 事業所請求年月一覧画面: No.「1」ボタン押下
        self.click_button_by_label("1")
        
        # 61 相談支援給付費請求書登録登録画面: 表示
        self.screen_shot("相談支援給付費請求書登録登録画面_61")
        
        # 62 相談支援給付費請求書登録登録画面: 「戻る」ボタン押下
        self.return_click()
        
        # 63 事業所請求年月一覧画面: 表示
        self.screen_shot("事業所請求年月一覧画面_63")
        
        # 64 事業所請求年月一覧画面: 「戻る」ボタン押下
        self.return_click()
        
        # 65 事業所・請求年月検索指定画面: 表示
        self.screen_shot("事業所・請求年月検索指定画面_65")
        
        # 66 事業所・請求年月検索指定画面: 「戻る」ボタン押下
        self.return_click()
        
        # 67 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_67")
        
        # 68 サブメニュー画面: 「実績記録票」ボタン押下
        self.click_button_by_label("実績記録票")
        
        # 69 実績記録票検索画面: 表示
        self.screen_shot("実績記録票検索画面_69")
        
        # 70 実績記録票検索画面: 事業「障害者総合支援」選択「サービス種類」選択
        self.form_input_by_id(idstr="CmbGyomu", text="障害者総合支援")
        self.form_input_by_id(idstr="CmbService", text="居宅介護")
        
        # 71 実績記録票検索画面: 「確定」ボタン押下
        self.click_button_by_label("確定")
        
        # 72 実績記録票検索画面: 表示
        self.screen_shot("実績記録票検索画面_72")
        
        # 73 実績記録票検索画面: 「受給者証番号」入力「請求年月」入力
        self.form_input_by_id(idstr="TxtJukyushaNo", value=JukyushaNo)
        self.form_input_by_id(idstr="TxtSeikyuYM", value=seikyu_year_month)
        
        # 74 実績記録票検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")
        
        # 75 実績記録票検索画面: 表示
        self.screen_shot("実績記録票検索画面_75")
        
        # 76 実績記録票検索画面: No.「1」ボタン押下
        self.click_button_by_label("1")
        
        # 77 実績記録票登録: 表示
        self.screen_shot("実績記録票登録_77")
        
        # 78 実績記録票登録: 「戻る」ボタン押下
        self.return_click()
        
        # 79 事業所請求年月一覧画面: 表示
        self.screen_shot("事業所請求年月一覧画面_79")
        
        # 80 事業所請求年月一覧画面: 「戻る」ボタン押下
        self.return_click()
        
        # 81 事業所・請求年月検索指定画面: 表示
        self.screen_shot("事業所・請求年月検索指定画面_81")
        
        # 82 事業所・請求年月検索指定画面: 「戻る」ボタン押下
        self.return_click()
        
        # 83 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_83")
        
        # 84 サブメニュー画面: 「戻る」ボタン押下
        self.return_click()
        
        # 85 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_85")
        
