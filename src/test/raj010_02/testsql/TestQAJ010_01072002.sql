DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

UPDATE WR$$JICHITAI_CODE$$FA..T宛名 SET 名称=N'欠字　オーバー字９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９９' WHERE 住民コード=100900000100066
UPDATE WR$$JICHITAI_CODE$$QA..QAJ高額計算結果 SET 処理対象年月='$$SHORI_YM$$' WHERE 自治体コード='$$JICHITAI_CODE$$' AND 削除フラグ='0' AND 業務コード='QAJ010' AND 処理対象年月='' AND 受給者番号 IN ('0000000182','0000002311','0000002600','0000003319','0000003400','0000003426','0000003533','0000003798','0000003806','0000004051','0000004069','0000004077','0000004085','0000004093','0001051721','9910111500','9930106027','9930106423','9930106423','9930107199','9930107751','9930107751','9930108148','9930108148','9930109591','9930109609','9930110243','9930110417')

IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END