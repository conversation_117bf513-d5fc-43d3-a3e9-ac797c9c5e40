DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

DELETE FROM WR$$JICHITAI_CODE$$QA..QAJ支給実績明細 WHERE EXISTS (SELECT 1 FROM WR$$JICHITAI_CODE$$QA..QAJ支給実績基本 AS 基本 WHERE QAJ支給実績明細.業務コード=基本.業務コード AND QAJ支給実績明細.サービス分類=基本.サービス分類 AND QAJ支給実績明細.支給実績番号=基本.支給実績番号 AND QAJ支給実績明細.支給実績履歴番号=基本.支給実績履歴番号 AND 基本.業務コード='QAS403' AND 基本.自治体コード='$$JICHITAI_CODE$$' AND 基本.受付年月=LEFT('$$SEIKYU_YMD$$',6) AND 基本.受給者番号='9930110771')
DELETE FROM WR$$JICHITAI_CODE$$QA..QAJ支給実績集計 WHERE EXISTS (SELECT 1 FROM WR$$JICHITAI_CODE$$QA..QAJ支給実績基本 AS 基本 WHERE QAJ支給実績集計.業務コード=基本.業務コード AND QAJ支給実績集計.サービス分類=基本.サービス分類 AND QAJ支給実績集計.支給実績番号=基本.支給実績番号 AND QAJ支給実績集計.支給実績履歴番号=基本.支給実績履歴番号 AND 基本.業務コード='QAS403' AND 基本.自治体コード='$$JICHITAI_CODE$$' AND 基本.受付年月=LEFT('$$SEIKYU_YMD$$',6) AND 基本.受給者番号='9930110771')
DELETE FROM WR$$JICHITAI_CODE$$QA..QAJ支給実績基本 WHERE 業務コード='QAS403' AND 自治体コード='$$JICHITAI_CODE$$' AND 受付年月=LEFT('$$SEIKYU_YMD$$',6) AND 受給者番号='9930110771'

IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END