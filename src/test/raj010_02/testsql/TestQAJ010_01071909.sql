DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

DELETE FROM WR$$JICHITAI_CODE$$QA..QAJ請求基本 where 業務コード='QAJ010' AND サービス分類='0' AND 請求書番号=999999 AND 請求書履歴番号=999999 AND 自治体コード='$$JICHITAI_CODE$$' AND 受付年月='$$SEIKYU_YM$$' AND 点検結果='1'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAJ請求明細 where 業務コード='QAJ010' AND サービス分類='0' AND 請求書番号=999999 AND 請求書履歴番号=999999 AND 連番=999999 AND 自治体コード='$$JICHITAI_CODE$$' AND サービス種類='44' AND 受付年月='$$SEIKYU_YM$$' AND 点検結果='1'


INSERT INTO WR$$JICHITAI_CODE$$QA..QAJ請求基本 (業務コード,サービス分類,請求書番号,請求書履歴番号,自治体コード,受付年月,点検結果) VALUES ('QAJ010','0',999999,999999,'$$JICHITAI_CODE$$','$$SEIKYU_YM$$','1')
INSERT INTO WR$$JICHITAI_CODE$$QA..QAJ請求明細 (業務コード,サービス分類,請求書番号,請求書履歴番号,連番,自治体コード,サービス種類,受付年月,点検結果) VALUES ('QAJ010','0',999999,999999,999999,'$$JICHITAI_CODE$$','44','$$SEIKYU_YM$$','1')

IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END