DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

UPDATE WR$$JICHITAI_CODE$$QA..QAS資格履歴 SET 削除フラグ = '1' WHERE 業務コード = 'QAJ104' AND 宛名コード = '$$RAJ011_ATENA_CODE$$'
UPDATE WR$$JICHITAI_CODE$$QA..QAS汎用台帳資格内容 SET 削除フラグ = '1' WHERE 業務コード = 'QAJ104' AND 宛名コード = '$$RAJ011_ATENA_CODE$$'
UPDATE WR$$JICHITAI_CODE$$QA..QAZ受給状況 SET 削除フラグ = '1' WHERE 業務コード = 'QAJ104' AND 宛名コード = '$$RAJ011_ATENA_CODE$$'


IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END