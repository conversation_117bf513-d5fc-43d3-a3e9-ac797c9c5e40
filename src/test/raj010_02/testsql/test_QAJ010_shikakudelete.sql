DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

--DELETE WR$$JICHITAI_CODE$$QA..QAJ意見書作成情報      
DELETE WR$$JICHITAI_CODE$$QA..QAJ一次判定結果                    WHERE 宛名コード = '$$ATENA_CODE$$' and 業務コード='QAJ010' 
DELETE WR$$JICHITAI_CODE$$QA..QAJ資格履歴                      where 宛名コード='$$ATENA_CODE$$'  and 業務コード='QAJ010'
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援決定サービス区分管理    where 宛名コード='$$ATENA_CODE$$'  and 業務コード='QAJ010' 
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援決定サービス詳細　　　　where 宛名コード='$$ATENA_CODE$$'  and 業務コード='QAJ010'
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援申請決定管理　　　　　　where 宛名コード='$$ATENA_CODE$$'  and 業務コード='QAJ010'
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援負担額詳細　　　　　　　where 宛名コード='$$ATENA_CODE$$'  and 業務コード='QAJ010'
DELETE WR$$JICHITAI_CODE$$QA..QAJ減免申請            WHERE 本人宛名コード = '$$ATENA_CODE$$' AND 業務コード = 'QAJ010'
DELETE WR$$JICHITAI_CODE$$QA..QAJ収入資産管理        WHERE 本人宛名コード = '$$ATENA_CODE$$' AND 業務コード = 'QAJ010'
DELETE WR$$JICHITAI_CODE$$QA..QAZ受給状況　　　　　　　　　　　　where 宛名コード='$$ATENA_CODE$$'  and 業務コード='QAJ010'　
DELETE WR$$JICHITAI_CODE$$QA..QAZ生活保護                      where 宛名コード='$$ATENA_CODE$$'
DELETE WR$$JICHITAI_CODE$$QA..QAZ疾病情報                      where 宛名コード='$$ATENA_CODE$$'
DELETE WR$$JICHITAI_CODE$$QA..QAZメモ情報                      where 宛名コード='$$ATENA_CODE$$' and 業務コード='QAJ010'　

DELETE WR$$JICHITAI_CODE$$QZ..QZ送付先マスタ                   where 宛名コード='$$ATENA_CODE$$'
DELETE WR$$JICHITAI_CODE$$QZ..QZ連絡先                         where 宛名コード='$$ATENA_CODE$$'
DELETE WR$$JICHITAI_CODE$$QZ..QZ緊急連絡先                         where 宛名コード='$$ATENA_CODE$$'

-- DELETE WR$$JICHITAI_CODE$$QA..QAJ混合世帯情報                    WHERE 混合世帯コード = '100900000100935' and 業務コード='QAJ010'

UPDATE WR$$JICHITAI_CODE$$QZ..QZ医療機関マスタ SET 削除フラグ = '0'
where 医療機関コード='0000000001'

IF NOT EXISTS ( SELECT [自治体コード],[福祉事務所コード],[業務コード],[宛名コード],[年度] FROM [WR$$JICHITAI_CODE$$QA].[dbo].[QAZ福祉税マスタ] WHERE [自治体コード] = '$$JICHITAI_CODE$$' AND [福祉事務所コード] = '00000' AND [業務コード] = 'QAJ010' AND [宛名コード] = '$$ATENA_CODE$$' AND [年度] = '2022')
INSERT INTO [WR$$JICHITAI_CODE$$QA].[dbo].[QAZ福祉税マスタ]([自治体コード],[福祉事務所コード],[業務コード],[宛名コード],[年度],[賦課日],[課税区分],[税世帯コード],[営業収入],[農業収入],[その他事業収入],[不動産収入],[利子収入],[配当収入],[給与収入],[年金収入],[その他雑収入],[山林収入],[退職収入],[土地事業等収入],[一般株式収入],[新規公開株式収入],[収入合計],[収入_予備1],[収入_予備2],[収入_予備3],[収入_予備4],[収入_予備5],[営業所得],[農業所得],[その他事業所得],[不動産所得],[利子所得],[配当所得],[給与所得],[年金所得],[その他雑所得],[総合短期譲渡所得],[総合長期譲渡所得],[一時所得],[肉用牛所得],[山林所得],[退職所得],[分離株式所得],[分離_土地等の事業雑所得],[分離_短期譲渡所得],[分離_短期譲渡所得_軽減],[分離_長期譲渡所得],[分離_長期譲渡所得_特定],[分離_長期譲渡所得_軽減],[分離_長期譲渡所得_居住],[先物所得],[山林所得_繰越後],[退職所得_繰越後],[分離株式所得_繰越後],[分離_土地等の事業雑所得_繰越後],[先物所得_繰越後],[分離_短期譲渡所得_特別控除額],[分離_短期譲渡所得_軽減_特別控除額],[分離_長期譲渡所得_特別控除額],[分離_長期譲渡所得_特定_特別控除額],[分離_長期譲渡所得_軽減_特別控除額],[分離_長期譲渡所得_居住_特別控除額],[分離_短期譲渡所得_特控後],[分離_短期譲渡所得_軽減_特控後],[分離_長期譲渡所得_特控後],[分離_長期譲渡所得_特定_特控後],[分離_長期譲渡所得_軽減_特控後],[分離_長期譲渡所得_居住_特控後],[特別控除前譲渡所得],[特別控除後譲渡所得],[特別控除前譲渡所得_繰越後],[特別控除後譲渡所得_繰越後],[分離所得有無],[繰越純損失所得],[繰越雑損失所得],[繰越損失_総合],[繰越損失_株式],[繰越損失_先物],[繰越損失_その他],[繰越損失_分離居住],[総所得],[総所得金額等],[合計所得],[課税標準額合計],[所得_予備1],[所得_予備2],[所得_予備3],[所得_予備4],[所得_予備5],[所得_予備6],[所得_予備7],[所得_予備8],[所得_予備9],[所得_予備10],[所得税法雑損控除額],[所得税法医療費控除額],[所得税法社会保険料控除額],[所得税法小規模共済等控除額],[所得税法生命保険料控除額],[所得税法個人年金支払控除額],[所得税法配偶者特別控除],[所得税法損害保険料控除],[所得税法寄附金控除],[所得税法配偶者控除],[所得税法基礎控除],[所得税法障害者控除],[所得税法同居特障加算額],[所得税法老年者控除],[所得税法寡婦夫控除],[所得税法勤労学生控除],[所得税法扶養控除],[所得税法所得控除合計],[所得税法配当控除],[所得税法投資_リース控除],[所得税法住宅取得控除],[所得税法災害免除],[所得税法外国税控除],[所得税法政党等寄付金特別控除],[所得税法特別減税],[所得税法税額控除合計],[住民税法雑損控除額],[住民税法医療費控除額],[住民税法社会保険料控除額],[住民税法小規模共済等控除額],[住民税法生命保険料控除額],[住民税法個人年金支払控除額],[住民税法配偶者特別控除],[住民税法損害保険料控除],[住民税法寄附金控除],[住民税法配偶者控除],[住民税法基礎控除],[住民税法障害者控除],[住民税法同居特障加算額],[住民税法老年者控除],[住民税法寡婦夫控除],[住民税法勤労学生控除],[住民税法扶養控除],[住民税法所得控除合計],[市_住民税法配当控除],[市_住民税法外国税控除],[市_住民税法調整税額],[市_住民税法特別減税],[県_住民税法配当控除],[県_住民税法外国税控除],[県_住民税法調整税額],[県_住民税法特別減税],[住民税法税額控除合計],[分離長期譲渡特別控除],[分離短期譲渡特別控除],[譲渡配当割_市],[譲渡配当割_県],[控除_予備1],[控除_予備2],[控除_予備3],[控除_予備4],[控除_予備5],[控除_予備6],[控除_予備7],[控除_予備8],[控除_予備9],[控除_予備10],[控除対象配偶者有無],[控除対象配偶者区分],[夫妻有無区分],[扶養_同居老人],[扶養_老人],[扶養_特定],[扶養_その他],[扶養_同居特別障害],[扶養_特別障害],[扶養_その他障害],[老年者該当],[寡婦夫区分],[勤労学生該当],[その他障害者該当],[特別障害者該当],[未成年該当],[被扶養区分],[区分_予備1],[区分_予備2],[区分_予備3],[区分_予備4],[区分_予備5],[区分_予備6],[区分_予備7],[区分_予備8],[区分_予備9],[区分_予備10],[差引所得割額_市区町村民税],[差引所得割額_都道府県民税],[均等割額_市区町村民税],[均等割額_都道府県民税],[算出年税額],[減免額_所得割額_市民税],[減免額_所得割額_県民税],[減免額_均等割額_市民税],[減免額_均等割額_県民税],[減免額合計],[非課税理由コード],[減免理由コード],[所得税額],[推定所得税額],[入力区分],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('$$JICHITAI_CODE$$','00000','QAJ010','$$ATENA_CODE$$','2022','00000000','0000000001','000000000000000','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0000000000','0000000000','0000000000','0','0','0','0','0','0','0','0000000000','0000000000','0000000000','0000000000','0000000000','0000000000','0000000000','0000000000','0000000000','0000000000','0000000000','0000000000','0000000000','0000000000','0000000000','0000000000','0000000000','0','0','0','0','0','0','0','0','0','0','0000000000','0000000000','0','0','0000000000','0','9501','9501','2024-05-10 10:17:38.737','2024-05-10 10:17:38.737','QAZF011   ');


IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END
