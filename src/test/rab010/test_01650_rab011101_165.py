import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB011101165(FukushiSiteTestCaseBase):
    """TESTRAB011101165"""

    # 補装具申請者に対して、決定登録ができることを確認する。支給券情報（支給番号）が自動配番されていることを確認する。
    def test_case_rab011101_165(self):
        """支給決定情報登録"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        hantei_ymd = case_data.get("hantei_ymd", "")
        koufu_ymd = case_data.get("koufu_ymd", "")
        bikou = case_data.get("bikou", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAB010")
        
        # 1 保健福祉総合システム > 福祉基本情報 > 履歴選択画面 > 「履歴選択一覧」の「資格管理」ボタン押下
        self.click_by_id("CmdButton1_1") 

        # self.do_login()
        # 1 補装具費支給資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 9 補装具費支給資格管理画面: 「福祉世帯情報」ボタン押下
    

        # 2 補装具費支給資格管理画面: 表示
        self.screen_shot("[011101-133]_補装具費支給資格管理画面_2")

        # 3 補装具費支給資格管理画面: 決定日「○○」入力決定結果「決定」入力交付日「○○」入力
        self.form_input_by_id(idstr="TxtKetteiYMD", value=hantei_ymd)
        self.select_by_id("KetteiKekkaCmb", text="決定")
        self.form_input_by_id(idstr="TxtKoufuYMD", value=koufu_ymd)

        #self.form_input_by_id(idstr="TxtToukeisu", value=case_data.get("TxtToukeisu", "")) 

        # 4 補装具費支給資格管理画面: 借受期間開始日「○○」入力借受期間終了日「○○」入力
        # 4 借受期間開始日「○○」入力 
        self.form_input_by_id(idstr="TxtKariukeKikanStartYMD", value=case_data.get("TxtKariukeKikanStartYMD", "")) 
        # 4 借受期間終了日「○○」入力 
        self.form_input_by_id(idstr="TxtKariukeKikanEndYMD", value=case_data.get("TxtKariukeKikanEndYMD", ""))         

        # 5 補装具費支給資格管理画面: 「指導記録」ボタン押下
        self.click_button_by_label("指導記録")

        # 6 補装具費支給資格管理画面: 表示
        self.screen_shot("[011101-133]_補装具費支給資格管理画面_6")

        # 7 補装具費支給資格管理画面: 備考「備考欄入力テスト」入力
        self.form_input_by_id(idstr="TxtBikou", value=bikou)

        self.click_button_by_label("福祉世帯情報")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value=koufu_ymd)
        self.form_input_by_id(idstr="GaitoYMDtxt_2", value=koufu_ymd)
        # 9 福祉世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了") 
        self.alert_ok() 
        self.form_input_by_id(idstr="TxtShinseiNo", value="")
        # 8 補装具費支給資格管理画面: 「支給券データ作成」ボタン押下
        self.click_by_id("KaisoBtn")
        self.click_button_by_label("所得区分コンボ再作成")
        self.click_button_by_label("入力所得区分で再計算")
        self.click_button_by_label("所得区分コンボ再作成")
        self.click_button_by_label("支給券データ作成")

        # 9 補装具費支給資格管理画面: 表示
        self.screen_shot("[011101-133]_補装具費支給資格管理画面_9")

        self.form_input_by_id(idstr="Kofuken_JikofutanJosei1", value="1000")
        # 10 補装具費支給資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.screen_shot("[011101-133]_補装具費支給資格管理画面_10")

        # 11 補装具費支給資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("[011101-133]_補装具費支給資格管理画面_登録した_11")
