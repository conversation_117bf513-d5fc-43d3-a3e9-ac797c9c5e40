import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB01110129(FukushiSiteTestCaseBase):
    """TESTRAB01110129"""

    # 補装具申請者に対して、申請種別「修理」の申請を登録できることを確認する。
    def test_case_rab011101_29(self):
        """申請情報登録"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAB010")

        # 保健福祉総合システム > 福祉基本情報 > 履歴選択画面 > 「履歴選択一覧」の「資格管理」ボタン押下
        self.click_by_id("CmdButton1_1") 
        
        # 1 補装具費支給資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")
        self.screen_shot("[011101-29]_補装具費支給資格管理画面_1")

        # 2 補装具費支給資格管理画面: 申請種別「購入(判定あり)」選択「確定」ボタン押下
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=case_data.get("ShinseiShubetsuCmb", ""))
        self.click_button_by_label("確定")

        # 3 補装具費支給資格管理画面: 表示
        self.screen_shot("[011101-29]_補装具費支給資格管理画面_3")

        # 4 補装具費支給資格管理画面: 申請日入力
        self.form_input_by_id(idstr="TxtShinseiYMD", value=case_data.get("TxtShinseiYMD", ""))
        # 4 補装具費支給資格管理画面: 管理場所入力
        if self.exist_item(item_type="select", item_id="TantoShokatsukuCmb"):
            self.form_input_by_id(idstr="TantoShokatsukuCmb", text=case_data.get("TantoShokatsukuCmb", ""))
        # 4 補装具費支給資格管理画面: 補装具種別入力
        self.form_input_by_id(idstr="YoguShuCmb", text=case_data.get("YoguShuCmb", ""))
        # 4 補装具費支給資格管理画面: 補装具名入力
        self.form_input_by_id(idstr="HosoguCmb", text=case_data.get("HosoguCmb", ""))
        # 4 補装具費支給資格管理画面: 統計数入力
        self.form_input_by_id(idstr="TxtToukeisu", value=case_data.get("TxtToukeisu", ""))
         
        # 4 補装具費支給資格管理画面: 事業者入力
        self.click_button_by_label("事業者名")
        self.form_input_by_id(idstr="kanjiMeisho", value=case_data.get("kanjiMeisho", ""))
        self.click_button_by_label("検索")
        self.click_button_by_label("1")

        # 4 補装具費支給資格管理画面: 福祉世帯情報入力
        self.click_button_by_label("福祉世帯情報")
        self.click_button_by_label("NoBtn1")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value=case_data.get("GaitoYMDtxt_1", ""))
        self.form_input_by_id(idstr="HoninCmb_1", text=case_data.get("HoninCmb_1", ""))
        self.form_input_by_id(idstr="JukyuCmb_1", text=case_data.get("JukyuCmb_1", ""))
        self.click_button_by_label("NoBtn2")
        self.form_input_by_id(idstr="GaitoYMDtxt_2", value=case_data.get("GaitoYMDtxt_2", ""))
        self.form_input_by_id(idstr="HoninCmb_2", text=case_data.get("HoninCmb_2", ""))
        self.form_input_by_id(idstr="JukyuCmb_2", text=case_data.get("JukyuCmb_2", ""))
        self.click_button_by_label("入力完了")
       
        # 4 補装具費支給資格管理画面: 支給内容入力
        self.form_input_by_id(idstr="TxtKyufuTaishoYM", value=case_data.get("TxtKyufuTaishoYM", ""))
        self.form_input_by_id(idstr="TxtMitsumoriKakaku", value=case_data.get("TxtMitsumoriKakaku", ""))
        self.form_input_by_id(idstr="TxtKetteiKakaku", value=case_data.get("TxtKetteiKakaku", ""))
        self.form_input_by_id(idstr="KaisoCmb", text=case_data.get("KaisoCmb", ""))
        # 4 補装具費支給資格管理画面: 「入力所得区分で再計算」ボタン押下
        self.click_button_by_label("入力所得区分で再計算")

        # 5 補装具費支給資格管理画面: 「支給券データ作成」ボタン押下
        self.click_button_by_label("支給券データ作成")

        # 6 補装具費支給資格管理画面: 表示
        self.screen_shot("[011101-29]_補装具費支給資格管理画面_6")

        # 7 補装具費支給資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 8 補装具費支給資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("[011101-29]_補装具費支給資s格管理画面_8")
