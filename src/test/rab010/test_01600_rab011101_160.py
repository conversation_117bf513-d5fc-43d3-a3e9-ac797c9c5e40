import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB011101160(FukushiSiteTestCaseBase):
    """TESTRAB011101160"""

    # 判定が必要な補装具申請者に対して、判定依頼年月日を登録できることを確認する。
    def test_case_rab011101_160(self):
        """判定依頼入力"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAB010")

        # 1 保健福祉総合システム > 福祉基本情報 > 履歴選択画面 > 「履歴選択一覧」の「資格管理」ボタン押下
        self.click_by_id("CmdButton1_1") 

        # 1 補装具費支給資格管理画面: 「判定入力」ボタン押下
        self.click_button_by_label("判定入力")

        # 2 補装具費支給資格管理画面: 表示
        self.screen_shot("[011101-31]_補装具費支給資格管理画面_2")

        # 3 補装具費支給資格管理画面: 判定依頼日「〇〇」入力
        self.form_input_by_id(idstr="TxtShintatsuYMD", value=case_data.get("TxtShintatsuYMD", ""))

        # 4 補装具費支給資格管理画面: 判定予定日「〇〇」入力判定時間「12」:「00」入力判定方法「〇〇」選択判定会場「〇〇」選択
        self.form_input_by_id(idstr="TxtHanteiYoteiYMD", value=case_data.get("TxtHanteiYoteiYMD", ""))
        self.form_input_by_id(idstr="TxtHanteiJikan_Ji", value=case_data.get("TxtHanteiJikan_Ji", ""))
        self.form_input_by_id(idstr="TxtHanteiJikan_Fun", value=case_data.get("TxtHanteiJikan_Fun", ""))
        self.form_input_by_id(idstr="HanteiHouhouCmb", text=case_data.get("HanteiHouhouCmb", ""))
        self.form_input_by_id(idstr="HanteiKaijouCmb", text=case_data.get("HanteiKaijouCmb", ""))

        # 3 補装具費支給資格管理画面: 「福祉世帯情報」ボタン押下
        self.click_button_by_label("福祉世帯情報") #DEL

        # 福祉世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了") 
        self.alert_ok()
        
        # 5 補装具費支給資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 6 補装具費支給資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("[011101-31]_補装具費支給資格管理画面_6")
