import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB01119902(FukushiSiteTestCaseBase):
    """TESTRAB01119902"""
    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    def test_QAB010_01119901(self):
        """住記異動リスト出力処理"""
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 2 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_2")

        # 3 バッチ起動画面: 業務：障害事業：補装具支給　処理区分：月次処理　処理分類：住記異動
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="補装具費支給")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="住記異動")
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 「住記異動リスト出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("住記異動リスト出力処理")
        

        # 5 バッチ起動画面: 申請年月日はじめ「○○」を入力申請年月日おわり「○○」を入力
        params = [
            {"title": "更新年月日（はじめ）", "type": "text", "value": case_data.get("start_ymd", "")},
            {"title": "更新年月日（おわり）", "type": "text", "value": case_data.get("end_ymd", "")}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_5")
        # 6 バッチ起動画面: 「処理開始」ボタン押下
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 7 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 8 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_8")

        # 9 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_9")

        # 10 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 11 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 12 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_12")

        # 13 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 14 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_14")
