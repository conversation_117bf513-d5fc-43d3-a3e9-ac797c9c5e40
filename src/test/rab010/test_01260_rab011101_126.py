import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB011101126(FukushiSiteTestCaseBase):
    """TESTRAB011101126"""

    # 【パターン：完全な新規、障害児、代理受領、①購入判定なし（決定、却下）】補装具未申請者に対して、補装具費（購入・借受け・修理）支給申請書を出力できることを確認する。
    def test_case_rab011101_126(self):
        """補装具費_購入・借受け・修理_支給申請書出力"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        
        self.do_login()
        # 1	メインメニュー画面: 表示
        self.screen_shot("[011101-125]_メインメニュー画面_1")

        # 2	メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")

        # 3	個人検索画面: 表示
        self.screen_shot("[011101-125]_個人検索画面_3")

        # 4	個人検索画面: 「住民コード」入力
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 5	個人検索画面: 「検索」ボタン押下

        # 6	受給状況画面: 表示
        self.screen_shot("[011101-125]_受給状況画面_6")

        # 7	受給状況画面: 「補装具費支給」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAB010")

        # 8	履歴選択画面: 表示
        self.screen_shot("[011101-125]_履歴選択画面_8")

        # 9	履歴選択画面: 「新規資格登録」ボタン押下
        self.click_button_by_label("新規資格登録")

        # 10 履歴選択画面: 「資格履歴」ボタン押下

        # 11 補装具費支給資格管理画面: 表示
        self.screen_shot("[011101-125]_補装具費支給資格管理画面_11")

        # 12 補装具費支給資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")
        self.click_by_id("kirikae")
        # 13 帳票印刷画面: 表示
        self.screen_shot("[011101-125]_帳票印刷画面_13")

        # 14 帳票印刷画面:
        # 「補装具費（購入・借受け・修理）支給申請書」行の印刷チェックボックス選択
        # 「補装具費（購入・借受け・修理）支給申請書」行の発行年月日チェックボックス選択
        # 発行年月日「○○」入力
        # self.print_online_reports(case_name="ケース名", report_name=case_data.get("form_name_0", ""), hakkou_ymd=case_data.get("hakkou_ymd", ""))
        report_param_list = [
            {
                "report_name": case_data.get("form_name_0", ""),
                "params": [
                    {"title": "発行年月日", "type": "text", "value": case_data.get("hakkou_ymd", "")}
                ]
            }
        ]
        self.print_online_reports(case_name="ケース名", report_param_list=report_param_list) 
        self.screen_shot("[011101-125]_帳票印刷画面_14")

        # 15 帳票印刷画面: 「印刷」ボタン押下

        # 16 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下 メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")

        # 17 帳票（PDF）表示
        # self.screen_shot("帳票（PDF）_17")

        # 18 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 19 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 20 補装具費支給資格管理画面: 表示
        self.screen_shot("[011101-125]_補装具費支給資格管理画面_20")
