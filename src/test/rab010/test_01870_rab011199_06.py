import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB01119906(FukushiSiteTestCaseBase):
    """TESTRAB01119906"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_list = case_data.get("sql_params", {})
        self.exec_sqlfile("RAB011199-06.sql",  params=atena_list,db_key="db")
        self.exec_sqlfile("RAB011199-06.sql", params=atena_list,db_key="stddb")
        super().setUp()

    # 医療機関が登録できることを確認する。医療機関情報が検索できることを確認する。
    def test_RAB010_01119906(self):
        """医療機関登録"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        # atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 「マスタメンテナンス」ボタン押下
        # self.master_maintenance_click()
        self.click_button_by_label("マスタメンテナンス")

        # 2 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_2")

        # 3 サブメニュー画面: 「医療機関マスタメンテナンス」ボタン押下
        self.click_button_by_label("医療機関マスタメンテナンス")

        # 4 医療機関検索画面: 表示
        self.screen_shot("医療機関検索画面_4")

        # 5 医療機関検索画面: 所在地区分「指定なし」選択
        self.form_input_by_id(idstr="Shozaichi_1", value="1")

        # 6 医療機関検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 7 医療機関検索画面: 表示
        self.screen_shot("医療機関検索画面_7")

        # 8 医療機関検索画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 9 医療機関検索画面: 表示
        self.screen_shot("医療機関検索画面_9")

        # 10 医療機関入力画面: 医療機関コード（上２桁）「01」を入力医療機関コード（上３桁目）「1」を入力医療機関コード（下７桁）「1234567」を入力医療機関名称「テスト医療機関名称」を入力医療機関名称カナ「ﾃｽﾄｲﾘｮｳｷｶﾝ」を入力略称「テスト医療機関略称」を入力医療機関代表者名「テスト医療機関代表者名」を入力医療機関郵便番号上３桁「123」を入力医療機関郵便番号下４桁「4567」を入力医療機関住所「テスト医療機関住所」を入力医療機関住所　方書「テスト医療機関方書」を入力カナ住所「ﾃｽﾄｲﾘｮｳｷｶﾝｼﾞｭｳｼｮ」を入力カナ方書「ﾃｽﾄｲﾘｮｳｷｶﾝｶﾀｶﾞｷ」を入力医療機関電話番号「1111111111」を入力医療機関ファックス番号「2222222222」を入力所在地区分「指定なし」を選択医療機関都道府県「北海道」を選択医療機関種別「医科」を選択医療機関診療科目チェックボックス「内科」をチェック
        # NG no ID 医療機関住所、カナ住所が入力不可能, 医療機関郵便番号上３桁, 医療機関郵便番号下４桁の
        self.form_input_by_id(idstr="TxtIryoKCode1", value="01")
        self.form_input_by_id(idstr="TxtIryoKCode2", value="1")
        self.form_input_by_id(idstr="TxtIryoKCode3", value="1234567")
        self.form_input_by_id(idstr="TxtKanjiName", value="テスト医療機関名称")
        self.form_input_by_id(idstr="TxtKanaName", value="ﾃｽﾄｲﾘｮｳｷｶﾝ")
        self.form_input_by_id(idstr="TxtRyakusho", value="テスト医療機関略称")
        self.form_input_by_id(idstr="TxtDaihyoName", value="テスト医療機関代表者名")
        self.get_post_code()
        self.form_input_by_id(idstr="TxtKanjiKatagaki", value="テスト医療機関方書")
        self.form_input_by_id(idstr="TxtKanaKatagaki", value="ﾃｽﾄｲﾘｮｳｷｶﾝｶﾀｶﾞｷ")
        self.form_input_by_id(idstr="TxtTelBan", value="1111111111")
        self.form_input_by_id(idstr="TxtFaxBan", value="2222222222")
        self.form_input_by_id(idstr="Shozaichi_1", value="1")
        self.form_input_by_id(idstr="CmbTodoufuken", text="北海道")
        self.form_input_by_id(idstr="CmbTensuHyo", text="医科")
        self.form_input_by_id(idstr="ChkShinRyou_1", value="1")

        # 11 医療機関入力画面: 使用業務選択「補装具費支給」をチェック適用開始日「20240101」を入力名称「テスト名称」を入力住所「テスト住所」を入力方書「テスト方書」を入力備考「テスト備考」を入力指定日「20240101」を入力
        self.form_input_by_id(idstr="ChkGyomu_QAB010", value="1")
        self.form_input_by_id(idstr="TxtYukokikanKaishi_QAB010", value="20240101")
        self.form_input_by_id(idstr="TxtMeisho_QAB010", value="テスト名称")
        self.form_input_by_id(idstr="TxtJusho_QAB010", value="テスト住所")
        self.form_input_by_id(idstr="TxtKatagaki_QAB010", value="テスト方書")
        self.form_input_by_id(idstr="TxbGyomu_QAB010", value="テスト備考")
        self.form_input_by_id(idstr="TxtShiteiYMD_QAB010", value="20240101")
        self.form_input_by_id(idstr="TxtHaishiYMD_QAB010", value="")
        self.form_input_by_id(idstr="TxtHaishiriyu_QAB010", value="")

        # 12 医療機関入力画面: 「登録」ボタン押下
        self.click_button_by_label("登録／復活")
        self.alert_ok()

        # 13 医療機関入力画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("医療機関入力画面_13")

        # 14 医療機関入力画面: 「戻る」ボタン押下
        # self.return_click()
        self.find_element(By.ID,"GOBACK").click()
        time.sleep(3)
        # self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())

        
        # 15 医療機関検索画面: 表示
        self.screen_shot("医療機関検索画面_15")

        # 16 医療機関検索画面: 「初期表示」ボタン押下
        self.click_button_by_label("初期表示")

        # 17 医療機関検索画面: 所在地区分「指定なし」選択漢字名称「テスト医療機関名称」入力
        self.form_input_by_id(idstr="Shozaichi_1", value="1")
        self.form_input_by_id(idstr="TxtNameKanji", value="テスト医療機関名称")

        # 18 医療機関検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 19 医療機関検索画面: 表示
        self.screen_shot("医療機関検索画面_19")

        # 20 医療機関検索画面: No1ボタン押下
        self.click_button_by_label("1")

        # 21 医療機関検索画面: 「削除」ボタン押下
        # Assert: メッセージエリアに「削除しました 」と表示されていることを確認する。
        self.click_button_by_label("削除")
        self.alert_ok()
        self.assert_message_area("削除しました")

        # 22 医療機関検索画面: 「戻る」ボタン押下
        self.return_click()

        # 23 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_23")

        # 24 サブメニュー画面: 「戻る」ボタン押下
        self.return_click()

        # 25 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_25")
