import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181306(FukushiSiteTestCaseBase):
    """TestQAC050_181306"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181306"]
        super().setUp()

    # 不足書類があるため保留となっている対象者について、保留通知書等を出力できることを確認する。
    def test_QAC050_181306(self):
        """保留通知書作成"""

        case_data = self.test_data["TestQAC050_181306"]
        atena_code = case_data.get("atena_code", "")
        pTxtHakko = case_data.get("pTxtHakko", "")
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        pKijyunYMD = case_data.get("pKijyunYMD", "")
        pOutputOrder = case_data.get("pOutputOrder", "")
        pTeishutsuKigen = case_data.get("pTeishutsuKigen", "")
        pTokusokuYMD = case_data.get("pTokusokuYMD", "")
        pKetteiYMD = case_data.get("pKetteiYMD", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        self.click_button_by_label("修正")
        # 1: "児童扶養手当 資格管理画面": 「現況情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="現況情報")

        # 2: 現況履歴画面: 表示
        self.screen_shot("現況履歴画面_2")

        # 3: 現況履歴画面: 現況履歴一覧「1」Noボタン押下
        self.click_button_by_label("1")

        # 4: 現況履歴画面: 発行年月日「20230702」
        self.form_input_by_id(idstr="TxtHakko", value=pTxtHakko)
        self.screen_shot("現況履歴画面_4")

        # 5: 現況履歴画面: 「印刷」ボタン押下
        self.pdf_output_and_download_no_alert(button_id="BtnInsatsu",case_name="現況届")
        self.assert_message_area("プレビューを表示しました")

        # 6: 現況履歴画面: 児童扶養手当現況届「ファイルを開く(O)」ボタンを押下

        # 7: 児童扶養手当現況届（PDF）: 表示
        self.screen_shot("児童扶養手当現況届（PDF）_7")

        # 8: 児童扶養手当現況届（PDF）: 2ページ目表示 中面が出力されていることを確認する。
        # self.screen_shot("児童扶養手当現況届（PDF）_8")

        # 9: 児童扶養手当現況届（PDF）: 3ページ目表示 裏面が出力されていることを確認する
        # self.screen_shot("児童扶養手当現況届（PDF）_9")

        # 10 児童扶養手当現況届（PDF）: ×ボタン押下でPDFを閉じる

        # 11 現況履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 12 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当資格管理画面_12")

        # 13 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_13")

        # 14 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")

        # 15 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_15")

        # 16 バッチ起動画面: "業務：児童 事業：児童扶養手当 処理区分：月次 処理分類：不足書類督促"
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect)
        self.screen_shot("バッチ起動画面_16")

        # 17 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_17")

        # 18 バッチ起動画面: 「不足書類督促抽出処理」のNoボタン押下
        self.click_batch_job_button_by_label("不足書類督促抽出処理")

        # 19 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_19")

        # 20 バッチ起動画面: "基準日「20230701」 出力順「カナ氏名順」"
        params = [
            {"title": "基準日", "type": "text", "value": pKijyunYMD},
            {"title": "出力順", "type": "select", "value": pOutputOrder}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_20")

        # 21 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 22 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_22")

        # 23 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 24 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_24")

        # 25 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 26 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_26")

        # 27 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_27")

        # 28 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 29 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_29")

        # 30 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 31 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_31")

        # 32 ジョブ帳票履歴画面: 「督促用決済名簿」のNoボタン押下

        # 33 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 34 督促用決済名簿（PDF）: 表示
        # self.screen_shot("督促用決済名簿（PDF）_34")

        # 35 督促用決済名簿（PDF）: ×ボタン押下でPDFを閉じる

        # 36 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_36")

        # 37 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 38 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_38")

        # 39 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 40 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_40")

        # 41 バッチ起動画面: 「不足書類督促状出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("不足書類督促状出力処理")

        # 42 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_42")

        # 43 バッチ起動画面: 提出期限「20230930」
        params = [
            {"title": "提出期限", "type": "text", "value": pTeishutsuKigen}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_43")

        # 44 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 45 バッチ起動画面: 表示 メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_45")

        # 46 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 47 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_47")

        # 48 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 49 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_49")

        # 50 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # self.screen_shot("ジョブ実行履歴画面_27")

        # 51 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 52 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_52")

        # 53 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 54 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_54")

        # 55 ジョブ帳票履歴画面: 「保留通知書」のNoボタン押下

        # 56 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 57 保留通知書（PDF）: 表示
        # self.screen_shot("保留通知書（PDF）_57")

        # 58 保留通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 59 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_59")

        # 60 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 61 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_61")

        # 62 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 63 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_63")

        # 64 バッチ起動画面: 「不足書類テーブル更新処理」のNoボタン押下
        self.click_batch_job_button_by_label("不足書類テーブル更新処理")

        # 65 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_65")

        # 66 バッチ起動画面: "督促年月日「20230701」 決定年月日「20230701」"
        params = [
            {"title": "督促年月日", "type": "text", "value": pTokusokuYMD},
            {"title": "決定年月日", "type": "text", "value": pKetteiYMD}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_66")

        # 67 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 68 バッチ起動画面: 表示	メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_68")

        # 69 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 70 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_70")

        # 71 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 72 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_72")

        # 73 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_73")
