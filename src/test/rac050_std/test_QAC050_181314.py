import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181314(FukushiSiteTestCaseBase):
    """TestQAC050_181314"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181314"]
        super().setUp()

    # 支給停止解除通知書を出力できることを確認する。
    def test_QAC050_181314(self):
        """支給停止解除通知書等作成"""

        case_data = self.test_data["TestQAC050_181314"]
        atena_code = case_data.get("atena_code", "")
        pTxtShoushoKoufuYMD = case_data.get("pTxtShoushoKoufuYMD", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 "児童扶養手当資格管理画面": 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 "児童扶養手当 資格管理画面": 証書交付年月日「20230902」
        self.form_input_by_id(idstr="TxtShoushoKoufuYMD", value=pTxtShoushoKoufuYMD)
        self.screen_shot("児童扶養手当資格管理画面_2")

        # 3 "児童扶養手当 資格管理画面": 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 4 "児童扶養手当 資格管理画面": 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 5 "児童扶養手当 資格管理画面": 表示	メッセージエリアに「登録しました。 」と表示されていることを確認する。
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_23")
