import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181901(FukushiSiteTestCaseBase):
    """TestQAC050_181901"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181901"]
        sql_params = {
            "TARGET_GYOUMU_CODE": "QAC050", 
            "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
            "TARGET_NENDO":"2021",
            "TARGET_SOUSYOTOKU":"0"
        }
        self.exec_sqlfile("QAC-ALL-CLEAR.sql", params=sql_params)
        super().setUp()

    # 市外に転出した住民に対し、自動で差止情報を登録できることを確認する。
    def test_QAC050_181901(self):
        """住記異動者等対象者抽出"""

        case_data = self.test_data["TestQAC050_181901"]
        atena_code = case_data.get("atena_code", "")
        start_date = case_data.get("start_date", "")
        end_date = case_data.get("end_date", "")
        kettei_date = case_data.get("kettei_date", "")

        # 資格データ準備
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        self.click_button_by_label("申請内容入力")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
        self.click_button_by_label("確定")
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20220401")
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20220501")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("2")
        self.form_input_by_id(idstr="CmbZokugara", text="子")
        self.form_input_by_id(idstr="RdoDokyo1", value="1")
        self.form_input_by_id(idstr="TxtKangoYMD", value="20220325")
        self.form_input_by_id(idstr="KojiGaitou2", value="1")
        self.form_input_by_id(idstr="TxtGaitoYMD", value="20220501")
        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        self.form_input_by_id(idstr="TxtJiyuYMD", value="20220325")
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20220501")
        self.form_input_by_id(idstr="RdoShogai2", value="1")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("福祉世帯情報")
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20220501")
        self.form_input_by_id(idstr="ChkFlg_4", value="1")
        self.form_input_by_id(idstr="HoninCmb_2", text="子")
        self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20220501")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.form_input_by_id(idstr="TxtKaitei", value="202205")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20220401")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20220401")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
        self.click_button_by_label("決定内容入力") 
        self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20220401")
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20220401")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.form_input_by_id(idstr="TxtShoushoBango", value="180701")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")

        # 住民でなくなった日、住民でなくなった事由を変更
        sql_params = {
            "ATENA_CODE": case_data.get("atena_code", "")
        }
        self.exec_sqlfile("Test_QAC050_181901.sql", params=sql_params)

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: "業務：児童 事業：児童扶養手当 処理区分：月次 処理分類：住記異動処理"
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="住記異動処理")

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「住記異動者自動差止処理」のNoボタン押下
        self.click_batch_job_button_by_label("住記異動者自動差止処理")

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: "対象開始年月日「20230601」 対象終了年月日「20230630」 差止決定年月日「20230702」"
        params = [
            {"title": "対象開始年月日", "type": "text", "value": start_date},
            {"title": "対象終了年月日", "type": "text", "value": end_date},
            {"title": "差止決定年月日", "type": "text", "value": kettei_date}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示 メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 19 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「住記異動者リスト」のNoボタン押下

        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 22 住記異動者リスト（PDF）: 表示
        # self.screen_shot("住記異動者リスト（PDF）_22")

        # 23 住記異動者リスト（PDF）: ×ボタン押下でPDFを閉じる

        # 24 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_24")
