import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180304(FukushiSiteTestCaseBase):
    """TestQAC050_180304"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180304"]
        super().setUp()

    # 不足書類の提出日の登録ができることを確認する。
    def test_QAC050_180304(self):
        """受付情報入力"""

        case_data = self.test_data["TestQAC050_180304"]
        atena_codes = [case_data.get("atena_code", ""), case_data.get("atena_code2", "")]

        for i,atena_code in enumerate(atena_codes):
            if i == 0: atena_no = "①"
            if i == 1: atena_no = "②"

            self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
            # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
            self.click_button_by_label("修正")
            self.open_common_buttons_area()

            # 2 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
            self.click_button_by_label("提出書類管理")

            # 3 提出書類管理: 表示
            self.screen_shot(f"提出書類管理_3_{atena_no}")

            # 4 提出書類管理: 「修正」ボタン押下
            self.click_button_by_label("修正")

            # 5 提出書類管理: 戸籍謄抄本の提出日「20230602」所得証明書の提出日「20230602」
            self.form_input_by_id(idstr="ChkTeisyutsu_1", value="1")
            self.form_input_by_id(idstr="TxtSyoruiYMD_1", value="20230602")
            self.form_input_by_id(idstr="TxtSyoruiYMD_3", value="20230602")
            self.screen_shot(f"提出書類管理_5_{atena_no}")

            # 6 提出書類管理: 「入力完了」ボタン押下
            self.click_button_by_label("入力完了")
            self.click_button_by_label("確定")

            # 7 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_7_{atena_no}")

            # 8 児童扶養手当資格管理画面: 児童のNoボタン押下
            self.click_button_by_label("1")

            # 9 支給対象児童入力画面: 表示
            self.screen_shot(f"支給対象児童入力画面_9_{atena_no}")

            # 10 支給対象児童入力画面: 「戻る」ボタン押下
            self.return_click()

            # 11 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_11_{atena_no}")

            # 12 児童扶養手当資格管理画面: 「月額計算」ボタン押下
            self.click_button_by_label("月額計算")

            # 13 児童扶養手当資格管理画面: 「登録」ボタン押下
            self.click_button_by_label("登録")
            self.alert_ok()

            # 14 児童扶養手当資格管理画面: 表示
            # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
            self.assert_message_area("登録しました。")
            self.screen_shot(f"児童扶養手当資格管理画面_14_{atena_no}")
