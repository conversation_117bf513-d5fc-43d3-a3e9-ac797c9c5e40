import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181410(FukushiSiteTestCaseBase):
    """TestQAC050_181410"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181410"]
        super().setUp()

    # 支給停止通知書等を出力できることを確認する。
    def test_QAC050_181410(self):
        """支給停止通知書等作成"""

        case_data = self.test_data["TestQAC050_181410"]
        atena_code = case_data.get("atena_code", "")
        form_name_0 = case_data.get("form_name_0", "")
        hakkouYMD = case_data.get("hakkouYMD", "")
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        pStartYMD = case_data.get("pStartYMD", "")
        pEndYMD = case_data.get("pEndYMD", "")
        pAtenaCode = case_data.get("pAtenaCode", "")
        pOutputOrder = case_data.get("pOutputOrder", "")
        noti_category_6 = case_data.get("noti_category_6", "")
        pHakkoYMD = case_data.get("pHakkoYMD", "")
        pStartYMD_2 = case_data.get("pStartYMD_2", "")
        pEndYMD_2 = case_data.get("pEndYMD_2", "")
        pAtenaCode_2 = case_data.get("pAtenaCode_2", "")
        pOutputOrder_2 = case_data.get("pOutputOrder_2", "")
        noti_category_6_2 = case_data.get("noti_category_6_2", "")
        pHakkoYMD_2 = case_data.get("pHakkoYMD_2", "")
        pStartYMD_3 = case_data.get("pStartYMD_3", "")
        pEndYMD_3 = case_data.get("pEndYMD_3", "")
        pAtenaCode_3 = case_data.get("pAtenaCode_3", "")
        pOutputOrder_3 = case_data.get("pOutputOrder_3", "")
        noti_category_1 = case_data.get("noti_category_1", "")
        pHakkoYMD_3 = case_data.get("pHakkoYMD_3", "")
        pStartYMD_4 = case_data.get("pStartYMD_4", "")
        pEndYMD_4 = case_data.get("pEndYMD_4", "")
        pAtenaCode_4 = case_data.get("pAtenaCode_4", "")
        pOutputOrder_4 = case_data.get("pOutputOrder_4", "")
        noti_category_1_2 = case_data.get("noti_category_1_2", "")
        pHakkoYMD_4 = case_data.get("pHakkoYMD_4", "")
        
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_2")

        # 3 帳票印刷画面:
        # 「支給停止通知書」行の印刷チェックボックス選択
        # 「支給停止通知書」行の発行年月日チェックボックス選択
        # 「支給停止通知書」行の発行年月日「20230902」
        exec_params = [
            {
                "report_name": form_name_0,
                "params":[
                    {"title": "発行年月日", "value":hakkouYMD,"is_no_print":"1"}
                ]
            }
        ]
        self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_3")

        # 4 帳票印刷画面: 「印刷」ボタン押下
        # self.click_button_by_label("印刷")

        # 5 帳票印刷画面: 支給停止通知書「ファイルを開く(O)」ボタンを押下

        # 6 支給停止通知書（PDF）: 表示
        # self.screen_shot("支給停止通知書（PDF）_6")

        # 7 支給停止通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("支給停止通知書（PDF）_7")

        # 8 支給停止通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 9 帳票印刷画面: 児童扶養手当証書「ファイルを開く(O)」ボタンを押下

        # 10 児童扶養手当証書（PDF）: 表示
        # self.screen_shot("児童扶養手当証書（PDF）_10")

        # 11 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("児童扶養手当証書（PDF）_11")

        # 12 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # 13 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 14 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_14")

        # 15 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_15")

        # 16 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 17 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_17")

        # 18 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：通知書一括処理
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect)

        # 19 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_19")

#③出荷 -s
#        # 20 バッチ起動画面: 「児童扶養手当証書受領書出力」のNoボタン押下
#        self.click_batch_job_button_by_label("児童扶養手当証書受領書出力")
#
#        # 21 バッチ起動画面: 表示
#        self.screen_shot("バッチ起動画面_21")
#
#        # 22 バッチ起動画面: 開始決定日「20230902」終了決定日「20230902」宛名コード「」出力順「証書番号順」選択通知書区分「6」発行年月日「20230902」
#        params = [
#            {"title": "開始決定日", "type": "text", "value": pStartYMD},
#            {"title": "終了決定日", "type": "text", "value": pEndYMD},
#            {"title": "宛名コード", "type": "text", "value": pAtenaCode},
#            {"title": "出力順", "type": "select", "value": pOutputOrder},
#            {"title": "通知書区分", "type": "text", "value": noti_category_6},
#            {"title": "発行年月日", "type": "text", "value": pHakkoYMD}
#        ]
#        self.set_job_params(params)
#        self.screen_shot("バッチ起動画面_22")
#
#        # 23 バッチ起動画面: 「処理開始」ボタン押下
#        exec_datetime = self.exec_batch_job()
#
#        # 24 バッチ起動画面: 表示
#        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
#        self.assert_message_base_header("ジョブを起動しました")
#        self.screen_shot("バッチ起動画面_24")
#
#        # 25 バッチ起動画面: 「実行履歴」ボタン押下
#        self.click_job_exec_log()
#
#        # 26 ジョブ実行履歴画面: 表示
#        self.screen_shot("ジョブ実行履歴画面_26")
#
#        # 27 ジョブ実行履歴画面: 「検索」ボタン押下
#        self.wait_job_finished(120,20)
#        self.assert_job_normal_end(exec_datetime=exec_datetime)
#
#        # 28 ジョブ実行履歴画面: 表示
#        # self.screen_shot("ジョブ実行履歴画面_28")
#
#        # 29 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
#        self.screen_shot("ジョブ実行履歴画面_29")
#
#        # 30 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
#        self.click_report_log()
#
#        # 31 ジョブ帳票履歴画面: 表示
#        self.screen_shot("ジョブ帳票履歴画面_31")
#
#        # 32 ジョブ帳票履歴画面: 「検索」ボタン押下
#        self.get_job_report_pdf(exec_datetime=exec_datetime)
#
#        # 33 ジョブ帳票履歴画面: 表示
#        # self.screen_shot("ジョブ帳票履歴画面_33")
#
#        # 34 ジョブ帳票履歴画面: 「児童扶養手当証書受領書」のNoボタン押下
#
#        # 35 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
#
#        # 36 児童扶養手当証書受領書（PDF）: 表示
#        # self.screen_shot("児童扶養手当証書受領書（PDF）_36")
#
#        # 37 児童扶養手当証書受領書（PDF）: ×ボタン押下でPDFを閉じる
#        # self.screen_shot("児童扶養手当証書受領書（PDF）_37")
#
#        # 38 ジョブ帳票履歴画面: 表示
#        self.screen_shot("ジョブ帳票履歴画面_38")
#
#        # 39 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
#        self.click_job_list()
#
#        # 40 バッチ起動画面: 表示
#        self.screen_shot("バッチ起動画面_40")
#
#        # 41 バッチ起動画面: 「処理一覧」ボタン押下
#        self.click_job_exec_log_search()
#
#        # 42 バッチ起動画面: 表示
#        self.screen_shot("バッチ起動画面_42")
#③出荷 -e

        # 43 バッチ起動画面: 「児童扶養手当証書一括出力」のNoボタン押下
        self.click_batch_job_button_by_label("児童扶養手当証書一括出力")

        # 44 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_44")

        # 45 バッチ起動画面: 開始決定日「20230902」終了決定日「20230902」宛名コード「」出力順「証書番号順」選択通知書区分「6」発行年月日「20230902」
        params = [
            {"title": "開始決定日", "type": "text", "value": pStartYMD_2},
            {"title": "終了決定日", "type": "text", "value": pEndYMD_2},
            {"title": "宛名コード", "type": "text", "value": pAtenaCode_2},
            {"title": "出力順", "type": "select", "value": pOutputOrder_2},
            {"title": "通知書区分", "type": "text", "value": noti_category_6_2},
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD_2}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_45")

        # 46 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 47 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_47")

        # 48 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 49 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_49")

        # 50 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 51 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_51")

        # 52 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_52")

        # 53 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 54 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_54")

        # 55 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 56 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_56")

        # 57 ジョブ帳票履歴画面: 「児童扶養手当証書」のNoボタン押下

        # 58 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 59 児童扶養手当証書（PDF）: 表示
        # self.screen_shot("児童扶養手当証書（PDF）_59")

        # 60 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("児童扶養手当証書（PDF）_60")

        # 61 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # 62 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_62")

        # 63 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 64 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_64")

        # 65 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 66 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_66")

        # 67 バッチ起動画面: 「決定一括出力処理_減額適用関係一覧」のNoボタン押下
        self.click_batch_job_button_by_label("決定一括出力処理_減額適用関係一覧")

        # 68 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_68")

        # 69 バッチ起動画面: 開始決定日「20230902」終了決定日「20230902」宛名コード「」出力順「証書番号順」選択通知書区分「1」発行年月日「20230902」
        params = [
            {"title": "開始決定日", "type": "text", "value": pStartYMD_3},
            {"title": "終了決定日", "type": "text", "value": pEndYMD_3},
            {"title": "宛名コード", "type": "text", "value": pAtenaCode_3},
            {"title": "出力順", "type": "select", "value": pOutputOrder_3},
            {"title": "通知書区分", "type": "text", "value": noti_category_1},
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD_3}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_69")

        # 70 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 71 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_71")

        # 72 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 73 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_73")

        # 74 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 75 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_75")

        # 76 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_76")

        # 77 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 78 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_78")

        # 79 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 80 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_80")

        # 81 ジョブ帳票履歴画面: 「通知書対象者一覧」のNoボタン押下

        # 82 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 83 通知書対象者一覧（PDF）: 表示
        # self.screen_shot("通知書対象者一覧（PDF）_83")

        # 84 通知書対象者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 85 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_85")

        # 86 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 87 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_87")

        # 88 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 89 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_89")

        # 90 バッチ起動画面: 「決定一括出力処理_減額適用関係通知書」のNoボタン押下
        self.click_batch_job_button_by_label("決定一括出力処理_減額適用関係_通知書")

        # 91 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_91")

        # 92 バッチ起動画面: 開始決定日「20230902」終了決定日「20230902」宛名コード「」出力順「証書番号順」選択通知書区分「1」発行年月日「20230902」
        params = [
            {"title": "開始決定日", "type": "text", "value": pStartYMD_4},
            {"title": "終了決定日", "type": "text", "value": pEndYMD_4},
            {"title": "宛名コード", "type": "text", "value": pAtenaCode_4},
            {"title": "出力順", "type": "select", "value": pOutputOrder_4},
            {"title": "通知書区分", "type": "text", "value": noti_category_1_2},
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD_4}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_92")

        # 93 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 94 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_94")

        # 95 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 96 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_96")

        # 97 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 98 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_98")

        # 99 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_99")

        # 100 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 101 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_101")

        # 102 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="支給停止通知書102")

        # 103 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_103")

        # 104 ジョブ帳票履歴画面: 「支給停止通知書」のNoボタン押下

        # 105 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 106 支給停止通知書（PDF）: 表示
        # self.screen_shot("支給停止通知書（PDF）_106")

        # 107 支給停止通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("支給停止通知書（PDF）_107")

        # 108 支給停止通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 109 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_109")
