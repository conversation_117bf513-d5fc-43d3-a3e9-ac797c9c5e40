import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181104(FukushiSiteTestCaseBase):
    """TestQAC050_181104"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181104"]
        super().setUp()

    # 差止通知書の差止情報を確認できることを確認する。
    def test_QAC050_181104(self):
        """支払差止通知書作成"""

        case_data = self.test_data["TestQAC050_181104"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.do_login()
        # 1 児童扶養手当資格管理画面: 「差止情報」ボタン押下
        self.click_button_by_label("差止情報")

        # 2 差止情報画面: 表示
        self.screen_shot("差止情報画面_2")

        # 3 差止情報画面: 差止履歴「1」Noボタン押下
        self.click_button_by_label("1")  # TODO instead of call self.click_batch_job_button_by_label("1")

        # 4 差止情報画面: 「戻る」ボタン押下
        self.return_click()

        # 5 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_5")
