import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181701(FukushiSiteTestCaseBase):
    """TestQAC050_181701"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181701"]
        super().setUp()

    # 61表および各種報告書を出力できることを確認する。
    def test_QAC050_181701(self):
        """各種報告書作成"""

        case_data = self.test_data["TestQAC050_181701"]
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        pHokokuYM = case_data.get("pHokokuYM", "")
        pHokokuYM_2 = case_data.get("pHokokuYM_2", "")
        pTani = case_data.get("pTani", "")
        pTaishoY = case_data.get("pTaishoY", "")
        pStartYM = case_data.get("pStartYM", "")
        pEndYM = case_data.get("pEndYM", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童
        # 事業：児童扶養手当
        # 処理区分：月次
        # 処理分類：統計処理
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect)

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「六十一表出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("六十一表出力処理")

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: 報告年月「202304」
        params = [
            {"title": "報告年月", "type": "text", "value": pHokokuYM}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 12 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 19 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「61表」のNoボタン押下

        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 22 61表（PDF）: 表示
        # self.screen_shot("61表（PDF）_22")

        # 23 61表（PDF）: ×ボタン押下でPDFを閉じる

        # 24 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_24")

        # 25 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 26 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_26")

        # 27 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 28 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_28")

        # 29 バッチ起動画面: 「現受給者集計票及び手当額分布表出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("現受給者集計票及び手当額分布表出力処理")

        # 30 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_30")

        # 31 バッチ起動画面: 報告年月「202304」
        # 分布単位「10」
        params = [
            {"title": "報告年月", "type": "text", "value": pHokokuYM_2},
            {"title": "分布単位", "type": "text", "value": pTani}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_31")

        # 32 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 33 バッチ起動画面: 表示	メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_33")

        # 34 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 35 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_35")

        # 36 ジョブ実行履歴画面: 「検索」ボタン押下

        # 37 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_37")

        # 38 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_38")

        # 39 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 40 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_40")

        # 41 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 42 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_42")

        # 43 ジョブ帳票履歴画面: 「現受給者集計表」のNoボタン押下

        # 44 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 45 現受給者集計表（PDF）: 表示
        # self.screen_shot("現受給者集計表（PDF）_45")

        # 46 現受給者集計表（PDF）: ×ボタン押下でPDFを閉じる

        # 47 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_47")

        # 48 ジョブ帳票履歴画面: 「月額分布表」のNoボタン押下

        # 49 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 50 月額分布表（PDF）:	表示
        # self.screen_shot("月額分布表（PDF）_50")

        # 51 月額分布表（PDF）:	×ボタン押下でPDFを閉じる

        # 52 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_52")

        # 53 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 54 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_54")

        # 55 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 56 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_56")

        # 57 バッチ起動画面: 「執行状況調べ」のNoボタン押下
        self.click_batch_job_button_by_label("執行状況調べ")

        # 58 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_58")

        # 59 バッチ起動画面: 対象年度「令和4年」選択
        # 開始振込年月「202204」
        # 終了振込年月「202303」
        params = [
            {"title": "対象年度", "type": "select", "value": pTaishoY},
            {"title": "開始振込年月", "type": "text", "value": pStartYM},
            {"title": "終了振込年月", "type": "text", "value": pEndYM}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_59")

        # 60 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 61 バッチ起動画面: 表示 メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_61")

        # 62 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 63 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_63")

        # 64 ジョブ実行履歴画面: 「検索」ボタン押下

        # 65 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_65")

        # 66 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_66")

        # 67 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 68 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_68")

        # 69 ジョブ帳票履歴画面: 「検索」ボタン押下

        # 70 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_70")

        # 71 ジョブ帳票履歴画面: 「執行状況調べ」のNoボタン押下

        # 72 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 今回の処理で作成したPDFのDL（戻値はDLしたファイル数）
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 73 執行状況調べ（PDF）: 表示

        # 74 執行状況調べ（PDF）: ×ボタン押下でPDFを閉じる

        # 75 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_75")
