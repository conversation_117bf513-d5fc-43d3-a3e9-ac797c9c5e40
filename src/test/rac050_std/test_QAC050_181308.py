import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181308(FukushiSiteTestCaseBase):
    """TestQAC050_181308"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181308"]
        super().setUp()

    # 所得判定
    def test_QAC050_181308(self):
        """所得判定処理"""

        case_data = self.test_data["TestQAC050_181308"]
        atena_code = case_data.get("atena_code", "")
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        pGenkyoY = case_data.get("pGenkyoY", "")
        pOutputOrder = case_data.get("pOutputOrder", "")
        pHakkoYMD = case_data.get("pHakkoYMD", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 "児童扶養手当 資格管理画面": 「所得判定詳細情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="所得判定詳細情報")

        # 2 所得判定詳細情報画面: 表示
        self.screen_shot("所得判定詳細情報画面_2")

        # 3 所得判定詳細情報画面: 「次年度＞」ボタン押下
        self.click_button_by_label("次年度＞")

        # 4 所得判定詳細情報画面: 表示
        self.screen_shot("所得判定詳細情報画面_4")

        # 5 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 6 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当資格管理画面_6")

        # 7 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_7")

        # 8 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")

        # 9 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_9")

        # 10 バッチ起動画面: "業務：児童 事業：児童扶養手当 処理区分：年次処理 処理分類：現況更新処理"
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect)
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_11")

        # 12 バッチ起動画面: 「現況届一括更新処理_抽出_一覧」のNoボタン押下
        self.click_batch_job_button_by_label("現況届一括更新処理_抽出_一覧")

        # 13 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_13")

        # 14 バッチ起動画面: "現況年度「令和5年」選択 出力順「証書番号順」選択 発行年月日「20230802」"
        params = [
            {"title": "現況年度", "type": "select", "value": pGenkyoY},
            {"title": "出力順", "type": "select", "value": pOutputOrder},
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD},
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_14")

        # 15 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 16 バッチ起動画面: 表示	メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_16")

        # 17 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 18 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_18")

        # 19 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 20 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_20")

        # 21 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # self.screen_shot("ジョブ実行履歴画面_21")

        # 22 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 23 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_23")

        # 24 バッチ帳票履歴: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 25 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_25")

        # 26 バッチ帳票履歴: 「現況支給判定結果(一覧)」のNoボタン押下

        # 27 バッチ帳票履歴: 「ファイルを開く」ボタン押下

        # 28 現況支給判定結果(一覧)（PDF）: 表示
        # self.screen_shot("現況支給判定結果(一覧)（PDF）_28")

        # 29 現況支給判定結果(一覧)（PDF）: ×ボタン押下でPDFを閉じる

        # 30 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_30")
