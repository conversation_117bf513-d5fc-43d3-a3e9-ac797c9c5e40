import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_182103(FukushiSiteTestCaseBase):
    """TestQAC050_182103"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_182103"]
        super().setUp()

    # 書類が出力されていない受給者に対して、差止情報を登録できることを確認する。
    def test_QAC050_182103(self):
        """支払差止処理"""

        case_data = self.test_data["TestQAC050_182103"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_1")

        # 2 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 3 児童扶養手当資格管理画面: 「差止情報」ボタン押下
        self.click_button_by_label("差止情報")

        # 4 差止情報画面: 表示
        self.screen_shot("差止情報画面_4")

        # 5 差止情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 6 差止情報画面: 差止事由「障害認定判定待ち」選択支払差止決定日「20230502」支払差止開始年月「202306」
        self.form_input_by_id(idstr="CmbRiyu", text="障害認定判定待ち")
        self.form_input_by_id(idstr="TxtKettei", value="20230502")
        self.form_input_by_id(idstr="TxtKaishi", value="202306")
        self.screen_shot("差止情報画面_6")

        # 7 差止情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 8 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_8")

        # 9 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 10 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 11 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_11")
