import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181603(FukushiSiteTestCaseBase):
    """TestQAC050_181603"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181603"]
        super().setUp()

    # 債権情報の登録できることを確認する。
    def test_QAC050_181603(self):
        """返納方法登録"""

        case_data = self.test_data["TestQAC050_181603"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「債権管理」ボタン押下
        self.saiken_kanri_click()  # Button with ID: CmdProcess7_2 instead of self.click_button_by_label("債権管理")

        # 3 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_3")

        # 4 債権履歴画面: 事業「児童扶養手当」選択絞り込み条件「未返納のみ」チェック
        self.form_input_by_id(idstr="CmbGyomu", text="児童扶養手当")
        self.form_input_by_id(idstr="RadioM", value="1")
        self.screen_shot("債権履歴画面_4")

        # 5 債権履歴画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 6 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_6")

        # 7 債権履歴画面: 該当者一覧「1」ボタン押下
        self.click_button_by_label("1")  # Instead of self.click_button_by_label("該当者一覧1")

        # 8 債権情報画面: 表示
        self.screen_shot("債権情報画面_8")

        # 9 債権情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 10 債権情報画面: 調整債権区分「全額債権」チェック
        self.form_input_by_id(idstr="RadioZengakuS", value="1")
        self.screen_shot("債権情報画面_10")

        # 11 債権情報画面: 「計算」ボタン押下
        self.click_button_by_label("計算")

        # 12 債権情報画面: 「債権入力」ボタン押下
        self.click_button_by_label("債権入力")

        # 13 債権情報画面: 債務承認日「20230502」返納方法「口座振込」選択
        self.form_input_by_id(idstr="TxtSaimuYMD", value="20230502")
        self.form_input_by_id(idstr="CmbHennou", text="口座振込")  # TODO Not found ID and Spec note: 返納方法見つかりません
        self.screen_shot("債権情報画面_13")

        # 14 債権情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 15 債権情報画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("債権情報画面_15")

        # 16 債権情報画面: 「戻る」ボタン押下
        self.return_click()

        # 17 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_17")
