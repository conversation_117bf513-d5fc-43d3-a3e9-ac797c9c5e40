import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181007(FukushiSiteTestCaseBase):
    """TestQAC050_181007"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181007"]
        super().setUp()

    # 年金併給中でかつ手当月額が０円の場合、その他必要な情報の登録ができることを確認する。
    def test_QAC050_181007(self):
        """全部停止処理"""

        case_data = self.test_data["TestQAC050_181007"]
        atena_code = case_data.get("atena_code", "")
        shoushoHenpuYMD = case_data.get("shoushoHenpuYMD", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 証書返付年月日「20230602」
        self.form_input_by_id(idstr="TxtShoushoHenpuYMD", value=shoushoHenpuYMD)
        self.screen_shot("児童扶養手当資格管理画面_2")

        # 3 児童扶養手当資格管理画面: 「所得判定詳細情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="所得判定詳細情報")

        # 4 所得判定詳細情報画面: 表示
        self.screen_shot("所得判定詳細情報画面_4")

        # 5 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 6 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_6")

        # 7 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 8 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 9 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_9")
