import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180801(FukushiSiteTestCaseBase):
    """TestQAC050_180801"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180801"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC050", "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
                      "TARGET_NENDO":case_data.get("nendo", ""),"TARGET_SOUSYOTOKU":case_data.get("soushotoku", "")}
        self.exec_sqlfile("Test_QAC050_180801.sql", params=sql_params)
        super().setUp()

    # バッチで市内転居した住民を把握できることを確認する。
    def test_QAC050_180801(self):
        """届出情報入力"""

        case_data = self.test_data["TestQAC050_180801"]
        atena_code = case_data.get("atena_code", "")
        report_name = case_data.get("report_name", "")

        # =====資格登録=====
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        self.click_button_by_label("申請内容入力")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
        self.click_button_by_label("確定")
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230325")
        self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20230501")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("2")
        self.form_input_by_id(idstr="CmbZokugara", text="子")
        self.form_input_by_id(idstr="RdoDokyo1", value="1")
        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        self.form_input_by_id(idstr="KojiGaitou2", value="1")
        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        self.form_input_by_id(idstr="RdoShogai2", value="1")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("3")
        self.form_input_by_id(idstr="CmbZokugara", text="子")
        self.form_input_by_id(idstr="RdoDokyo1", value="1")
        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        self.form_input_by_id(idstr="KojiGaitou2", value="1")
        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        self.form_input_by_id(idstr="RdoShogai2", value="1")
        self.click_button_by_label("福祉世帯情報")
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230501")
        self.form_input_by_id(idstr="ChkFlg_4", value="1")
        self.form_input_by_id(idstr="HoninCmb_2", text="子")
        self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20230501")
        self.form_input_by_id(idstr="HoninCmb_3", text="子")
        self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20230501")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("所得情報")
        self.click_button_by_label("1")
        self.return_click()
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("口座情報")
        self.click_button_by_label("追加")
        self.entry_kouza_info(
            start_ymd="20230501",
            ginko_code="0005",
            shiten_code="001",
            kouza_shubetsu_text="普通",
            kouza_bango="7777777",
            koukai=True
        )
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.form_input_by_id(idstr="TxtKaitei", value="202305")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230401")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
        self.click_button_by_label("決定内容入力") 
        self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230401")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.form_input_by_id(idstr="TxtShoushoBango", value="180701")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.screen_shot("個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)  # not sure with self.click_button_by_label("検索") or self.click_job_exec_log_search()

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「児童扶養手当」ボタン押下
        self.click_button_by_label("児童扶養手当")

        # 8 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_8")

        # 9 児童扶養手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 10 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_10")

        # 11 帳票印刷画面:
        # 「住所変更（転出・転入）・金融機関変更届」行の印刷チェックボックス選択
        # 「住所変更（転出・転入）・金融機関変更届」行の発行年月日チェックボックス選択
        # 「住所変更（転出・転入）・金融機関変更届」行の発行年月日「20230601」
        self.switch_online_report_type("申請書")
        exec_params = [
            {
                "report_name": report_name,
                "params":[
                    {"title": "印刷種別", "value":"1","is_no_print":"1"},
                ]
            }
        ]
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_11")
        self.assert_message_area("プレビューを表示しました")

        # 12 帳票印刷画面: 「印刷」ボタン押下

        # 13 帳票印刷画面: 受給資格者台帳「ファイルを開く(O)」ボタンを押下

        # 14 住所変更（転出・転入）・金融機関変更届（PDF）: 表示
        #self.screen_shot("住所変更（転出・転入）・金融機関変更届（PDF）_14")

        # 15 住所変更（転出・転入）・金融機関変更届（PDF）: ×ボタン押下でPDFを閉じる
        # TODO: I don't know which function to close the pdf tab

        # 16 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 17 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_17")
