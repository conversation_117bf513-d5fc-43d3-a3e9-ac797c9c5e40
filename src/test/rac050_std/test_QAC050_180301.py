import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180301(FukushiSiteTestCaseBase):
    """TestQAC050_180301"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180301"]
        sql_params = {
            "TARGET_GYOUMU_CODE": "QAC050", 
            "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
            "TARGET_NENDO":"2022"
        }
        self.exec_sqlfile("Test_QAC050_180301.sql", params=sql_params)
        sql_params = {
            "TARGET_GYOUMU_CODE": "QAC050", 
            "DELETE_ATENA_CODE": case_data.get("atena_code2", ""),
            "TARGET_NENDO":"2022"
        }
        self.exec_sqlfile("Test_QAC050_180301.sql", params=sql_params)
        sql_params = {
            "TARGET_GYOUMU_CODE": "QAC050", 
            "DELETE_ATENA_CODE": case_data.get("atena_code3", ""),
            "TARGET_NENDO":"2022"
        }
        self.exec_sqlfile("Test_QAC050_180301.sql", params=sql_params)
        super().setUp()

    # 額改定請求した住民および対象児童や、その他必要な情報の登録ができることを確認する。
    def test_QAC050_180301(self):
        """請求情報入力"""

        case_data = self.test_data["TestQAC050_180301"]
        shinsei_shubetsu = case_data.get("shinsei_shubetsu", "")
        shinsei_riyuu = case_data.get("shinsei_riyuu", "")
        atena_codes = [case_data.get("atena_code", ""), case_data.get("atena_code2", ""), case_data.get("atena_code3", "")]

        for i,atena_code in enumerate(atena_codes):
            if i == 0: atena_no = "①"
            if i == 1: atena_no = "②"
            if i == 2: atena_no = "③"
            # =====資格登録=====
            self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
            self.click_button_by_label("申請内容入力")
            self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
            self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
            self.click_button_by_label("確定")
            self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
            self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
            self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
            self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230325")
            self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
            self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20220501")
            self.click_button_by_label("児童追加")
            self.click_button_by_label("2")
            self.form_input_by_id(idstr="CmbZokugara", text="子")
            self.form_input_by_id(idstr="RdoDokyo1", value="1")
            self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
            self.form_input_by_id(idstr="KojiGaitou2", value="1")
            self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
            self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
            self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
            self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
            self.form_input_by_id(idstr="RdoShogai2", value="1")
            self.click_button_by_label("入力完了")
            self.click_button_by_label("福祉世帯情報")
            self.form_input_by_id(idstr="HoninCmb_1", text="本人")
            self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
            self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230501")
            self.form_input_by_id(idstr="ChkFlg_4", value="1")
            self.form_input_by_id(idstr="HoninCmb_2", text="子")
            self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
            self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20230501")
            self.form_input_by_id(idstr="HoninCmb_3", text="子")
            self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
            self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20230501")
            self.click_button_by_label("入力完了")
            self.form_input_by_id(idstr="TxtKaitei", value="202305")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            can_shintatsu_button = self.click_button_by_label("本庁進達入力")
            if (can_shintatsu_button):
                self.click_button_by_label("本庁進達入力")
                self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230401")
                self.click_button_by_label("月額計算")
                self.click_button_by_label("登録")
                self.alert_ok()
                self.assert_message_area("登録しました")
                self.click_button_by_label("本庁進達結果入力")
                self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
                self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
                self.click_button_by_label("月額計算")
                self.click_button_by_label("登録")
                self.alert_ok()
                self.assert_message_area("登録しました")
            self.click_button_by_label("決定内容入力") 
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
            self.form_input_by_id(idstr="TxtKetteiYMD", value="20230401")
            self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
            if i == 0:
                self.form_input_by_id(idstr="TxtShoushoBango", value="18030101")
            elif i == 1:
                self.form_input_by_id(idstr="TxtShoushoBango", value="18030102")
            elif i == 2:
                self.form_input_by_id(idstr="TxtShoushoBango", value="18030103")
            else:
                pass

            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")

            self.do_login()
            # 1 メインメニュー画面: 表示
            self.screen_shot(f'メインメニュー画面_1_{atena_no}')

            # 2 メインメニュー画面: 「申請資格管理」ボタン押下
            self.shinsei_shikaku_kanri_click()

            # 3 個人検索画面: 表示
            self.screen_shot(f"個人検索画面_3_{atena_no}")

            # 4 個人検索画面: 「住民コード」入力
            self.screen_shot(f"個人検索画面_4_{atena_no}")

            # 5 個人検索画面: 「検索」ボタン押下
            self.kojin_kensaku_by_atena_code(atena_code=atena_code)

            # 6 受給状況画面: 表示
            self.screen_shot("受給状況画面_6")

            # 7 受給状況画面: 「児童扶養手当」ボタン押下
            self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC050")

            # 8 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_8_{atena_no}")

            # 9 児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
            self.click_button_by_label("申請内容入力")

            # 10 児童扶養手当資格管理画面: 申請種別「額改定（増額）」選択申請理由「未婚」選択
            self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinsei_shubetsu)
            self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinsei_riyuu)
            self.screen_shot(f"児童扶養手当資格管理画面_10_{atena_no}")

            # 11 児童扶養手当資格管理画面: 「確定」ボタン押下
            self.click_button_by_label("確定")

            # 12 児童扶養手当資格管理画面: 請求年月日「20230501」担当所管区「第一区」選択受給者区分「父または母」選択事由発生年月日「20230429」養育費の取決め有無「有」チェック
            self.form_input_by_id(idstr="TxtShinseiYMD", value="20230501")
            self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
            self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
            self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230429")
            self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
            self.screen_shot(f"児童扶養手当資格管理画面_12_{atena_no}")

            # 13 児童扶養手当資格管理画面: 「児童追加」ボタン押下
            self.click_button_by_label("児童追加")

            # 14 世帯員検索画面: 表示
            self.screen_shot(f"世帯員検索画面_14_{atena_no}")

            # 15 世帯員検索画面: 世帯員一覧「3」ボタン押下
            self.click_button_by_label("3")
            self.screen_shot(f"世帯員検索画面_15_{atena_no}")

            # 16 支給対象児童入力画面: 表示
            self.screen_shot(f"支給対象児童入力画面_16_{atena_no}")

            # 17 支給対象児童入力画面: 続柄「次男」を選択同居・別居の別「同居」チェック監護等を始めた年月日「20230429」孤児該当「非該当」チェック該当年月日「20230601」該当事由「未婚」選択支給事由発生年月日「20230429」当初支給開始年月日「20230601」障害有無「無」チェック
            self.form_input_by_id(idstr="CmbZokugara", text="子")
            self.form_input_by_id(idstr="DoukyoBekkyoRBtn1", value="1")
            self.form_input_by_id(idstr="TxtKangoYMD", value="20230429")
            self.form_input_by_id(idstr="KojiGaitou2", value="1")
            self.form_input_by_id(idstr="TxtGaitoYMD", value="20230601")
            self.form_input_by_id(idstr="CmbGaitoJiyu", text="未婚")
            self.form_input_by_id(idstr="TxtJiyuYMD", value="20230429")
            self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230601")
            self.form_input_by_id(idstr="RdoShogai2", value="1")
            self.screen_shot(f"支給対象児童入力画面_17_{atena_no}")

            # 18 支給対象児童入力画面: 「入力完了」ボタン押下
            self.click_button_by_label("入力完了")

            # 19 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_19_{atena_no}")

            # 20 児童扶養手当資格管理画面: 「福祉世帯情報」ボタン押下
            self.click_button_by_label("福祉世帯情報")

            # 21 福祉世帯情報画面: 表示
            self.screen_shot(f"福祉世帯情報画面_21_{atena_no}")

            # 22 福祉世帯情報画面: 3に対して本人から見た続柄「子」選択受給者との関係「対象児童」該当日「20230601」
            self.form_input_by_id(idstr="HoninCmb_3", text="子")
            self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
            self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20230601")
            self.screen_shot(f"福祉世帯情報画面_22_{atena_no}")

            # 23 福祉世帯情報画面: 「入力完了」ボタン押下
            self.click_button_by_label("入力完了")

            # 24 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_24_{atena_no}")

            # 25 児童扶養手当資格管理画面: 「メモ情報」ボタン押下
            self.open_common_buttons_area()
            self.click_button_by_label("メモ情報")

            # 26 メモ情報画面: 表示
            self.screen_shot(f"メモ情報画面_26_{atena_no}")

            # 27 メモ情報画面: 「追加」ボタン押下
            self.click_button_by_label("追加")

            # 28 メモ情報画面: 内容「あおいうえ」
            self.form_input_by_id(idstr="TxtNaiyo", value="あおいうえ")
            self.screen_shot(f"メモ情報画面_28_{atena_no}")

            # 29 メモ情報画面: 「登録」ボタン押下
            self.click_button_by_label("登録")
            self.alert_ok()

            # 30 メモ情報画面: 「戻る」ボタン押下
            self.return_click()

            # 31 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_31_{atena_no}")

            # 32 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
            self.click_button_by_label("提出書類管理")

            # 33 提出書類管理: 表示
            self.screen_shot(f"提出書類管理_33_{atena_no}")

            # 34 提出書類管理: 「追加」ボタン押下
            self.click_button_by_label("追加")

            # 35 提出書類管理: 戸籍謄抄本にチェック
            self.entry_teishutsu_shorui(shorui_name="戸籍謄抄本")
            self.screen_shot(f"提出書類管理_35_{atena_no}")

            # 36 提出書類管理: 「入力完了」ボタン押下
            self.click_button_by_label("入力完了")

            # 37 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_37_{atena_no}")

            # 38 児童扶養手当資格管理画面: 「月額計算」ボタン押下
            self.click_button_by_label("月額計算")

            # 39 児童扶養手当資格管理画面: 「登録」ボタン押下
            self.click_button_by_label("登録")
            self.alert_ok()

            # 40 児童扶養手当資格管理画面: 表示
            # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
            self.assert_message_area("登録しました。")
            self.screen_shot(f"児童扶養手当資格管理画面_40_{atena_no}")
