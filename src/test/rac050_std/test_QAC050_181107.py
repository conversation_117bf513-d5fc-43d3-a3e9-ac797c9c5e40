import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181107(FukushiSiteTestCaseBase):
    """TestQAC050_181107"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181107"]
        super().setUp()

    # 差止解除通知書を出力できることを確認する。
    def test_QAC050_181107(self):
        """支払差止解除通知書作成"""

        case_data = self.test_data["TestQAC050_181107"]
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「差止情報」ボタン押下
        self.click_button_by_label("修正")
        time.sleep(3)
        self.click_button_by_label("差止情報")

        # 2 差止情報画面: 表示
        self.screen_shot("差止情報画面_2")

        # 3 差止情報画面: 差止履歴「1」Noボタン押下
        self.click_button_by_label("1")

        # 4 差止情報画面: 発行年月日「20230602」
        self.form_input_by_id(idstr="TxtHakko", value="20230602")
        self.form_input_by_id(idstr="TxtJikaiShiharaiYMD", value="20230802")
        self.screen_shot("差止情報画面_4")

        # 5 差止情報画面: 「印刷」ボタン押下
        self.pdf_output_and_download_no_alert("CmdPrint",case_name="差止解除通知書")
        self.assert_message_area("プレビューを表示しました")

        # 6 差止情報画面: 差止解除通知書「ファイルを開く(O)」ボタンを押下

        # 7 差止解除通知書（PDF）: 表示
        # self.screen_shot("差止解除通知書（PDF）_7")

        # 8 差止情報画面: 「戻る」ボタン押下
        self.return_click()

        # 9 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_9")
