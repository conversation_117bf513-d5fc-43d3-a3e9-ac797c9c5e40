import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180105(FukushiSiteTestCaseBase):
    """TestQAC050_180105"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180105"]
        super().setUp()

    # 提出書類記載内容や所得情報、住記情報、年金情報等を確認する。
    def test_QAC050_180105(self):
        """認定審査"""

        case_data = self.test_data["TestQAC050_180105"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「進捗管理」ボタン押下
        self.click_button_by_label("進捗管理")

        # 3 進捗管理対象者検索画面: 表示
        self.screen_shot("進捗管理対象者検索画面_3")

        # 4 進捗管理対象者検索画面: 業務「児童」選択事業「児童扶養手当」選択
        self.form_input_by_id(idstr="Gyomu", text="児童")
        self.form_input_by_id(idstr="Jigyo", text="児童扶養手当")
        self.screen_shot("進捗管理対象者検索画面_4")

        # 5 進捗管理対象者検索画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 6 進捗管理対象者検索画面: 表示
        self.screen_shot("進捗管理対象者検索画面_6")

        # 7 進捗管理対象者検索画面: 申請年月日「20230401」申請種別「認定請求」選択
        self.form_input_by_id(idstr="ShinseiKaishiYMD", value="20230401")
        self.form_input_by_id(idstr="ShinseiShubetsu", text="認定請求")
        self.screen_shot("進捗管理対象者検索画面_7")

        # 8 進捗管理対象者検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 9 進捗管理対象者一覧画面: 表示
        self.screen_shot("進捗管理対象者一覧画面_9")

        # 10 進捗管理対象者一覧画面: 対象者一覧 Noボタン押下
        atena_col = '6'
        table_idx = 0
        th_idx = 0
        tr_elem = self.find_elements_by_css_selector("#_wrFk_TitleTable_Div_1 > table > tbody > tr")
        for elem in tr_elem:
            table_idx += 1
            try:
                td_elem = elem.find_element(By.CSS_SELECTOR, "td:nth-child(" + atena_col + ")")
            except Exception:
                th_idx +=1
                continue
            if atena_code == td_elem.text:
                table_idx = table_idx - th_idx
                self.click_by_id("NoBtn" + str(table_idx))
                break

        # 11 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_11")

        # 12 児童扶養手当資格管理画面: 「所得情報」ボタン押下
        self.click_button_by_label("所得情報")

        # 13 税世帯情報画面: 表示
        self.screen_shot("税世帯情報画面_13")

        # 14 税世帯情報画面: 年度指定「令和04年」選択
        self.form_input_by_id(idstr="CmbNendo", text="令和04年")
        self.screen_shot("税世帯情報画面_14")

        # 15 税世帯情報画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 16 税世帯情報画面: 表示
        self.screen_shot("税世帯情報画面_16")

        # 17 税世帯情報画面: 対象者世帯一覧「1」Noボタン押下
        self.click_button_by_label("1")

        # 18 税個人情報画面: 表示
        self.screen_shot("税個人情報画面_18")

        # 19 税個人情報画面: 「戻る」ボタン押下
        self.return_click()

        # 20 税世帯情報画面: 表示
        self.screen_shot("税世帯情報画面_20")

        # 21 税世帯情報画面: 「戻る」ボタン押下
        self.return_click()

        # 22 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_22")

        # 23 児童扶養手当資格管理画面: 「住記情報」ボタン押下
        self.click_button_by_label("住記情報")

        # 24 世帯一覧画面: 表示
        self.screen_shot("世帯一覧画面_24")

        # 25 世帯一覧画面: 世帯一覧「1」Noボタン押下
        self.click_button_by_label("1")

        # 26 住記情報: 表示
        self.screen_shot("住記情報_26")

        # 27 住記情報: 「戻る」ボタン押下
        self.return_click()

        # 28 世帯一覧画面: 表示
        self.screen_shot("世帯一覧画面_28")

        # 29 世帯一覧画面: 「戻る」ボタン押下
        self.return_click()

        # 30 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_30")

        # 31 児童扶養手当資格管理画面: 「年金情報登録」ボタン押下
        self.click_button_by_label("年金情報登録")

        # 32 年金情報: 表示
        self.screen_shot("年金情報_32")

        # 33 年金情報: 「戻る」ボタン押下
        self.return_click()

        # 34 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_34")

        # 35 児童扶養手当資格管理画面: 「戻る」ボタン押下
        self.return_click()

        # 36 進捗管理対象者一覧画面: 表示
        self.screen_shot("進捗管理対象者一覧画面_36")

        # 37 進捗管理対象者一覧画面: 「戻る」ボタン押下
        self.return_click()

        # 38 進捗管理対象者検索画面: 表示
        self.screen_shot("進捗管理対象者検索画面_38")
