import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180804(FukushiSiteTestCaseBase):
    """TestQAC050_180804"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180804"]
        super().setUp()

    # 不足書類があるため保留となっている対象者について、保留通知書等を出力できることを確認する。
    def test_QAC050_180804(self):
        """保留通知書等作成"""

        case_data = self.test_data["TestQAC050_180804"]
        atena_code = case_data.get("atena_code", "")
        report_name = case_data.get("report_name", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        
        #保留決定
        #self.do_login()
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230601")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230601")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
        self.click_button_by_label("決定内容入力") 
        self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230601")
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230601")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="保留")
        self.form_input_by_id(idstr="TxtShoushoBango", value="180804")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")
        # 1 児童扶養手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_2")

        # 3 帳票印刷画面:
        # 「住所変更（転出・転入）・金融機関変更届」行の印刷チェックボックス選択
        # 「住所変更（転出・転入）・金融機関変更届」行の発行年月日チェックボックス選択
        # 「住所変更（転出・転入）・金融機関変更届」行の発行年月日「20230601」
        self.switch_online_report_type("申請書")
        exec_params = [
            {
                "report_name": report_name,
                "params":[
                    {"title": "印刷種別", "value":"1","is_no_print":"1"},
                ]
            }
        ]
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_3")
        self.assert_message_area("プレビューを表示しました")

        # 4 帳票印刷画面: 「印刷」ボタン押下

        # 5 帳票印刷画面: 受給資格者台帳「ファイルを開く(O)」ボタンを押下

        # 6 住所変更（転出・転入）・金融機関変更届（PDF）: 表示
        #self.screen_shot("住所変更（転出・転入）・金融機関変更届（PDF）_6")

        # 7 住所変更（転出・転入）・金融機関変更届（PDF）: ×ボタン押下でPDFを閉じる
        # TODO: I don't know which function to close the pdf tab

        # 8 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 9 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_9")

        # 10 メインメニュー画面: 表示
        self.do_login() # TODO not sure based on spec if this is required
        self.screen_shot("メインメニュー画面_10")

        # 11 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()  # Button with ID: CmdProcess4_1 instead of self.click_button_by_label("バッチ起動")

        # 12 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_12")

        # 13 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：不足書類督促
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="不足書類督促")
        self.screen_shot("バッチ起動画面_13")

        # 14 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_14")

        # 15 バッチ起動画面: 「不足書類督促抽出処理」のNoボタン押下
        self.click_batch_job_button_by_label("不足書類督促抽出処理")  # Instead of self.click_by_id("Sel1")

        # 16 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_16")

        # 17 バッチ起動画面: 基準日「20230701」出力順「カナ氏名順」
        params = [
            {"title": "基準日", "type": "text", "value": "20230701"},
            {"title": "出力順", "type": "select", "value": "カナ氏名順"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_17")

        # 18 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()  # Button with ID: ExecuteButton

        # 19 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_19")

        # 20 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()  # Button with ID: JobListButton

        # 21 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_21")

        # 22 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.assert_job_normal_end(exec_datetime=exec_datetime)  # Button with ID: SearchButton
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 23 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_23")

        # 24 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # TODO unknown how to code this case
        self.screen_shot("ジョブ実行履歴画面_24")

        # 25 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()  # Button with ID: ReportListButton

        # 26 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_26")

        # 27 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)  # Button with ID: SearchButton

        # 28 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_28")

        # 29 ジョブ帳票履歴画面: 「督促用決済名簿」のNoボタン押下

        # 30 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 31 督促用決済名簿（PDF）: 表示
        #self.screen_shot("督促用決済名簿（PDF）_31")

        # 32 督促用決済名簿（PDF）: ×ボタン押下でPDFを閉じる
        # TODO: I don't know which function to close the pdf tab

        # 33 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_33")

        # 34 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()  # Button with ID: ExecListButton

        # 35 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_35")

        # 36 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()  # Button with ID: ExecListButton

        # 37 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_37")

        # 38 バッチ起動画面: 「不足書類督促状出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("不足書類督促状出力処理")  # Instead of self.click_by_id("Sel2")

        # 39 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_39")

        # 40 バッチ起動画面: 提出期限「(空白)」
        params = [
            {"title": "提出期限", "type": "text", "value": "20240701"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_40")

        # 41 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()  # Button with ID: ExecuteButton

        # 42 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_42")

        # 43 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()  # Button with ID: JobListButton

        # 44 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_44")

        # 45 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.assert_job_normal_end(exec_datetime=exec_datetime)  # Button with ID: SearchButton
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 46 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_46")

        # 47 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # TODO unknown how to code this case
        self.screen_shot("ジョブ実行履歴画面_47")

        # 48 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()  # Button with ID: ReportListButton

        # 49 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_49")

        # 50 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)  # Button with ID: SearchButton

        # 51 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_51")

        # 52 ジョブ帳票履歴画面: 「保留通知書」のNoボタン押下

        # 53 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 54 保留通知書（PDF）: 表示
        #self.screen_shot("保留通知書（PDF）_54")

        # 55 保留通知書（PDF）: ×ボタン押下でPDFを閉じる
        # TODO: I don't know which function to close the pdf tab

        # 56 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_56")

        # 57 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()  # Button with ID: ExecListButton

        # 58 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_58")

        # 59 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()  # Button with ID: ExecListButton

        # 60 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_60")

        # 61 バッチ起動画面: 「不足書類テーブル更新処理」のNoボタン押下
        self.click_batch_job_button_by_label("不足書類テーブル更新処理")  # Instead of self.click_by_id("Sel3")

        # 62 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_62")

        # 63 バッチ起動画面: 督促年月日「20230701」決定年月日「20230701」
        params = [
            {"title": "督促年月日", "type": "text", "value": "20230701"},
            {"title": "決定年月日", "type": "text", "value": "20230701"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_63")

        # 64 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()  # Button with ID: ExecuteButton

        # 65 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_65")

        # 66 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()  # Button with ID: JobListButton

        # 67 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_67")

        # 68 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.assert_job_normal_end(exec_datetime=exec_datetime)  # Button with ID: SearchButton
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 69 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_69")

        # 70 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # TODO unknown how to code this case
        self.screen_shot("ジョブ実行履歴画面_70")
