import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180510(FukushiSiteTestCaseBase):
    """TestQAC050_180510"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180510"]
        super().setUp()

    # 債権情報の登録できることを確認する。
    def test_QAC050_180510(self):
        """返納方法登録"""

        case_data = self.test_data["TestQAC050_180510"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 「債権履歴」ボタン押下
        self.open_common_buttons_area()
        self.common_button_click("債権履歴")

        # 3 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_3")

        # 4 債権履歴画面: 該当者一覧「1」ボタン押下
        self.click_button_by_label("1")

        # 5 債権情報画面: 表示
        self.screen_shot("債権情報画面_5")

        # 6 債権情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 7 債権情報画面: 調整債権区分「全額債権」チェック「計算」ボタン押下
        self.form_input_by_id(idstr="RadioZengakuS", value="1")
        self.click_button_by_label("計算")
        self.screen_shot("債権情報画面_7")

        # 8 債権情報画面: 「債権入力」ボタン押下
        self.click_button_by_label("債権入力")

        # 9 債権情報画面: 債務承認日「20230702」返納方法「口座振込」選択
        self.form_input_by_id(idstr="TxtSaimuYMD", value="20230702")
        self.form_input_by_id(idstr="CmbHennou", text="口座振込")   #返納方法表示はシステム設定コード=QACF013008で制御
        self.screen_shot("債権情報画面_9")

        # 10 債権情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 11 債権情報画面: 「戻る」ボタン押下
        self.return_click()

        # 12 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_12")

        # 13 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 14 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 15 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_15")
