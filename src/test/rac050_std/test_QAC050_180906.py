import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180906(FukushiSiteTestCaseBase):
    """TestQAC050_180906"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180906"]
        super().setUp()

    # 支給区分の判定を行うことができることを確認する。
    def test_QAC050_180906(self):
        """所得判定処理"""

        case_data = self.test_data["TestQAC050_180906"]
        atena_codes = [case_data.get("atena_code_1", ""), case_data.get("atena_code_2", ""), case_data.get("atena_code_3", "")]

        for i,atena_code in enumerate(atena_codes):
            if i == 0: atena_no = "①"
            if i == 1: atena_no = "②"
            if i == 2: atena_no = "③"
            self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

            # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
            self.click_button_by_label("修正")

            # 2 児童扶養手当資格管理画面: 決定年月日「20230602」決定結果「決定」改定年月「202307」
            self.form_input_by_id(idstr="TxtKetteiYMD", value="20230602")
            self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
            self.form_input_by_id(idstr="TxtKaitei", value="202307")
            self.screen_shot(f"児童扶養手当資格管理画面_2_{atena_no}")

            # 3 児童扶養手当資格管理画面: 児童のNoボタン押下
            self.click_by_id("CmdNo1")

            # 4 支給対象児童入力画面: 表示
            self.screen_shot(f"支給対象児童入力画面_4_{atena_no}")

            # 5 支給対象児童入力画面: 「戻る」ボタン押下
            self.return_click()

            # 6 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_6_{atena_no}")

            # 7 児童扶養手当資格管理画面: 「所得判定詳細情報」ボタン押下
            self.open_common_buttons_area()
            self.click_button_by_label("所得判定詳細情報")

            # 8 所得判定詳細情報画面: 表示
            self.screen_shot(f"所得判定詳細情報画面_8_{atena_no}")

            # 9 所得判定詳細情報画面: 「戻る」ボタン押下
            self.return_click()

            # 10 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_10_{atena_no}")

            # 11 児童扶養手当資格管理画面: 「月額計算」ボタン押下
            self.click_button_by_label("月額計算")

            # 12 児童扶養手当資格管理画面: 「登録」ボタン押下
            self.click_button_by_label("登録")
            self.alert_ok()

            # 13 児童扶養手当資格管理画面: 表示
            # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
            self.assert_message_area("登録しました。")
            self.screen_shot(f"児童扶養手当資格管理画面_13_{atena_no}")
