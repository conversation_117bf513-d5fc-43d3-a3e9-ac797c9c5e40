import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180707(FukushiSiteTestCaseBase):
    """TestQAC050_180707"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180707"]
        super().setUp()

    # 未支払支払通知書を出力できることを確認する。
    def test_QAC050_180707(self):
        """支払通知書等作成"""

        case_data = self.test_data["TestQAC050_180707"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：支払処理
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="支払処理")

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「支払計算処理」のNoボタン押下
        self.click_batch_job_button_by_label("支払計算処理")

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面:
        # 支払区分「随時」選択
        # 対象年月「202308」
        # 振込年月日「20230810」
        params = [
            {"title": "支払区分", "type": "select", "value": "随時"},
            {"title": "対象年月", "type": "text", "value": "202308"},
            {"title": "振込年月日", "type": "text", "value": "20230810"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 17 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_17")

        # 18 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 19 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_19")

        # 20 バッチ起動画面: 「支払通知書データ作成」のNoボタン押下
        self.click_batch_job_button_by_label("支払通知書データ作成")

        # 21 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_21")

        # 22 バッチ起動画面: 出力順「証書番号順」選択発行年月日「20230702」
        params = [
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "発行年月日", "type": "text", "value": "20230702"},
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_22")

        # 23 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 24 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_24")

        # 25 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 26 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_26")

        # 27 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 28 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_28")

        # 29 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_29")

        # 30 ジョブ実行履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 31 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_31")

        # 32 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 33 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_33")

        # 34 バッチ起動画面: 「支払通知書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("支払通知書出力処理")

        # 35 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_35")

        # 36 バッチ起動画面: 出力区分「0」開始頁「000001」終了頁「999999」
        params = [
            {"title": "出力区分", "type": "text", "value": "0"},
            {"title": "開始頁", "type": "text", "value": "000001"},
            {"title": "終了頁", "type": "text", "value": "999999"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_36")

        # 37 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 38 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_38")

        # 39 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 40 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_40")

        # 41 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 42 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_42")

        # 43 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_43")

        # 44 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 45 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_45")

        # 46 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 47 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_47")

        # 48 ジョブ帳票履歴画面: 「未支払_支払通知書」のNoボタン押下

        # 49 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 50 未支払_支払通知書（PDF）: 表示
        # self.screen_shot("未支払_支払通知書（PDF）_50")

        # 51 未支払_支払通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 52 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_52")

        # 53 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 54 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_54")

        # 55 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 56 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_56")

        # 57 バッチ起動画面: 「支払更新処理」のNoボタン押下
        self.click_batch_job_button_by_label("支払更新処理")

        # 58 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_58")

        # 59 バッチ起動画面:
        # 支払区分「随時」選択
        # 対象年月「202308」
        # 振込年月日「20230810」
        params = [
            {"title": "支払区分", "type": "select", "value": "随時"},
            {"title": "対象年月", "type": "text", "value": "202308"},
            {"title": "振込年月日", "type": "text", "value": "20230810"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_59")

        # 60 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 61 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_61")

        # 62 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 63 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_63")

        # 64 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 65 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_65")

        # 66 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_66")
