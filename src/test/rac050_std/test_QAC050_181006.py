import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181006(FukushiSiteTestCaseBase):
    """TestQAC050_181006"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181006"]
        super().setUp()

    # 公的年金給付等受給届を提出した住民に対し、提出書類や所得情報等を確認する、
    def test_QAC050_181006(self):
        """公的年金併給認定処理"""

        case_data = self.test_data["TestQAC050_181006"]
        atena_code_1 = case_data.get("atena_code_1", "")
        atena_code_2 = case_data.get("atena_code_2", "")
        atena_code_3 = case_data.get("atena_code_3", "")
        txtKetteiYMD = case_data.get("txtKetteiYMD", "")
        ketteiKekkaCmb = case_data.get("ketteiKekkaCmb", "")
        txtKaitei = case_data.get("txtKaitei", "")
        nenkinSagakuChkBox = case_data.get("nenkinSagakuChkBox", "")
        shougaikiso_true = case_data.get("shougaikiso_true", "")
        kasan4 = case_data.get("kasan4", "")
        txtKetteiYMD_2 = case_data.get("txtKetteiYMD_2", "")
        ketteiKekkaCmb_2 = case_data.get("ketteiKekkaCmb_2", "")
        txtKaitei_2 = case_data.get("txtKaitei_2", "")
        nenkinSagakuChkBox_2 = case_data.get("nenkinSagakuChkBox_2", "")

# 申請理由が「年金受給開始」の場合（②書類不備なし、併給認定で「一部支給」になる場合） -s
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code_2, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 2 児童扶養手当資格管理画面: 決定年月日「20230602」決定結果「決定」改定年月「202307」
        self.form_input_by_id(idstr="TxtKetteiYMD", value=txtKetteiYMD)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=ketteiKekkaCmb)
        self.form_input_by_id(idstr="TxtKaitei", value=txtKaitei)
        self.screen_shot("1_児童扶養手当資格管理画面_2")

        # 3 児童扶養手当資格管理画面: 公的年金等停止額チェックする
        self.form_input_by_id(idstr="NenkinSagakuChkBox", value=nenkinSagakuChkBox)

        # 4 児童扶養手当資格管理画面: 「公的年金等停止額」ボタン押下
        self.click_button_by_label("公的年金等停止額")

        # 5 年金登録確認画面: 表示
        self.screen_shot("1_年金登録確認画面_5")

        # 6 年金登録確認画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 7 年金登録確認画面: 年金入力欄の障害基礎年金「該当」チェック第３項による停止額の児童１「228,700」
        self.form_input_by_id(idstr="shougaikiso_true", value=shougaikiso_true)
        self.form_input_by_id(idstr="kasan4", value=kasan4)

        # 8 年金登録確認画面: 「計算」ボタン押下
        self.click_button_by_label("計算")

        # 9 年金登録確認画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 10 児童扶養手当資格管理画面: 表示
        self.screen_shot("1_児童扶養手当資格管理画面_10")

        # 11 児童扶養手当資格管理画面: 「所得判定詳細情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="所得判定詳細情報")

        # 12 所得判定詳細情報画面: 表示
        self.screen_shot("1_所得判定詳細情報画面_12")

        # 13 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 14 児童扶養手当資格管理画面: 表示
        self.screen_shot("1_児童扶養手当資格管理画面_14")

        # 15 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")
        self.screen_shot("1_児童扶養手当資格管理画面_15")

        # 16 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.screen_shot("1_児童扶養手当資格管理画面_16")

        # 17 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("1_児童扶養手当資格管理画面_17")
# 申請理由が「年金受給開始」の場合（②書類不備なし、併給認定で「一部支給」になる場合） -e

# 申請理由が「年金受給開始」の場合（③書類不備なし、併給認定で「全部停止」になる場合） -s
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code_3, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 2 児童扶養手当資格管理画面: 決定年月日「20230602」決定結果「決定」改定年月「202307」
        self.form_input_by_id(idstr="TxtKetteiYMD", value=txtKetteiYMD)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=ketteiKekkaCmb)
        self.form_input_by_id(idstr="TxtKaitei", value=txtKaitei)
        self.screen_shot("2_児童扶養手当資格管理画面_2")

        # 3 児童扶養手当資格管理画面: 公的年金等停止額チェックする
        self.form_input_by_id(idstr="NenkinSagakuChkBox", value=nenkinSagakuChkBox)

        # 4 児童扶養手当資格管理画面: 「公的年金等停止額」ボタン押下
        self.click_button_by_label("公的年金等停止額")

        # 5 年金登録確認画面: 表示
        self.screen_shot("2_年金登録確認画面_5")

        # 6 年金登録確認画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 7 年金登録確認画面: 年金入力欄の障害基礎年金「該当」チェック第３項による停止額の児童１「228,700」
        self.form_input_by_id(idstr="shougaikiso_true", value=shougaikiso_true)
        self.form_input_by_id(idstr="kasan4", value=kasan4)

        # 8 年金登録確認画面: 「計算」ボタン押下
        self.click_button_by_label("計算")

        # 9 年金登録確認画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 10 児童扶養手当資格管理画面: 表示
        self.screen_shot("2_児童扶養手当資格管理画面_10")

        # 11 児童扶養手当資格管理画面: 「所得判定詳細情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="所得判定詳細情報")

        # 12 所得判定詳細情報画面: 表示
        self.screen_shot("2_所得判定詳細情報画面_12")

        # 13 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 14 児童扶養手当資格管理画面: 表示
        self.screen_shot("2_児童扶養手当資格管理画面_14")

        # 15 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")
        self.screen_shot("2_児童扶養手当資格管理画面_15")

        # 16 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.screen_shot("2_児童扶養手当資格管理画面_16")

        # 17 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("2_児童扶養手当資格管理画面_17")
# 申請理由が「年金受給開始」の場合（③書類不備なし、併給認定で「全部停止」になる場合） -e

# 申請理由が「年金受給終了」の場合（①書類不備なし、併給認定で「全部支給」になる場合） -s
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code_1, gyoumu_code="QAC050")

        # 18 児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 19 児童扶養手当資格管理画面: 決定年月日「20230602」決定結果「決定」改定年月「202307」
        self.form_input_by_id(idstr="TxtKetteiYMD", value=txtKetteiYMD_2)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=ketteiKekkaCmb_2)
        self.form_input_by_id(idstr="TxtKaitei", value=txtKaitei_2)
        self.screen_shot("3_児童扶養手当資格管理画面_19")

        # 20 児童扶養手当資格管理画面: 「公的年金等停止額」ボタン押下
        self.click_button_by_label("公的年金等停止額")

        # 21 年金登録確認画面: 表示
        self.screen_shot("3_年金登録確認画面_21")

        # 22 年金登録確認画面: 「戻る」ボタン押下
        self.return_click()

        # 23 児童扶養手当資格管理画面: 表示
        self.screen_shot("3_児童扶養手当資格管理画面_23")

        # 24 児童扶養手当資格管理画面: 公的年金等停止額「0」公的年金等停止額チェックを外す
        self.form_input_by_id(idstr="NenkinSagakuChkBox", value=nenkinSagakuChkBox_2)
        self.screen_shot("3_児童扶養手当資格管理画面_24")

        # 25 児童扶養手当資格管理画面: 「所得判定詳細情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="所得判定詳細情報")

        # 26 所得判定詳細情報画面: 表示
        self.screen_shot("3_所得判定詳細情報画面_26")

        # 27 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 28 児童扶養手当資格管理画面: 表示
        self.screen_shot("3_児童扶養手当資格管理画面_28")

        # 29 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 30 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 31 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("3_児童扶養手当資格管理画面_31")
# 申請理由が「年金受給終了」の場合（①書類不備なし、併給認定で「全部支給」になる場合） -e