import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_182302(FukushiSiteTestCaseBase):
    """TestQAC050_182302"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_182302"]
        super().setUp()

    # 不備があった書類の登録ができることを確認する。
    def test_QAC050_182302(self):
        """不備書類入力"""

        case_data = self.test_data["TestQAC050_182302"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("提出書類管理")

        # 3 提出書類管理: 表示
        self.screen_shot("提出書類管理_3")

        # 4 提出書類管理: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 5 提出書類管理: 所得証明書にチェック
        self.form_input_by_id(idstr="ChkTeisyutsu_3", value="1")
        self.screen_shot("提出書類管理_5")

        # 6 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        self.click_button_by_label("確定")

        # 7 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_7")

        # 8 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 9 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 10 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_10")

        # 11 児童扶養手当資格管理画面: 「本庁進達入力」ボタン押下
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")

            # 12 児童扶養手当資格管理画面: 進達日「20230602」
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230602")
            self.screen_shot("児童扶養手当資格管理画面_12")
        
            # 13 児童扶養手当資格管理画面: 「月額計算」ボタン押下
            self.click_button_by_label("月額計算")
        
            # 14 児童扶養手当資格管理画面: 「登録」ボタン押下
            self.click_button_by_label("登録")
            self.alert_ok()
        
            # 15 児童扶養手当資格管理画面: 表示
            # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
            self.assert_message_area("登録しました")
            self.screen_shot("児童扶養手当資格管理画面_15")

            # 16 児童扶養手当資格管理画面: 「本庁進達結果入力」ボタン押下
            self.click_button_by_label("本庁進達結果入力")
        
            # 17 児童扶養手当資格管理画面: 進達判定年月日「20230602」進達結果「決定」
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230602")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.screen_shot("児童扶養手当資格管理画面_17")
        
            # 18 児童扶養手当資格管理画面: 「月額計算」ボタン押下
            self.click_button_by_label("月額計算")

            # 19 児童扶養手当資格管理画面: 「登録」ボタン押下
            self.click_button_by_label("登録")
            self.alert_ok()

            # 20 児童扶養手当資格管理画面: 表示
            # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。 
            self.assert_message_area("登録しました。")
            self.screen_shot("児童扶養手当資格管理画面_20")
        
        # 21 児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 22 児童扶養手当資格管理画面: 決定年月日「20230602」決定結果「保留」改定年月「202307」
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230602")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="保留")
        self.form_input_by_id(idstr="TxtKaitei", value="202307")
        self.screen_shot("児童扶養手当資格管理画面_22")

        # 23 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 24 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 25 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_25")
