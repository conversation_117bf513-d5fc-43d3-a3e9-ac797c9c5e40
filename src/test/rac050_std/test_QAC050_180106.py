import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180106(FukushiSiteTestCaseBase):
    """TestQAC050_180106"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180106"]
        super().setUp()

    # 認定請求した住民に対し決定登録ができることを確認する。かつ証書番号が付与されていることを確認する。
    def test_QAC050_180106(self):
        """認定処理"""

        case_data = self.test_data["TestQAC050_180106"]
        atena_code = case_data.get("atena_code", "")
        report_name = case_data.get("report_name", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")

        # 進達入力
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230501")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230501")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
        
        # 1 児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 2 児童扶養手当資格管理画面: 決定年月日「20230502」決定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230502")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.screen_shot("児童扶養手当資格管理画面_2")

        # 3 児童扶養手当資格管理画面: 児童のNoボタン押下
        self.click_by_id("CmdNo1")

        # 4 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_4")

        # 5 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 6 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_6")

        # 7 児童扶養手当資格管理画面: 「所得判定詳細情報」ボタン押下
        self.click_button_by_label("所得判定詳細情報")

        # 8 所得判定詳細情報画面: 表示
        self.screen_shot("所得判定詳細情報画面_8")

        # 9 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 10 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_10")

        # 11 児童扶養手当資格管理画面: 「被災情報」ボタン押下
        # self.click_button_by_label("被災情報")

        # 12 被災情報画面: 表示
        # self.screen_shot("被災情報画面_12")

        # 13 被災情報画面: 「戻る」ボタン押下
        # self.return_click()

        # 14 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_14")

        # 15 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 16 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 17 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_17")

        # 18 児童扶養手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 19 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_19")

        # 20 帳票印刷画面: 「受給資格者台帳」行の印刷チェックボックス選択「受給資格者台帳」行の発行年月日チェックボックス選択「受給資格者台帳」行の発行年月日「20230502」
        exec_params = [
            {
                "report_name": report_name,
                "params":[
                    {"title": "発行年月日", "value":hakkou_ymd,"is_no_print":"1"},
                ]
            }
        ]
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_20")

        # 21 帳票印刷画面: 「印刷」ボタン押下
        # self.click_button_by_label("印刷")

        # 22 帳票印刷画面: 受給資格者台帳「ファイルを開く(O)」ボタンを押下

        # 23 受給資格者台帳（PDF）: 表示
        # self.screen_shot("受給資格者台帳（PDF）_24")

        # 24 受給資格者台帳（PDF）: 2ページ目表示
        # Assert: 中面が出力されていることを確認する。
        # self.screen_shot("受給資格者台帳（PDF）_25")

        # 25 受給資格者台帳（PDF）: 3ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("受給資格者台帳（PDF）_26")

        # 26 受給資格者台帳（PDF）: ×ボタン押下でPDFを閉じる

        # 27 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 28 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_28")

        # 29 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_29")

        # 30 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 31 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_31")

        # 32 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：通知書一括処理
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="確認処理")
        self.screen_shot("バッチ起動画面_32")

        # 33 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_33")

        # 34 バッチ起動画面: 「受給者台帳出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("受給者台帳出力処理")

        # 35 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_35")

        # 36 バッチ起動画面: 現在受給区分「現受給者」選択基準年月日「20230502」開始証書番号「0000000000」終了証書番号「9999999999」出力順「証書番号順」選択
        params = [
            #{"title": "現在受給区分", "type": "select", "value": "現受給者"},
            {"title": "基準年月日", "type": "text", "value": "20230502"},
            {"title": "証書番号開始", "type": "text", "value": "0000000000"},
            {"title": "証書番号終了", "type": "text", "value": "9999999999"},
            {"title": "出力順", "type": "select", "value": "証書番号順"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_36")

        # 37 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 38 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_38")

        # 39 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 40 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_40")

        # 41 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 42 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_42")

        # 43 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_43")

        # 44 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 45 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_45")

        # 46 バッチ帳票履歴: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 47 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_47")

        # 48 バッチ帳票履歴: 「受給資格者台帳」のNoボタン押下

        # 49 バッチ帳票履歴: 「ファイルを開く」ボタン押下

        # 50 受給資格者台帳（PDF）: 表示
        # self.screen_shot("受給資格者台帳（PDF）_52")

        # 51 受給資格者台帳（PDF）: 2ページ目表示
        # Assert: 中面が出力されていることを確認する。
        # self.screen_shot("受給資格者台帳（PDF）_53")

        # 52 受給資格者台帳（PDF）: 3ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("受給資格者台帳（PDF）_54")

        # 53 受給資格者台帳（PDF）: ×ボタン押下でPDFを閉じる

        # 54 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_54")
