import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_182802(FukushiSiteTestCaseBase):
    """TestQAC050_182802"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # オンライン申請内容の修正ができることを確認する。
    def test_QAC050_182802(self):
        """申請情報の補正および登録"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「オンライン申請管理」ボタン押下
        self.master_maintenance_click()

        # 3 オンライン申請データ検索画面: 表示
        self.screen_shot("オンライン申請データ検索画面_3")

        # 4 オンライン申請データ検索画面: 業務「児童」選択事業「児童扶養手当」選択申請種類「児童扶養手当等の現況届」選択処理状況「（空欄）」選択申請日開始「（空欄）」申請日終了「（空欄）」住民照合区分「（空欄）」
        self.form_input_by_id(idstr="Cmb業務", text="児童")
        self.form_input_by_id(idstr="Cmb事業", text="児童扶養手当")
        self.form_input_by_id(idstr="Cmb申請種類", text="児童扶養手当等の現況届")
        self.form_input_by_id(idstr="Cmb処理状況", text="")
        self.form_input_by_id(idstr="Txt申請日開始", value="")
        self.form_input_by_id(idstr="Txt申請日終了", value="")
        self.form_input_by_id(idstr="Cmb住民照合区分", text="")

        # 5 オンライン申請データ検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 6 オンライン申請データ検索結果一覧画面: 表示
        self.screen_shot("オンライン申請データ検索結果一覧画面_6")

        # 7 オンライン申請データ検索結果一覧画面: 対象者一覧「1｣Noボタン押下
        self.click_by_id("Sel1")

        # 8 オンライン申請データ管理画面: 表示
        self.screen_shot("オンライン申請データ管理画面_8")

        # 9 オンライン申請データ管理画面: 「申請情報」ボタン押下
        self.click_button_by_label("申請情報")

        # 10 オンライン申請データ管理画面: 表示
        self.screen_shot("オンライン申請データ管理画面_10")

        # 11 オンライン申請データ管理画面: 「資格情報」ボタン押下
        self.click_button_by_label("資格情報")

        # 12 オンライン申請データ管理画面: 表示
        self.screen_shot("オンライン申請データ管理画面_12")

        # 13 オンライン申請データ管理画面: 「連絡先」ボタン押下
        self.click_button_by_label("連絡先")

        # 14 オンライン申請データ管理画面: 表示
        self.screen_shot("オンライン申請データ管理画面_14")

        # 15 オンライン申請データ管理画面: 「課税地」ボタン押下
        self.click_button_by_label("課税地")

        # 16 オンライン申請データ管理画面: 表示
        self.screen_shot("オンライン申請データ管理画面_16")

        # 17 オンライン申請データ管理画面: 「児童」ボタン押下
        self.click_button_by_label("児童")

        # 18 オンライン申請データ管理画面: 表示
        self.screen_shot("オンライン申請データ管理画面_18")

        # 19 オンライン申請データ管理画面: 「配偶者」ボタン押下
        self.click_button_by_label("配偶者")

        # 20 オンライン申請データ管理画面: 表示
        self.screen_shot("オンライン申請データ管理画面_20")

        # 21 オンライン申請データ管理画面: 「公金受取口利用」ボタン押下
        self.click_button_by_label("公金受取口利用")

        # 22 オンライン申請データ管理画面: 表示
        self.screen_shot("オンライン申請データ管理画面_22")

        # 23 オンライン申請データ管理画面: 「現況」ボタン押下
        self.click_button_by_label("現況")

        # 24 オンライン申請データ管理画面: 表示
        self.screen_shot("オンライン申請データ管理画面_24")

        # 25 オンライン申請データ管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 26 オンライン申請データ管理画面: 提出日「20240802」
        # self.form_input_by_id(idstr="", value="20240802")
        # NG: Id of 提出日 is not found

        # 27 オンライン申請データ管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 28 オンライン申請データ管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。 ")
        self.screen_shot("オンライン申請データ管理画面_28")

        # 29 オンライン申請データ管理画面: 「戻る」ボタン押下
        self.return_click()

        # 30 オンライン申請データ検索結果一覧画面: 表示
        self.screen_shot("オンライン申請データ検索結果一覧画面_30")

        # 31 オンライン申請データ検索結果一覧画面: 「戻る」ボタン押下
        self.return_click()

        # 32 オンライン申請データ検索画面: 表示
        self.screen_shot("オンライン申請データ検索画面_32")
