import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180901(FukushiSiteTestCaseBase):
    """TestQAC050_180901"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180901"]
        sql_params = {
            "TARGET_GYOUMU_CODE": "QAC050", 
            "DELETE_ATENA_CODE": case_data.get("atena_code_1", ""),
            "TARGET_NENDO_1":"2021",
            "TARGET_NENDO_2":"2022",
            "TARGET_SOUSYOTOKU_1":"5000000",
            "TARGET_SOUSYOTOKU_2":"0"
        }
        self.exec_sqlfile("Test_QAC050_180901.sql", params=sql_params)
        sql_params = {
            "TARGET_GYOUMU_CODE": "QAC050", 
            "DELETE_ATENA_CODE": case_data.get("atena_code_2", ""),
            "TARGET_NENDO_1":"2021",
            "TARGET_NENDO_2":"2022",
            "TARGET_SOUSYOTOKU_1":"0",
            "TARGET_SOUSYOTOKU_2":"1000000"
        }
        self.exec_sqlfile("Test_QAC050_180901.sql", params=sql_params)
        sql_params = {
            "TARGET_GYOUMU_CODE": "QAC050", 
            "DELETE_ATENA_CODE": case_data.get("atena_code_3", ""),
            "TARGET_NENDO_1":"2021",
            "TARGET_NENDO_2":"2022",
            "TARGET_SOUSYOTOKU_1":"0",
            "TARGET_SOUSYOTOKU_2":"5000000"
        }
        self.exec_sqlfile("Test_QAC050_180901.sql", params=sql_params)
        sql_params = {
            "TARGET_GYOUMU_CODE": "QAC050", 
            "DELETE_ATENA_CODE": case_data.get("atena_code_4", ""),
            "TARGET_NENDO_1":"2021",
            "TARGET_NENDO_2":"2022",
            "TARGET_SOUSYOTOKU_1":"0",
            "TARGET_SOUSYOTOKU_2":"0"
        }
        self.exec_sqlfile("Test_QAC050_180901.sql", params=sql_params)
        super().setUp()

    # 支給停止届を提出した住民に対し、その他必要な情報の登録ができることを確認する。
    def test_QAC050_180901(self):
        """届出情報入力"""

        case_data = self.test_data["TestQAC050_180901"]
        atena_codes = [case_data.get("atena_code_1", ""), case_data.get("atena_code_2", ""), case_data.get("atena_code_3", ""), case_data.get("atena_code_4", "")]

        for i,atena_code in enumerate(atena_codes):
            if i == 0: atena_no = "①"
            if i == 1: atena_no = "②"
            if i == 2: atena_no = "③"
            if i == 3: atena_no = "④"
            # =====資格登録=====
            self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
            self.click_button_by_label("申請内容入力")
            self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
            self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
            self.click_button_by_label("確定")
            self.form_input_by_id(idstr="TxtShinseiYMD", value="20220401")
            self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
            self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
            self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20220325")
            self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
            self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20220501")
            self.click_button_by_label("児童追加")
            self.click_button_by_label("2")
            self.form_input_by_id(idstr="CmbZokugara", text="子")
            self.form_input_by_id(idstr="RdoDokyo1", value="1")
            self.form_input_by_id(idstr="TxtKangoYMD", value="20220325")
            self.form_input_by_id(idstr="KojiGaitou2", value="1")
            self.form_input_by_id(idstr="TxtGaitoYMD", value="20220501")
            self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
            self.form_input_by_id(idstr="TxtJiyuYMD", value="20220325")
            self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20220501")
            self.form_input_by_id(idstr="RdoShogai2", value="1")
            self.click_button_by_label("入力完了")
            self.click_button_by_label("児童追加")
            self.click_button_by_label("3")
            self.form_input_by_id(idstr="CmbZokugara", text="子")
            self.form_input_by_id(idstr="RdoDokyo1", value="1")
            self.form_input_by_id(idstr="TxtKangoYMD", value="20220325")
            self.form_input_by_id(idstr="KojiGaitou2", value="1")
            self.form_input_by_id(idstr="TxtGaitoYMD", value="20220501")
            self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
            self.form_input_by_id(idstr="TxtJiyuYMD", value="20220325")
            self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20220501")
            self.form_input_by_id(idstr="RdoShogai2", value="1")
            self.click_button_by_label("福祉世帯情報")
            self.form_input_by_id(idstr="HoninCmb_1", text="本人")
            self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
            self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20220501")
            self.form_input_by_id(idstr="ChkFlg_4", value="1")
            self.form_input_by_id(idstr="HoninCmb_2", text="子")
            self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
            self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20220501")
            self.form_input_by_id(idstr="HoninCmb_3", text="子")
            self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
            self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20220501")
            self.click_button_by_label("入力完了")
            self.click_button_by_label("所得情報")
            self.click_button_by_label("1")
            self.return_click()
            self.find_common_buttons_open_button().click()
            self.click_button_by_label("口座情報")
            self.click_button_by_label("追加")
            self.entry_kouza_info(
                start_ymd="20220501",
                ginko_code="0005",
                shiten_code="001",
                kouza_shubetsu_text="普通",
                kouza_bango="7777777",
                koukai=True
            )
            self.click_button_by_label("登録")
            self.alert_ok()
            self.return_click()
            self.form_input_by_id(idstr="TxtKaitei", value="202205")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            can_shintatsu_button = self.click_button_by_label("本庁進達入力")
            if (can_shintatsu_button):
                self.click_button_by_label("本庁進達入力")
                self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20220401")
                self.click_button_by_label("月額計算")
                self.click_button_by_label("登録")
                self.alert_ok()
                self.assert_message_area("登録しました")
                self.click_button_by_label("本庁進達結果入力")
                self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20220401")
                self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
                self.click_button_by_label("月額計算")
                self.click_button_by_label("登録")
                self.alert_ok()
                self.assert_message_area("登録しました")
            self.click_button_by_label("決定内容入力") 
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20220401")
            self.form_input_by_id(idstr="TxtKetteiYMD", value="20220401")
            self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
            self.form_input_by_id(idstr="TxtShoushoBango", value="180701")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")


            self.do_login()
            # 1 メインメニュー画面: 表示
            self.screen_shot(f'メインメニュー画面_1_{atena_no}')

            # 2 メインメニュー画面: 「申請資格管理」ボタン押下
            self.shinsei_shikaku_kanri_click()

            # 3 個人検索画面: 表示
            self.screen_shot(f"個人検索画面_3_{atena_no}")

            # 4 個人検索画面: 「住民コード」入力
            self.screen_shot(f"個人検索画面_4_{atena_no}")

            # 5 個人検索画面: 「検索」ボタン押下
            self.kojin_kensaku_by_atena_code(atena_code=atena_code)

            # 6 受給状況画面: 表示
            self.screen_shot(f"受給状況画面_6_{atena_no}")

            # 7 受給状況画面: 「児童扶養手当」ボタン押下
            self.click_button_by_label("児童扶養手当")

            # 8 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_8_{atena_no}")

            # 9 児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
            self.click_button_by_label("申請内容入力")

            # 10 児童扶養手当資格管理画面: 申請種別「支給停止事由変更」申請理由「その他」
            self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=case_data.get("shinsei_shubetsu", ""))
            self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("shinsei_riyu", ""))

            # 11 児童扶養手当資格管理画面: 「確定」ボタン押下
            self.click_button_by_label("確定")

            # 12 児童扶養手当資格管理画面: 申請年月日「20230601」事由発生日「20230531」
            self.form_input_by_id(idstr="TxtShinseiYMD", value="20230601")
            self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20230531")

            # 13 児童扶養手当資格管理画面: 児童「1」Noボタン押下
            self.click_button_by_label("1")

            # 14 支給対象児童入力画面: 表示
            self.screen_shot(f"支給対象児童入力画面_14_{atena_no}")

            # 15 支給対象児童入力画面: 「戻る」ボタン押下
            self.return_click()

            # 16 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_16_{atena_no}")

            # 17 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
            self.open_common_buttons_area()
            self.common_button_click("提出書類管理")

            # 18 提出書類管理: 表示
            self.screen_shot(f"提出書類管理_18_{atena_no}")

            # 19 提出書類管理: 「追加」ボタン押下
            self.click_button_by_label("追加")

            # 20 提出書類管理: 戸籍謄抄本にチェック
            self.form_input_by_id(idstr="ChkTeisyutsu_1", value="1")
            self.screen_shot(f"提出書類管理_20_{atena_no}")

            # 21 提出書類管理: 「入力完了」ボタン押下
            self.click_button_by_label("入力完了")

            # 22 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_22_{atena_no}")

            # 23 児童扶養手当資格管理画面: 「月額計算」ボタン押下
            self.click_button_by_label("月額計算")

            # 24 児童扶養手当資格管理画面: 「登録」ボタン押下
            self.click_button_by_label("登録")
            self.alert_ok()

            # 25 児童扶養手当資格管理画面: 表示
            # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
            self.assert_message_area("登録しました。")
            self.screen_shot(f"児童扶養手当資格管理画面_25_{atena_no}")