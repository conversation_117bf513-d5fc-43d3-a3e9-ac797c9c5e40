import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181402(FukushiSiteTestCaseBase):
    """TestQAC050_181402"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181402"]
        super().setUp()

    # 一部支給停止措置対象者に対して、お知らせ等を出力できることを確認する。
    def test_QAC050_181402(self):
        """児童扶養手当受給に関する重要なお知らせ等作成"""

        case_data = self.test_data["TestQAC050_181402"]
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        pStartPage = case_data.get("pStartPage", "")
        pEndPage = case_data.get("pEndPage", "")
        pHakkoYMD = case_data.get("pHakkoYMD", "")
        pTeishutsuStart = case_data.get("pTeishutsuStart", "")
        pTeishutsuEnd = case_data.get("pTeishutsuEnd", "")
        pSodanKigen = case_data.get("pSodanKigen", "")
        pJizenTsuchiYMD = case_data.get("pJizenTsuchiYMD", "")
        pGyomuSelect_2 = case_data.get("pGyomuSelect_2", "")
        pJigyoSelect_2 = case_data.get("pJigyoSelect_2", "")
        pShoriKubunSelect_2 = case_data.get("pShoriKubunSelect_2", "")
        pShoriBunruiSelect_2 = case_data.get("pShoriBunruiSelect_2", "")
        pStartPage_2 = case_data.get("pStartPage_2", "")
        pEndPage_2 = case_data.get("pEndPage_2", "")
        pHakkoYMD_2 = case_data.get("pHakkoYMD_2", "")
        pTeishutsuStart_2 = case_data.get("pTeishutsuStart_2", "")
        pTeishutsuEnd_2 = case_data.get("pTeishutsuEnd_2", "")
        pSodanKigen_2 = case_data.get("pSodanKigen_2", "")
        pNendo = case_data.get("pNendo", "")
        pSentakuJizenYMD = case_data.get("pSentakuJizenYMD", "")
        
        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：13条の3関連処理分類：月次
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect)

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「事前通知対象者_お知らせ_月次」のNoボタン押下
        self.click_batch_job_button_by_label("事前通知対象者_お知らせ_月次")

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: 開始ページ「000000」終了ページ「999999」発行年月日「20230702」提出期限開始「20230801」提出期限終了「20230831」相談期限「20230815」
        params = [
            {"title": "開始ページ", "type": "text", "value": pStartPage},
            {"title": "終了ページ", "type": "text", "value": pEndPage},
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD},
            {"title": "提出期限開始", "type": "text", "value": pTeishutsuStart},
            {"title": "提出期限終了", "type": "text", "value": pTeishutsuEnd},
            {"title": "相談期限", "type": "text", "value": pSodanKigen}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="一部支給停止適用除外事由届出書_18")

        # 19 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「児童扶養手当の受給に関する重要なお知らせ」のNoボタン押下

        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 22 児童扶養手当の受給に関する重要なお知らせ（PDF）: 表示
        # self.screen_shot("児童扶養手当の受給に関する重要なお知らせ（PDF）_22")

        # 23 児童扶養手当の受給に関する重要なお知らせ（PDF）: 2ページ目表示
        # Assert: 中面が出力されていることを確認する
        # self.screen_shot("児童扶養手当の受給に関する重要なお知らせ（PDF）_23")

        # 24 児童扶養手当の受給に関する重要なお知らせ（PDF）: 3ページ目表示
        # Assert: 裏面が出力されていることを確認する
        # self.screen_shot("児童扶養手当の受給に関する重要なお知らせ（PDF）_24")

        # 25 児童扶養手当の受給に関する重要なお知らせ（PDF）: ×ボタン押下でPDFを閉じる

        # 26 ジョブ帳票履歴画面: 「一部支給停止適用除外事由届出書」のNoボタン押下

        # 27 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 28 一部支給停止適用除外事由届出書（PDF）: 表示
        # self.screen_shot("一部支給停止適用除外事由届出書（PDF）_28")

        # 29 一部支給停止適用除外事由届出書（PDF）: ×ボタン押下でPDFを閉じる

        # 30 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_30")

        # 31 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 32 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_32")

        # 33 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 34 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_34")

        # 35 バッチ起動画面: 「事前通知対象者_更新_月次」のNoボタン押下
        self.click_batch_job_button_by_label("事前通知対象者_更新_月次")

        # 36 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_36")

        # 37 バッチ起動画面: 事前通知発送日「20230702」
        params = [
            {"title": "事前通知発送日", "type": "text", "value": pJizenTsuchiYMD}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_37")

        # 38 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 39 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_39")

        # 40 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 41 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_41")

        # 42 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 43 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_43")

        # 44 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_44")

        # 45 ジョブ実行履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 46 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_46")

        # 47 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 48 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_48")

        # 49 バッチ起動画面:
        # 業務：児童
        # 事業：児童扶養手当
        # 処理区分：13条の3関連
        # 処理分類：年次
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect_2)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect_2)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect_2)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect_2)

        # 50 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_50")

        # 51 バッチ起動画面: 「事前通知対象者_お知らせ_年次」のNoボタン押下
        self.click_batch_job_button_by_label("事前通知対象者_お知らせ_年次")

        # 52 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_52")

        # 53 バッチ起動画面: 開始ページ「000000」終了ページ「999999」発行年月日「20230702」提出期限開始「20230801」提出期限終了「20230831」相談期限「20230815」
        # NG no ID
        params = [
            {"title": "開始ページ", "type": "text", "value": pStartPage_2},
            {"title": "終了ページ", "type": "text", "value": pEndPage_2},
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD_2},
            {"title": "提出期限開始", "type": "text", "value": pTeishutsuStart_2},
            {"title": "提出期限終了", "type": "text", "value": pTeishutsuEnd_2},
            {"title": "相談期限", "type": "text", "value": pSodanKigen_2}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_53")

        # 54 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 55 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_55")

        # 56 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 57 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_57")

        # 58 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 59 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_59")

        # 60 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_60")

        # 61 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 62 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_62")

        # 63 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="一部支給停止適用除外事由届出書_63")

        # 64 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_64")

        # 65 ジョブ帳票履歴画面: 「児童扶養手当の受給に関する重要なお知らせ」のNoボタン押下

        # 66 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 67 児童扶養手当の受給に関する重要なお知らせ（PDF）: 表示
        # self.screen_shot("児童扶養手当の受給に関する重要なお知らせ（PDF）_67")

        # 68 児童扶養手当の受給に関する重要なお知らせ（PDF）: 2ページ目表示
        # Assert: 中面が出力されていることを確認する
        # self.screen_shot("児童扶養手当の受給に関する重要なお知らせ（PDF）_68")

        # 69 児童扶養手当の受給に関する重要なお知らせ（PDF）: 3ページ目表示
        # Assert: 裏面が出力されていることを確認する
        # self.screen_shot("児童扶養手当の受給に関する重要なお知らせ（PDF）_69")

        # 70 児童扶養手当の受給に関する重要なお知らせ（PDF）: ×ボタン押下でPDFを閉じる

        # 71 ジョブ帳票履歴画面: 「一部支給停止適用除外事由届出書」のNoボタン押下

        # 72 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 73 一部支給停止適用除外事由届出書（PDF）: 表示
        # self.screen_shot("一部支給停止適用除外事由届出書（PDF）_73")

        # 74 一部支給停止適用除外事由届出書（PDF）: ×ボタン押下でPDFを閉じる

        # 75 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_75")

        # 76 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 77 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_77")

        # 78 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 79 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_79")

        # 80 バッチ起動画面: 「事前通知対象者_更新_年次」のNoボタン押下
        self.click_batch_job_button_by_label("事前通知対象者_更新_年次")

        # 81 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_81")

        # 82 バッチ起動画面: 年度「令和５年」選択事前通知発送日「20230702」
        params = [
            {"title": "年度", "type": "select", "value": pNendo},
            {"title": "事前通知発送日", "type": "text", "value": pSentakuJizenYMD},
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_82")

        # 83 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 84 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_84")

        # 85 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 86 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_86")

        # 87 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 88 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_88")

        # 89 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_89")
