import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180305(FukushiSiteTestCaseBase):
    """TestQAC050_180305"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180305"]
        super().setUp()

    # 提出書類記載内容や住記情報、年金情報等を確認する。
    def test_QAC050_180305(self):
        """額改定審査"""

        case_data = self.test_data["TestQAC050_180305"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「進捗管理」ボタン押下
        self.click_button_by_label("進捗管理")

        # 3 進捗管理対象者検索画面: 表示
        self.screen_shot("進捗管理対象者検索画面_3")

        # 4 進捗管理対象者検索画面: 業務「児童」選択事業「児童扶養手当」選択
        self.form_input_by_id(idstr="Gyomu", text="児童")
        self.form_input_by_id(idstr="Jigyo", text="児童扶養手当")
        self.screen_shot("進捗管理対象者検索画面_4")

        # 5 進捗管理対象者検索画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 6 進捗管理対象者検索画面: 表示
        self.screen_shot("進捗管理対象者検索画面_6")

        # 7 進捗管理対象者検索画面: 申請年月日「20230401」申請種別「額改定（増）」選択
        self.form_input_by_id(idstr="ShinseiKaishiYMD", value="20230401")
        self.form_input_by_id(idstr="CmbPreShinchoku", text="額改定（増額）")
        self.screen_shot("進捗管理対象者検索画面_7")

        # 8 進捗管理対象者検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 9 進捗管理対象者一覧画面: 表示
        self.screen_shot("進捗管理対象者一覧画面_9")

        # 10 進捗管理対象者一覧画面: 対象者一覧「1」Noボタン押下
        self.click_button_by_label("1")

        # 11 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_11")

        # 12 児童扶養手当資格管理画面: 「住記情報」ボタン押下
        self.click_button_by_label("住記情報")

        # 13 世帯一覧画面: 表示
        self.screen_shot("世帯一覧画面_13")

        # 14 世帯一覧画面: 世帯一覧「1」Noボタン押下
        self.click_button_by_label("1")

        # 15 住記情報: 表示
        self.screen_shot("住記情報_15")

        # 16 住記情報: 「戻る」ボタン押下
        self.return_click()

        # 17 世帯一覧画面: 表示
        self.screen_shot("世帯一覧画面_17")

        # 18 世帯一覧画面: 「戻る」ボタン押下
        self.return_click()

        # 19 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_19")

        # 20 児童扶養手当資格管理画面: 「年金情報登録」ボタン押下
        self.click_button_by_label("年金情報登録")

        # 21 年金情報: 表示
        self.screen_shot("年金情報_21")

        # 22 年金情報: 「戻る」ボタン押下
        self.return_click()

        # 23 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_23")

        # 24 児童扶養手当資格管理画面: 「戻る」ボタン押下
        self.return_click()

        # 25 進捗管理対象者一覧画面: 表示
        self.screen_shot("進捗管理対象者一覧画面_25")

        # 26 進捗管理対象者一覧画面: 「戻る」ボタン押下
        self.return_click()

        # 27 進捗管理対象者検索画面: 表示
        self.screen_shot("進捗管理対象者検索画面_27")
