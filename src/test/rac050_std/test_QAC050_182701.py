import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_182701(FukushiSiteTestCaseBase):
    """TestQAC050_182701"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 番号管理連携システムへ登録できることを確認する。
    def test_QAC050_182701(self):
        """副本登録"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：副本登録処理分類：特定個人情報の管理番号１６
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="副本登録")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="特定個人情報の管理番号１６")

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「児童扶養手当_所得照会抽出」のNoボタン押下
        self.click_batch_job_button_by_label("児童扶養手当_所得照会抽出")

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_8")

        # 8 バッチ起動画面: パラメータ指定なし
        self.screen_shot("バッチ起動画面_9")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 17 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_17")

        # 18 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 19 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_19")

        # 20 バッチ起動画面: 「副本データ整合性確認処理」のNoボタン押下
        self.click_batch_job_button_by_label("副本データ整合性確認処理")

        # 21 バッチ起動画面: パラメータ指定なし
        self.screen_shot("バッチ起動画面_21")

        # 22 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 23 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 24 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_24")

        # 25 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 26 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_26")

        # 27 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_27")
