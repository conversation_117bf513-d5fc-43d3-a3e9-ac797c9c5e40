import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181810(FukushiSiteTestCaseBase):
    """TestQAC050_181810"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181810"]
        super().setUp()

    # 内払い決定通知書を出力できることを確認する。
    def test_QAC050_181810(self):
        """内払調整結果通知書作成"""

        case_data = self.test_data["TestQAC050_181810"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.do_login()
        # 1 児童扶養手当資格管理画面: 「支払調整履歴」ボタン押下
        self.click_button_by_label("支払調整履歴")

        # 2 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_2")

        # 3 支払調整履歴画面: 該当者一覧 「1」Noボタン押下
        self.click_button_by_label("1")  # Instead of self.click_button_by_label("該当者一覧 1No")

        # 4 支払調整履歴画面: 過払年月日「20240228」発行年月日「20240302」
        # TODO Item with ID: No1のため、NG
        self.form_input_by_id(idstr="TxtHakkouYMD", value="20240302") # Item with ID: NG
        self.form_input_by_id(idstr="TxtKabaraiYMD", value="20240228") # Item with ID: NG
        self.screen_shot("支払調整履歴画面_4")

        # 5 支払調整履歴画面: 「印刷」ボタン押下
        self.pdf_output_and_download_no_alert(button_id="CmdInnsatuBTN",case_name="内払い決定通知書")

        # 6 支払調整履歴画面: 内払い決定通知書「ファイルを開く(O)」ボタンを押下

        # 7 内払い決定通知書（PDF）: 表示
        self.screen_shot("内払い決定通知書（PDF）_7")

        # 8 内払い決定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 9 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_9")

        # 10 支払調整履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 11 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_11")
