import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181004(FukushiSiteTestCaseBase):
    """TestQAC050_181004"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181004"]
        super().setUp()

    # 不足書類の提出日の登録ができることを確認する。
    def test_QAC050_181004(self):
        """受付情報入力"""

        case_data = self.test_data["TestQAC050_181004"]
        atena_code_1 = case_data.get("atena_code_1", "")
        atena_code_2 = case_data.get("atena_code_2", "")
        atena_code_3 = case_data.get("atena_code_3", "")
        chkTeisyutsu_1 = case_data.get("chkTeisyutsu_1", "")
        txtSyoruiYMD_1 = case_data.get("txtSyoruiYMD_1", "")
        chkFubi_1 = case_data.get("chkFubi_1", "")
        chkTeisyutsu_3 = case_data.get("chkTeisyutsu_3", "")
        txtSyoruiYMD_3 = case_data.get("txtSyoruiYMD_3", "")
        chkFubi_3 = case_data.get("chkFubi_3", "")
        
# ①書類不備なし、併給認定で「全部支給」になる場合 -s
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code_1, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="提出書類管理")

        # 3 提出書類管理: 表示
        self.screen_shot("1_提出書類管理_3")

        # 4 提出書類管理: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 5 提出書類管理: 戸籍謄抄本の未提出にチェックオン 戸籍謄抄本の提出日「20230702」 戸籍謄抄本の不備にチェックオフ 所得証明書の未提出にチェックオン 所得証明書の提出日「20230702」 所得証明書の不備にチェックオフ
        self.form_input_by_id(idstr="ChkTeisyutsu_1", value=chkTeisyutsu_1)
        self.form_input_by_id(idstr="TxtSyoruiYMD_1", value=txtSyoruiYMD_1)
        self.form_input_by_id(idstr="ChkFubi_1", value=chkFubi_1)
        self.form_input_by_id(idstr="ChkTeisyutsu_3", value=chkTeisyutsu_3)
        self.form_input_by_id(idstr="TxtSyoruiYMD_3", value=txtSyoruiYMD_3)
        self.form_input_by_id(idstr="ChkFubi_3", value=chkFubi_3)
        self.screen_shot("1_提出書類管理_5")

        # 6 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 7 児童扶養手当資格管理画面: 表示
        self.screen_shot("1_児童扶養手当資格管理画面_7")

        # 8 児童扶養手当資格管理画面: 児童のNoボタン押下
        self.click_by_id("CmdNo1")

        # 9 支給対象児童入力画面: 表示
        self.screen_shot("1_支給対象児童入力画面_9")

        # 10 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 11 児童扶養手当資格管理画面: 表示
        self.screen_shot("1_児童扶養手当資格管理画面_11")

        # 12 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 13 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 14 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("1_児童扶養手当資格管理画面_14")
# ①書類不備なし、併給認定で「全部支給」になる場合 -e

# ②書類不備なし、併給認定で「一部支給」になる場合 -s
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code_2, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="提出書類管理")

        # 3 提出書類管理: 表示
        self.screen_shot("2_提出書類管理_3")

        # 4 提出書類管理: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 5 提出書類管理: 戸籍謄抄本の未提出にチェックオン 戸籍謄抄本の提出日「20230702」 戸籍謄抄本の不備にチェックオフ 所得証明書の未提出にチェックオン 所得証明書の提出日「20230702」 所得証明書の不備にチェックオフ
        self.form_input_by_id(idstr="ChkTeisyutsu_1", value=chkTeisyutsu_1)
        self.form_input_by_id(idstr="TxtSyoruiYMD_1", value=txtSyoruiYMD_1)
        self.form_input_by_id(idstr="ChkFubi_1", value=chkFubi_1)
        self.form_input_by_id(idstr="ChkTeisyutsu_3", value=chkTeisyutsu_3)
        self.form_input_by_id(idstr="TxtSyoruiYMD_3", value=txtSyoruiYMD_3)
        self.form_input_by_id(idstr="ChkFubi_3", value=chkFubi_3)
        self.screen_shot("2_提出書類管理_5")

        # 6 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 7 児童扶養手当資格管理画面: 表示
        self.screen_shot("2_児童扶養手当資格管理画面_7")

        # 8 児童扶養手当資格管理画面: 児童のNoボタン押下
        self.click_by_id("CmdNo1")

        # 9 支給対象児童入力画面: 表示
        self.screen_shot("2_支給対象児童入力画面_9")

        # 10 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 11 児童扶養手当資格管理画面: 表示
        self.screen_shot("2_児童扶養手当資格管理画面_11")

        # 12 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 13 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 14 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("2_児童扶養手当資格管理画面_14")
# ②書類不備なし、併給認定で「一部支給」になる場合 -e

# ③書類不備なし、併給認定で「全部停止」になる場合 -s
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code_3, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="提出書類管理")

        # 3 提出書類管理: 表示
        self.screen_shot("3_提出書類管理_3")

        # 4 提出書類管理: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 5 提出書類管理: 戸籍謄抄本の未提出にチェックオン 戸籍謄抄本の提出日「20230702」 戸籍謄抄本の不備にチェックオフ 所得証明書の未提出にチェックオン 所得証明書の提出日「20230702」 所得証明書の不備にチェックオフ
        self.form_input_by_id(idstr="ChkTeisyutsu_1", value=chkTeisyutsu_1)
        self.form_input_by_id(idstr="TxtSyoruiYMD_1", value=txtSyoruiYMD_1)
        self.form_input_by_id(idstr="ChkFubi_1", value=chkFubi_1)
        self.form_input_by_id(idstr="ChkTeisyutsu_3", value=chkTeisyutsu_3)
        self.form_input_by_id(idstr="TxtSyoruiYMD_3", value=txtSyoruiYMD_3)
        self.form_input_by_id(idstr="ChkFubi_3", value=chkFubi_3)
        self.screen_shot("3_提出書類管理_5")

        # 6 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 7 児童扶養手当資格管理画面: 表示
        self.screen_shot("3_児童扶養手当資格管理画面_7")

        # 8 児童扶養手当資格管理画面: 児童のNoボタン押下
        self.click_by_id("CmdNo1")

        # 9 支給対象児童入力画面: 表示
        self.screen_shot("3_支給対象児童入力画面_9")

        # 10 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 11 児童扶養手当資格管理画面: 表示
        self.screen_shot("3_児童扶養手当資格管理画面_11")

        # 12 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 13 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 14 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("3_児童扶養手当資格管理画面_14")
# ③書類不備なし、併給認定で「全部停止」になる場合 -e