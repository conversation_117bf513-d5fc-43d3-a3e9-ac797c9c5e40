import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181404(FukushiSiteTestCaseBase):
    """TestQAC050_181404"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181404"]
        super().setUp()

    # 一部支給停止適用除外事由届未提出者に対してお知らせ等を出力できるか確認する。
    def test_QAC050_181404(self):
        """児童扶養手当受給に関する重要なお知らせ作成"""

        case_data = self.test_data["TestQAC050_181404"]
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        pStartPage = case_data.get("pStartPage", "")
        pEndPage = case_data.get("pEndPage", "")
        pHakkoYMD = case_data.get("pHakkoYMD", "")
        pIchiranShubetsu = case_data.get("pIchiranShubetsu", "")
        pTeishutsuKigen = case_data.get("pTeishutsuKigen", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：13条の3関連処理分類：除外届未提出者
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect)

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「除外届未提出者_お知らせ」のNoボタン押下
        self.click_batch_job_button_by_label("除外届未提出者_お知らせ")

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面:
        # 開始ページ「000000」
        # 終了ページ「999999」
        # 発行年月日「20230702」
        # 一覧種別「1」
        # 提出期限「20230831」
        params = [
            {"title": "開始ページ", "type": "text", "value": pStartPage},
            {"title": "終了ページ", "type": "text", "value": pEndPage},
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD},
            {"title": "一覧種別", "type": "text", "value": pIchiranShubetsu},
            {"title": "提出期限終了", "type": "text", "value": pTeishutsuKigen}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 19 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「児童扶養手当の受給に関する重要なお知らせ」のNoボタン押下

        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 22 児童扶養手当の受給に関する重要なお知らせ（PDF）: 表示
        # self.screen_shot("児童扶養手当の受給に関する重要なお知らせ（PDF）_22")

        # 23 児童扶養手当の受給に関する重要なお知らせ（PDF）: 2ページ目表示
        # Assert: 中面が出力されていることを確認する
        # self.screen_shot("児童扶養手当の受給に関する重要なお知らせ（PDF）_23")

        # 24 児童扶養手当の受給に関する重要なお知らせ（PDF）: 3ページ目表示
        # Assert: 裏面が出力されていることを確認する
        # self.screen_shot("児童扶養手当の受給に関する重要なお知らせ（PDF）_24")

        # 25 児童扶養手当の受給に関する重要なお知らせ（PDF）: ×ボタン押下でPDFを閉じる

        # 26 ジョブ帳票履歴画面: 「一部支給停止適用除外事由届出書」のNoボタン押下

        # 27 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 28 一部支給停止適用除外事由届出書（PDF）: 表示
        # self.screen_shot("一部支給停止適用除外事由届出書（PDF）_28")

        # 29 一部支給停止適用除外事由届出書（PDF）: ×ボタン押下でPDFを閉じる

        # 30 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_30")
