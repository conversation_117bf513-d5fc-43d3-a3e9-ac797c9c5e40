import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181324(FukushiSiteTestCaseBase):
    """TestQAC050_181324"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 内払い決定通知書を出力できることを確認する。
    def test_QAC050_181324(self):
        """内払調整結果通知書作成"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.do_login()
        # 2 児童扶養手当資格管理画面: 「支払調整履歴」ボタン押下
        self.click_button_by_label("支払調整履歴")

        # 3 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_3")

        # 4 支払調整履歴画面: 該当者一覧 「1」Noボタン押下
        self.click_button_by_label("1")

        # 5 支払調整履歴画面: 過払年月日「20211101」発行年月日「20231102」
        # Item with ID: NG
        self.screen_shot("支払調整履歴画面_5")

        # 6 支払調整履歴画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 7 支払調整履歴画面: 内払い決定通知書「ファイルを開く(O)」ボタンを押下

        # 8 内払い決定通知書（PDF）: 表示
        self.screen_shot("内払い決定通知書（PDF）_8")

        # 9 内払い決定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 10 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_10")

        # 11 支払調整履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 12 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_12")
