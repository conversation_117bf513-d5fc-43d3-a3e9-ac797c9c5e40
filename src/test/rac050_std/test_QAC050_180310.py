import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180310(FukushiSiteTestCaseBase):
    """TestQAC050_180310"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180310"]
        super().setUp()

    # 額改定決定通知書、児童扶養手当証書を出力できることを確認する。
    def test_QAC050_180310(self):
        """手当額改定通知書等作成"""

        case_data = self.test_data["TestQAC050_180310"]
        atena_code = case_data.get("atena_code", "")
        kettei_ymd_start = case_data.get("kettei_ymd_start", "")
        kettei_ymd_end = case_data.get("kettei_ymd_end", "")
        hakko_ymd = case_data.get("hakko_ymd", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_2")

        # 3 帳票印刷画面: 「額改定決定通知書」行の印刷チェックボックス選択「額改定決定通知書」行の発行年月日チェックボックス選択「額改定決定通知書」行の発行年月日「20230602」「児童扶養手当証書」行の印刷チェックボックス選択「児童扶養手当証書」行の発行年月日チェックボックス選択「児童扶養手当証書」行の発行年月日「20230602」
        exec_params = [
            {
                "report_name": "額改定決定通知書",
                "params":[
                    {"title": "発行年月日", "value": hakkou_ymd},
                ]
            },
            {
                "report_name": "児童扶養手当証書",
                "params":[
                    {"title": "発行年月日", "value": hakkou_ymd},
                ]
            }
        ]
        exec_count = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_3")

        # 4 帳票印刷画面: 「印刷」ボタン押下

        # 5 帳票印刷画面: 額改定決定通知書「ファイルを開く(O)」ボタンを押下

        # 6 額改定決定通知書（PDF）: 表示
        self.screen_shot("額改定決定通知書（PDF）_6")

        # 7 額改定決定通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        self.screen_shot("額改定決定通知書（PDF）_7")

        # 8 額改定決定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 9 帳票印刷画面: 児童扶養手当証書「ファイルを開く(O)」ボタンを押下

        # 10 児童扶養手当証書（PDF）: 表示
        self.screen_shot("児童扶養手当証書（PDF）_10")

        # 11 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        self.screen_shot("児童扶養手当証書（PDF）_11")

        # 12 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # 13 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 14 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_14")

        # 15 メインメニュー画面: 表示
        self.return_click()
        self.return_click()
        self.return_click()
        self.screen_shot("メインメニュー画面_15")

        # 16 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 17 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_17")

        # 18 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：通知書一括処理
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="通知書一括処理")

        # 19 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_19")

        # 20 バッチ起動画面: 「児童扶養手当証書受領書出力」のNoボタン押下
#        self.click_batch_job_button_by_label("児童扶養手当証書受領書出力")
#
#        # 21 バッチ起動画面: 表示
#        self.screen_shot("バッチ起動画面_21")
#
#        # 22 バッチ起動画面: 開始決定日「20230602」終了決定日「20230602」宛名コード「」出力順「証書番号順」選択通知書区分「2」発行年月日「20230602」
#        params = [
#            {"title": "開始決定日", "type": "text", "value": kettei_ymd_start},
#            {"title": "終了決定日", "type": "text", "value": kettei_ymd_end},
#            {"title": "宛名コード", "type": "text", "value": ""},
#            {"title": "出力順", "type": "select", "value": "証書番号順"},
#            {"title": "通知書区分", "type": "select", "value": case_data.get("noti_category_2", "")},
#            {"title": "発行年月日", "type": "text", "value": hakko_ymd}
#        ]
#        self.set_job_params(params)
#        self.screen_shot("バッチ起動画面_22")
#
#        # 23 バッチ起動画面: 「処理開始」ボタン押下
#        exec_datetime = self.exec_batch_job()
#
#        # 24 バッチ起動画面: 表示
#        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
#        self.assert_message_base_header("ジョブを起動しました")
#        self.screen_shot("バッチ起動画面_24")
#
#        # 25 バッチ起動画面: 「実行履歴」ボタン押下
#        self.click_job_exec_log_search()
#
#        # 26 ジョブ実行履歴画面: 表示
#        self.screen_shot("ジョブ実行履歴画面_26")
#
#        # 27 ジョブ実行履歴画面: 「検索」ボタン押下
#        self.wait_job_finished(120,20)
#        self.assert_job_normal_end(exec_datetime=exec_datetime)
#
#        # 28 ジョブ実行履歴画面: 表示
#        self.screen_shot("ジョブ実行履歴画面_28")
#
#        # 29 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
#        self.screen_shot("ジョブ実行履歴画面_29")
#
#        # 30 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
#        self.click_report_log()
#
#        # 31 ジョブ帳票履歴画面: 表示
#        self.screen_shot("ジョブ帳票履歴画面_31")
#
#        # 32 ジョブ帳票履歴画面: 「検索」ボタン押下
#        self.get_job_report_pdf(exec_datetime=exec_datetime)
#
#        # 33 ジョブ帳票履歴画面: 表示
#        self.screen_shot("ジョブ帳票履歴画面_33")
#
#        # 34 ジョブ帳票履歴画面: 「児童扶養手当証書受領書」のNoボタン押下
#        self.click_button_by_label("児童扶養手当証書受領書")
#
#        # 35 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
#
#        # 36 児童扶養手当証書受領書（PDF）: 表示
#        self.screen_shot("児童扶養手当証書受領書（PDF）_36")
#
#        # 37 児童扶養手当証書受領書（PDF）: ×ボタン押下でPDFを閉じる
#        self.screen_shot("児童扶養手当証書受領書（PDF）_37")
#
#        # 38 ジョブ帳票履歴画面: 表示
#        self.screen_shot("ジョブ帳票履歴画面_38")
#
#        # 39 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
#        self.click_job_list()
#
#        # 40 バッチ起動画面: 表示
#        self.screen_shot("バッチ起動画面_40")
#
#        # 41 バッチ起動画面: 「処理一覧」ボタン押下
#        self.click_job_exec_log_search()
#
#        # 42 バッチ起動画面: 表示
#        self.screen_shot("バッチ起動画面_42")

        # 43 バッチ起動画面: 「児童扶養手当証書一括出力」のNoボタン押下
        self.click_batch_job_button_by_label("児童扶養手当証書一括出力")

        # 44 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_44")

        # 45 バッチ起動画面: 開始決定日「20230602」終了決定日「20230602」宛名コード「」出力順「証書番号順」選択通知書区分「2」発行年月日「20230602」
        params = [
            {"title": "開始決定日", "type": "text", "value": kettei_ymd_start},
            {"title": "終了決定日", "type": "text", "value": kettei_ymd_end},
            {"title": "宛名コード", "type": "text", "value": "2"},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "通知書区分", "type": "text", "value": case_data.get("noti_category_2", "")},
            {"title": "発行年月日", "type": "text", "value": hakko_ymd}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_45")

        # 46 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 47 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_47")

        # 48 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 49 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_49")

        # 50 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(320,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 51 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_51")

        # 52 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_52")

        # 53 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 54 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_54")

        # 55 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 56 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_56")

        # 57 ジョブ帳票履歴画面: 「児童扶養手当証書」のNoボタン押下
        self.click_button_by_label("児童扶養手当証書")

        # 58 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 59 児童扶養手当証書（PDF）: 表示
        self.screen_shot("児童扶養手当証書（PDF）_59")

        # 60 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        self.screen_shot("児童扶養手当証書（PDF）_60")

        # 61 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # 62 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_62")

        # 63 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 64 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_64")

        # 65 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 66 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_66")

        # 67 バッチ起動画面: 「決定一括出力処理_一覧」のNoボタン押下
        self.click_batch_job_button_by_label("決定一括出力処理_一覧")

        # 68 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_68")

        # 69 バッチ起動画面: 開始決定日「20230602」終了決定日「20230602」宛名コード「」出力順「証書番号順」選択通知書区分「3」発行年月日「20230602」
        params = [
            {"title": "開始決定日", "type": "text", "value": kettei_ymd_start},
            {"title": "終了決定日", "type": "text", "value": kettei_ymd_end},
            {"title": "宛名コード", "type": "text", "value": ""},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "通知書区分", "type": "text", "value": case_data.get("noti_category_3", "")},
            {"title": "発行年月日", "type": "text", "value": hakko_ymd}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_69")

        # 70 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 71 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_71")

        # 72 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 73 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_73")

        # 74 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 75 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_75")

        # 76 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_76")

        # 77 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 78 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_78")

        # 79 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 80 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_80")

        # 81 ジョブ帳票履歴画面: 「通知書対象者一覧」のNoボタン押下
        self.click_button_by_label("通知書対象者一覧")

        # 82 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 83 通知書対象者一覧（PDF）: 表示
        self.screen_shot("通知書対象者一覧（PDF）_83")

        # 84 通知書対象者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 85 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_85")

        # 86 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 87 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_87")

        # 88 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 89 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_89")

        # 90 バッチ起動画面: 「決定一括出力処理_通知書」のNoボタン押下
        self.click_batch_job_button_by_label("決定一括出力処理_通知書")

        # 91 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_91")

        # 92 バッチ起動画面: 開始決定日「20230602」終了決定日「20230602」宛名コード「」出力順「証書番号順」選択通知書区分「3」発行年月日「20230602」
        params = [
            {"title": "開始決定日", "type": "text", "value": kettei_ymd_start},
            {"title": "終了決定日", "type": "text", "value": kettei_ymd_end},
            {"title": "宛名コード", "type": "text", "value": ""},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "通知書区分", "type": "text", "value": case_data.get("noti_category_3", "")},
            {"title": "発行年月日", "type": "text", "value": hakko_ymd}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_92")

        # 93 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 94 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_94")

        # 95 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 96 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_96")

        # 97 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 98 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_98")

        # 99 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_99")

        # 100 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 101 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_101")

        # 102 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 103 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_103")

        # 104 ジョブ帳票履歴画面: 「額改定決定通知書」のNoボタン押下
        self.click_button_by_label("額改定決定通知書")

        # 105 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 106 額改定決定通知書（PDF）: 表示
        self.screen_shot("額改定決定通知書（PDF）_106")

        # 107 額改定決定通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        self.screen_shot("額改定決定通知書（PDF）_107")

        # 108 額改定決定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 109 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_109")
