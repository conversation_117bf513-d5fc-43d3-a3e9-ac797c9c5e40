import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181606(FukushiSiteTestCaseBase):
    """TestQAC050_181606"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181606"]
        sql_params = {
            "TARGET_GYOUMU_CODE": "QAC050", 
            "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
            "TARGET_NENDO":"2022"
        }
        self.exec_sqlfile("Test_QAC050_181606.sql", params=sql_params)
        super().setUp()

    # 内払い決定通知書を出力できることを確認する。
    def test_QAC050_181606(self):
        """内払調整結果通知書作成"""

        case_data = self.test_data["TestQAC050_181606"]
        atena_code = case_data.get("atena_code", "")
        shinsei_shubetsu = case_data.get("shinsei_shubetsu", "")
        shinsei_shubetsu2 = case_data.get("shinsei_shubetsu2", "")
        shinsei_riyu = case_data.get("shinsei_riyu", "")
        shinsei_riyu2 = case_data.get("shinsei_riyu2", "")

        # =====資格登録=====
#        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
#        self.click_button_by_label("申請内容入力")
#        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
#        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
#        self.click_button_by_label("確定")
#        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
#        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
#        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
#        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230325")
#        self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
#        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20220501")
#        self.click_button_by_label("児童追加")
#        self.click_button_by_label("2")
#        self.form_input_by_id(idstr="CmbZokugara", text="子")
#        self.form_input_by_id(idstr="RdoDokyo1", value="1")
#        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
#        self.form_input_by_id(idstr="KojiGaitou2", value="1")
#        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
#        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
#        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
#        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
#        self.form_input_by_id(idstr="RdoShogai2", value="1")
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("児童追加")
#        self.click_button_by_label("3")
#        self.form_input_by_id(idstr="CmbZokugara", text="子")
#        self.form_input_by_id(idstr="RdoDokyo1", value="1")
#        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
#        self.form_input_by_id(idstr="KojiGaitou2", value="1")
#        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
#        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
#        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
#        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
#        self.form_input_by_id(idstr="RdoShogai2", value="1")
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("福祉世帯情報")
#        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
#        self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
#        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230501")
#        self.form_input_by_id(idstr="ChkFlg_4", value="1")
#        self.form_input_by_id(idstr="HoninCmb_2", text="子")
#        self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
#        self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20230501")
#        self.form_input_by_id(idstr="HoninCmb_3", text="子")
#        self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
#        self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20230501")
#        self.click_button_by_label("入力完了")
#        self.form_input_by_id(idstr="TxtKaitei", value="202305")
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
#        if (can_shintatsu_button):
#            self.click_button_by_label("本庁進達入力")
#            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230401")
#            self.click_button_by_label("月額計算")
#            self.click_button_by_label("登録")
#            self.alert_ok()
#            self.assert_message_area("登録しました")
#            self.click_button_by_label("本庁進達結果入力")
#            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
#            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
#            self.click_button_by_label("月額計算")
#            self.click_button_by_label("登録")
#            self.alert_ok()
#            self.assert_message_area("登録しました")
#        self.click_button_by_label("決定内容入力") 
#        self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
#        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230401")
#        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
#        self.form_input_by_id(idstr="TxtShoushoBango", value="18160601")
#
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました")
#
#        # =====過払情報登録=====
#        self.click_button_by_label("申請内容入力")
#        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=case_data.get("shinsei_shubetsu2", ""))
#        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("shinsei_riyu2", ""))
#        self.click_button_by_label("確定")
#        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230501")
#        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230429")
#        self.form_input_by_id(idstr="TxtKaitei", value="202306")
#        self.click_by_id("CmdNo2")
#        self.form_input_by_id(idstr="TxtHiGaitoYMD", value="20230531")
#        self.form_input_by_id(idstr="CmbHiGaitoJiyu", text="施設入所")
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("福祉世帯情報")
#        self.form_input_by_id(idstr="HiGaitoYMDtxt_3", value="20230531")
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
#        if (can_shintatsu_button):
#            self.click_button_by_label("本庁進達入力")
#            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230501")
#            self.click_button_by_label("月額計算")
#            self.click_button_by_label("登録")
#            self.alert_ok()
#            self.assert_message_area("登録しました")
#            self.click_button_by_label("本庁進達結果入力")
#            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230501")
#            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
#            self.click_button_by_label("月額計算")
#            self.click_button_by_label("登録")
#            self.alert_ok()
#            self.assert_message_area("登録しました")
#        self.click_button_by_label("決定内容入力") 
#        self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230501")
#        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230501")
#        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
#        self.form_input_by_id(idstr="TxtShoushoBango", value="18160602")
#
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「支払調整」ボタン押下
        self.shiharai_chousei_click()  # Button with ID: CmdProcess7_1 instead of self.click_button_by_label("支払調整")

        # 3 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_3")

        # 4 支払調整履歴画面: 事業「児童扶養手当」選択絞り込み条件「調整中のみ」チェック
        self.form_input_by_id(idstr="CmbGyomu", text="児童扶養手当")
        self.form_input_by_id(idstr="RadioC", value="1")
        self.screen_shot("支払調整履歴画面_4")

        # 5 支払調整履歴画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 6 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_6")

        # 7 支払調整履歴画面: 該当者一覧 「15」Noボタン押下
        self.click_button_by_label("15")  # Instead of self.click_button_by_label("該当者一覧 1No")

        # 8 支払調整登録画面: 表示

        # 9 支払調整登録画面: 過払年月日「20230110」発行年月日「20230502」
        # TODO 過払年月日,発行年月日が見当たりません
        self.form_input_by_id(idstr="TxtKabaraiYMD", value="20230110") # Button with ID: NG
        self.form_input_by_id(idstr="TxtHakkouYMD", value="20230502") # Button with ID: NG
        self.screen_shot("支払調整登録画面_9")

        # 10 支払調整登録画面: 「印刷」ボタン押下
        self.pdf_output_and_download_no_alert(button_id="CmdInnsatuBTN",case_name="内払い決定通知書")
        # 11 支払調整登録画面: 内払い決定通知書「ファイルを開く(O)」ボタンを押下

        # 12 内払い決定通知書（PDF）: 表示
        self.screen_shot("内払い決定通知書（PDF）_12")

        # 13 内払い決定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 14 支払調整登録画面: 表示
        self.screen_shot("支払調整登録画面_14")

        # 15 支払調整登録画面: 「戻る」ボタン押下
        self.return_click()

        # 16 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_16")

        # 17 支払調整履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 18 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_18")
