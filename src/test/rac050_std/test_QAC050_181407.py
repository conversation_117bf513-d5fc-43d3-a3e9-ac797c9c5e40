import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181407(FukushiSiteTestCaseBase):
    """TestQAC050_181407"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181407"]
        super().setUp()

    # 不足書類があるため保留となっている対象者について、保留通知書等を出力できることを確認する。
    def test_QAC050_181407(self):
        """保留通知書作成"""

        case_data = self.test_data["TestQAC050_181407"]
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        pKijyunbi = case_data.get("pKijyunbi", "")
        pOutputOrder = case_data.get("pOutputOrder", "")
        pTeishutsuKigen = case_data.get("pTeishutsuKigen", "")
        pTokusokuYMD = case_data.get("pTokusokuYMD", "")
        pKetteYMD = case_data.get("pKetteYMD", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面:
        # 業務：児童
        # 事業：児童扶養手当
        # 処理区分：月次
        # 処理分類：不足書類督促
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect)

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「不足書類督促抽出処理」のNoボタン押下
        self.click_batch_job_button_by_label("不足書類督促抽出処理")

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: 基準日「20230901」出力順「カナ氏名順」
        params = [
            {"title": "基準日", "type": "text", "value": pKijyunbi},
            {"title": "出力順", "type": "select", "value": pOutputOrder},
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 19 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「督促用決済名簿」のNoボタン押下

        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 22 督促用決済名簿（PDF）: 表示
        # self.screen_shot("督促用決済名簿（PDF）_22")

        # 23 督促用決済名簿（PDF）: ×ボタン押下でPDFを閉じる

        # 24 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_24")

        # 25 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 26 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_26")

        # 27 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 28 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_28")

        # 29 バッチ起動画面: 「不足書類督促状出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("不足書類督促状出力処理")

        # 30 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_30")

        # 31 バッチ起動画面: 提出期限「(空白)」
        params = [
            {"title": "提出期限", "type": "text", "value": pTeishutsuKigen}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_31")

        # 32 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 33 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_33")

        # 34 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 35 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_35")

        # 36 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 37 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_37")

        # 38 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_38")

        # 39 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 40 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_40")

        # 41 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 42 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_42")

        # 43 ジョブ帳票履歴画面: 「保留通知書」のNoボタン押下

        # 44 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 45 保留通知書（PDF）: 表示
        # self.screen_shot("保留通知書（PDF）_45")

        # 46 保留通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 47 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_47")

        # 48 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 49 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_49")

        # 50 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 51 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_51")

        # 52 バッチ起動画面: 「不足書類テーブル更新処理」のNoボタン押下
        self.click_batch_job_button_by_label("不足書類テーブル更新処理")

        # 53 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_53")

        # 54 バッチ起動画面: 督促年月日「20230901」決定年月日「20230901」
        params = [
            {"title": "督促年月日", "type": "text", "value": pTokusokuYMD},
            {"title": "決定年月日", "type": "text", "value": pKetteYMD},
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_54")

        # 55 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 56 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_56")

        # 57 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 58 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_58")

        # 59 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 60 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_60")

        # 61 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_61")
