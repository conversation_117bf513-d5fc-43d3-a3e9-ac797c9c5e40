import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180204(FukushiSiteTestCaseBase):
    """TestQAC050_180204"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180204"]
        super().setUp()

    # 不足書類の提出日の登録ができることを確認する。
    def test_QAC050_180204(self):
        """受付情報入力"""

        case_data = self.test_data["TestQAC050_180204"]
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 3 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("提出書類管理")

        # 4 提出書類管理: 表示
        self.screen_shot("提出書類管理_3")

        # 5 提出書類管理: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 6 提出書類管理: 戸籍謄抄本の提出日「20230502」所得証明書の提出日「20230502」
        # self.form_input_by_id(idstr="TxtSyoruiYMD_1", value="20230502")
        self.form_input_by_id(idstr="TxtSyoruiYMD_3", value="20230502")
        self.screen_shot("提出書類管理_5")

        # 7 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 8 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_7")

        # 9 児童扶養手当資格管理画面: 児童のNoボタン押下
        #self.click_by_id("CmdJidouTsuika")
        self.click_by_id("CmdNo1")

        # 10 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_9")

        # 11 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 12 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_11")

        # 13 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 14 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 15 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_14")
