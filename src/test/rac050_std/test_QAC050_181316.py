import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181316(FukushiSiteTestCaseBase):
    """TestQAC050_181316"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 現況届未提出者に対して、支払差止の情報を登録できることを確認する。
    def test_QAC050_181316(self):
        """支払差止処理"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.screen_shot("個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「児童扶養手当」ボタン押下
        self.click_button_by_label("児童扶養手当")

        # 8 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_8")

        # 9 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 10 児童扶養手当資格管理画面: 「差止情報」ボタン押下
        self.click_button_by_label("差止情報")

        # 11 差止情報画面: 表示
        self.screen_shot("差止情報画面_11")

        # 12 差止情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 13 差止情報画面: 表示
        self.screen_shot("差止情報画面_13")

        # 14 差止情報画面: 差止事由「現況届未提出」選択支払差止決定日「20231002」差止対象年度「令和５年」選択支払差止開始年月「202311」
        self.form_input_by_id(idstr="CmbRiyu", text="現況届未提出")
        self.form_input_by_id(idstr="TxtKettei", value="20231002")
        self.form_input_by_id(idstr="CmbSashitomeNendo", text="令和５年")
        self.form_input_by_id(idstr="TxtKaishi", value="202311")
        self.screen_shot("差止情報画面_14")

        # 15 差止情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 16 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_16")

        # 17 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 18 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 19 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。 ")
        self.screen_shot("児童扶養手当資格管理画面_19")

        # 20 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_20")

        # 21 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 22 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_22")

        # 23 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：年次処理分類：現況未提出者処理
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="年次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="現況未提出者処理")

        # 24 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_24")

        # 25 バッチ起動画面: 「現況届未提出差止対象者抽出処理」のNoボタン押下
        self.click_batch_job_button_by_label("現況届未提出差止対象者抽出処理")

        # 26 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_26")

        # 27 バッチ起動画面: 現況年度「令和５年」発行年月日「20230702」出力順「証書番号順」
        params = [
            {"title": "現況年度", "type": "select", "value": "令和５年"},
            {"title": "発行年月日", "type": "text", "value": "20230702"},
            {"title": "出力順", "type": "select", "value": "証書番号順"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_27")

        # 28 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 29 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_29")

        # 30 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 31 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_31")

        # 32 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 33 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_33")

        # 34 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_36")

        # 35 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 36 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_36")

        # 37 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 38 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_38")

        # 39 ジョブ帳票履歴画面: 「現況届未提出差止対象者一覧」のNoボタン押下

        # 40 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 41 現況届未提出差止対象者一覧（PDF）: 表示
        # self.screen_shot("現況届未提出差止対象者一覧（PDF）_43")

        # 42 現況届未提出差止対象者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 43 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_43")

        # 44 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 45 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_45")

        # 46 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 47 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_47")

        # 48 バッチ起動画面: 「現況届未提出差止通知書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("現況届未提出差止通知書出力処理")

        # 49 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_49")

        # 50 バッチ起動画面: 「処理開始」ボタン押下
        self.exec_batch_job()

        # 51 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_51")

        # 52 バッチ起動画面: 出力区分「0」開始頁「000001」終了頁「999999」
        params = [
            {"title": "出力区分", "type": "text", "value": "0"},
            {"title": "開始頁", "type": "text", "value": "000001"},
            {"title": "終了頁", "type": "text", "value": "999999"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_52")

        # 53 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 54 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_54")

        # 55 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 56 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_56")

        # 57 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 58 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_58")

        # 59 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_61")

        # 60 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 61 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_61")

        # 62 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 63 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_63")

        # 64 ジョブ帳票履歴画面: 「差止通知書」のNoボタン押下

        # 65 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 66 差止通知書（PDF）: 表示
        # self.screen_shot("差止通知書（PDF）_68")

        # 67 差止通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 68 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_68")

        # 69 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 70 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_70")

        # 71 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 72 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_72")

        # 73 バッチ起動画面: 「現況届未提出差止更新処理」のNoボタン押下
        self.click_batch_job_button_by_label("現況届未提出差止更新処理")

        # 74 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_74")

        # 75 バッチ起動画面: 現況年度「令和５年」差止決定「20230702」
        params = [
            {"title": "現況年度", "type": "select", "value": "令和５年"},
            {"title": "差止決定", "type": "text", "value": "20230702"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_75")

        # 76 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 77 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_77")

        # 78 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 79 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_79")

        # 80 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 81 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_81")

        # 82 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_84")
