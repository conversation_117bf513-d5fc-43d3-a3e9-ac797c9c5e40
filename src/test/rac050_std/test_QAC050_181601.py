import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181601(FukushiSiteTestCaseBase):
    """TestQAC050_181601"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181601"]
        super().setUp()

    # 過払金がある対象者を抽出できることを確認する。
    def test_QAC050_181601(self):
        """過払金対象者抽出"""

        case_data = self.test_data["TestQAC050_181601"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「債権管理」ボタン押下
        self.saiken_kanri_click()  # Button with ID: CmdProcess7_2 instead of self.click_button_by_label("債権管理")

        # 3 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_3")

        # 4 債権履歴画面: 事業「児童扶養手当」選択絞り込み条件「全て（終了含む）」チェック
        self.form_input_by_id(idstr="CmbGyomu", text="児童扶養手当")
        self.form_input_by_id(idstr="RadioA", value="1")
        self.screen_shot("債権履歴画面_4")

        # 5 債権履歴画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 6 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_6")

        # 7 債権履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 8 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_8")
