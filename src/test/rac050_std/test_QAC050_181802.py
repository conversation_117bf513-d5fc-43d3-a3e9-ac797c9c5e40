import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181802(FukushiSiteTestCaseBase):
    """TestQAC050_181802"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181802"]
        sql_params = {
            "TARGET_GYOUMU_CODE": "QAC050", 
            "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
            "TARGET_NENDO":"2022",
            "TARGET_NENDO2":"2023"
        }
        self.exec_sqlfile("Test_QAC050_181802.sql", params=sql_params)
        sql_params = {
            "TARGET_GYOUMU_CODE": "QAC050", 
            "DELETE_ATENA_CODE": case_data.get("atena_code2", ""),
            "TARGET_NENDO":"2022",
            "TARGET_NENDO2":"2023"
        }
        self.exec_sqlfile("Test_QAC050_181802.sql", params=sql_params)
        super().setUp()

    # 年齢到達した児童がいる場合、額改定（減額）の登録ができることを確認する。
    def test_QAC050_181802(self):
        """額改定処理"""

        case_data = self.test_data["TestQAC050_181802"]
        atena_code = case_data.get("atena_code", "")
        shinsei_shubetsu = case_data.get("shinsei_shubetsu", "")
        shinsei_riyuu = case_data.get("shinsei_riyuu", "")
        kettei_ymd = case_data.get("kettei_ymd", "")
        kaitei_ymd = case_data.get("kaitei_ymd", "")

        # =====資格登録=====
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        self.click_button_by_label("申請内容入力")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
        self.click_button_by_label("確定")
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230325")
        self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20230501")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("2")
        self.form_input_by_id(idstr="CmbZokugara", text="子")
        self.form_input_by_id(idstr="RdoDokyo1", value="1")
        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        self.form_input_by_id(idstr="KojiGaitou2", value="1")
        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        self.form_input_by_id(idstr="RdoShogai2", value="1")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("3")
        self.form_input_by_id(idstr="CmbZokugara", text="子")
        self.form_input_by_id(idstr="RdoDokyo1", value="1")
        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        self.form_input_by_id(idstr="KojiGaitou2", value="1")
        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        self.form_input_by_id(idstr="RdoShogai2", value="1")
        self.click_button_by_label("福祉世帯情報")
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230501")
        self.form_input_by_id(idstr="HoninCmb_2", text="子")
        self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20230501")
        self.form_input_by_id(idstr="HoninCmb_3", text="子")
        self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20230501")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("口座情報")
        self.click_button_by_label("追加")
        self.entry_kouza_info(
            start_ymd=kaitei_ymd,
            ginko_code="0001",
            shiten_code="001",
            kouza_shubetsu_text="普通",
            kouza_bango="1234567",
            koukai=True
        )
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.form_input_by_id(idstr="TxtKaitei", value="202305")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230401")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()

        self.click_button_by_label("決定内容入力") 
        self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230401")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.form_input_by_id(idstr="TxtShoushoBango", value="181802")

        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()

        #支払履歴入力(支払済にする)
        self.click_button_by_label("修正")
        self.open_common_buttons_area()
        self.click_button_by_label("支払履歴")
        self.click_button_by_label("6")
        self.click_button_by_label("修正")
        self.form_input_by_id(idstr="TbxPayDate", value="20230901")
        self.form_input_by_id(idstr="TbxDecisionDate", value="20230901")
        self.form_input_by_id(idstr="CmbPaymentMethod", text="窓口")
        self.click_button_by_label("最新口座")
        self.click_button_by_label("登録")
        self.alert_ok()

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：年齢到達処理
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="年齢到達処理")

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「年齢到達更新処理」のNoボタン押下
        self.click_batch_job_button_by_label("年齢到達更新処理")

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: 決定年月日「20240402」
        params = [
            {"title": "決定年月日", "type": "text", "value": kettei_ymd}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(360,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_16")

        # 17 メインメニュー画面: 「申請資格管理」ボタン押下
        self.do_login()
        self.shinsei_shikaku_kanri_click()

        # 18 個人検索画面: 表示
        self.screen_shot("個人検索画面_18")

        # 19 個人検索画面: 「住民コード」入力
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        self.screen_shot("個人検索画面_19")

        # 20 個人検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 21 受給状況画面: 表示
        self.screen_shot("受給状況画面_21")

        # 22 受給状況画面: 「児童扶養手当」ボタン押下
        self.click_button_by_label("児童扶養手当")

        # 23 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_23")

        # 24 児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 25 児童扶養手当資格管理画面: 申請種別「額改定（減額）」選択申請理由「２０歳到達」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinsei_shubetsu)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinsei_riyuu)
        self.screen_shot("児童扶養手当資格管理画面_25")

        # 26 児童扶養手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 27 児童扶養手当資格管理画面: 請求年月日「20240301」事由発生年月日「20240228」改定年月「202403」職権チェック
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20240301")
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20240228")
        self.form_input_by_id(idstr="TxtKaitei", value="202403")
        self.form_input_by_id(idstr="ShokkenChkBox", value="1")
        self.screen_shot("児童扶養手当資格管理画面_27")

        # 28 児童扶養手当資格管理画面: 児童「1」Noボタン押下
        self.click_button_by_label("1")

        # 29 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_29")

        # 30 支給対象児童入力画面: 非該当年月日「20240228」非該当事由「年齢到達（20歳）」
        self.form_input_by_id(idstr="TxtHiGaitoYMD", value="20240228")
        self.form_input_by_id(idstr="CmbHiGaitoJiyu", text="年齢到達（20歳）")
        self.screen_shot("支給対象児童入力画面_30")

        # 31 支給対象児童入力画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 32 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_32")

        # 33 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 34 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 35 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_35")

        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20240301")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20240301")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")

        # 36 児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 37 児童扶養手当資格管理画面: 決定年月日「20240302」決定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20240302")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.screen_shot("児童扶養手当資格管理画面_37")

        # 38 児童扶養手当資格管理画面: 児童「1」Noボタン押下
        self.click_button_by_label("1")

        # 39 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_39")

        # 40 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 41 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_41")

        # 42 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 43 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 44 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_44")
