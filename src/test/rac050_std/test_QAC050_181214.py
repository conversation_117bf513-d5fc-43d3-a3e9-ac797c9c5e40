import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181214(FukushiSiteTestCaseBase):
    """TestQAC050_181214"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181214"]
        super().setUp()

    # 過払金が発生有無を、支払履歴画面、債権履歴画面、支払調整履歴画面で確認する。※過払金の発生は、遡りで額改定（減）や資格喪失等の決定履歴の登録時に発生するため
    def test_QAC050_181214(self):
        """過払金計算"""

        case_data = self.test_data["TestQAC050_181214"]
        atena_code = case_data.get("atena_code", "")
        atena_code_2 = case_data.get("atena_code_2", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「支払履歴」ボタン押下
        self.click_button_by_label("支払履歴")

        # 2 支払履歴画面: 表示
        self.screen_shot("支払履歴画面_2")

        # 3 支払履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 4 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_4")

        # 5 児童扶養手当資格管理画面: 「債権履歴」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("債権履歴")

        # 6 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_6")

        # 7 債権履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 8 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_8")

        # 9 児童扶養手当資格管理画面: 「支払調整履歴」ボタン押下
        self.click_button_by_label("支払調整履歴")

        # 10 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_10")

        # 11 支払調整履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 12 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_12")

        # 13 児童扶養手当資格管理画面: 「支払履歴」ボタン押下
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code_2, gyoumu_code="QAC050")
        self.click_button_by_label("支払履歴")

        # 14 支払履歴画面: 表示
        self.screen_shot("支払履歴画面_14")

        # 15 支払履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 16 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_16")

        # 17 児童扶養手当資格管理画面: 「債権履歴」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("債権履歴")

        # 18 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_18")

        # 19 債権履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 20 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_20")

        # 21 児童扶養手当資格管理画面: 「支払調整履歴」ボタン押下
        self.click_button_by_label("支払調整履歴")

        # 22 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_22")

        # 23 支払調整履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 24 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_24")
