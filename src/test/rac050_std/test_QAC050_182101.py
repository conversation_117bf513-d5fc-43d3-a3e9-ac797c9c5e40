import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_182101(FukushiSiteTestCaseBase):
    """TestQAC050_182101"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_182101"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC050", "DELETE_ATENA_CODE": case_data.get("atena_code", ""),"DELETE_ATENA_CODE_2": case_data.get("atena_code_2", ""),
                      "TARGET_NENDO":case_data.get("nendo", ""),"TARGET_SOUSYOTOKU":case_data.get("soushotoku", "")}
        self.exec_sqlfile("Test_QAC050_182101.sql", params=sql_params)
        super().setUp()

    # 有期到来する受給者を抽出し、外国人在留期限一覧、拘禁終了予定一覧、障害有期認定一覧を出力できることを確認する。
    def test_QAC050_182101(self):
        """有期認定期限到来者抽出"""

        case_data = self.test_data["TestQAC050_182101"]
        atena_code = case_data.get("atena_code", "")
        atena_code_2 = case_data.get("atena_code_2", "")

        # =====有期認定：資格登録=====
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        self.click_button_by_label("申請内容入力")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
        self.click_button_by_label("確定")
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230325")
        self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20230501")
        #一人目障害ありの児童追加
        self.click_button_by_label("児童追加")
        self.click_button_by_label("3")
        self.form_input_by_id(idstr="CmbZokugara", text="子")
        self.form_input_by_id(idstr="RdoDokyo1", value="1")
        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        self.form_input_by_id(idstr="KojiGaitou2", value="1")
        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        self.form_input_by_id(idstr="RdoShogai1", value="1")
        self.form_input_by_id(idstr="CmbShinsaKekka", text="継続支給")
        self.form_input_by_id(idstr="TxtShougaiNinteiKaishiYMD", value="20230501")
        self.form_input_by_id(idstr="TxtShindanshouSakuseiYMD", value="20230501")
        self.form_input_by_id(idstr="TxtJikaiShindanshouTeisyutsuKigenYMD", value="20240501")
        self.form_input_by_id(idstr="ChkHaHaMei", value="1")
        self.form_input_by_id(idstr="CmbJokyo2", text="障害")
        self.form_input_by_id(idstr="TxtHaHaMei", value=atena_code)
        self.click_by_id("span_CmdKensaku2")
        self.click_button_by_label("1")
        self.form_input_by_id(idstr="TxtHaHaNinteiKaishi", value="202304")
        self.form_input_by_id(idstr="TxtHaHaNinteiSyuryou", value="202305")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("福祉世帯情報")
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230501")
        self.form_input_by_id(idstr="ChkFlg_2", value="1")
        self.form_input_by_id(idstr="HoninCmb_3", text="子")
        self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20230501")
        self.form_input_by_id(idstr="HoninCmb_4", text="子")
        self.form_input_by_id(idstr="JukyuCmb_4", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_4", value="20230501")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("所得情報")
        self.return_click()
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("口座情報")
        self.click_button_by_label("追加")
        self.entry_kouza_info(
            start_ymd='20230501',
            ginko_code="0001",
            shiten_code="001",
            kouza_shubetsu_text="普通",
            kouza_bango="1234567",
            koukai=True
        )
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.form_input_by_id(idstr="TxtKaitei", value="202305")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230401")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
        self.click_button_by_label("決定内容入力") 
        self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230401")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.form_input_by_id(idstr="TxtNinteiBango", value="1821769")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")

        # =====拘束：資格登録=====
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code_2, gyoumu_code="QAC050")
        self.click_button_by_label("申請内容入力")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
        self.click_button_by_label("確定")
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230325")
        self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20230501")
        #一人目障害ありの児童追加
        self.click_button_by_label("児童追加")
        self.click_button_by_label("3")
        self.form_input_by_id(idstr="CmbZokugara", text="子")
        self.form_input_by_id(idstr="RdoDokyo1", value="1")
        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        self.form_input_by_id(idstr="KojiGaitou2", value="1")
        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        self.form_input_by_id(idstr="RdoShogai1", value="1")
        self.form_input_by_id(idstr="CmbShinsaKekka", text="継続支給")
        self.form_input_by_id(idstr="TxtShougaiNinteiKaishiYMD", value="20230501")
        self.form_input_by_id(idstr="TxtShindanshouSakuseiYMD", value="20230501")
        self.form_input_by_id(idstr="TxtJikaiShindanshouTeisyutsuKigenYMD", value="20240501")
        self.form_input_by_id(idstr="ChkHaHaMei", value="1")
        self.form_input_by_id(idstr="TxtHaHaMei", value=atena_code_2)
        self.click_by_id("span_CmdKensaku2")
        self.click_button_by_label("1")
        self.form_input_by_id(idstr="CmbJokyo2", text="拘禁")
        self.form_input_by_id(idstr="TxtHaHaKouKinKaishiYMD", value="20230401")
        self.form_input_by_id(idstr="TxtShuryoYMD2", value="20230430")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("福祉世帯情報")
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230501")
        self.form_input_by_id(idstr="ChkFlg_2", value="1")
        self.form_input_by_id(idstr="HoninCmb_3", text="子")
        self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20230501")
        self.form_input_by_id(idstr="HoninCmb_4", text="子")
        self.form_input_by_id(idstr="JukyuCmb_4", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_4", value="20230501")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("所得情報")
        self.return_click()
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("口座情報")
        self.click_button_by_label("追加")
        self.entry_kouza_info(
            start_ymd='20230501',
            ginko_code="0001",
            shiten_code="001",
            kouza_shubetsu_text="普通",
            kouza_bango="1234567",
            koukai=True
        )
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.form_input_by_id(idstr="TxtKaitei", value="202305")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230401")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
        self.click_button_by_label("決定内容入力") 
        self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230401")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.form_input_by_id(idstr="TxtNinteiBango", value="1821925")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：確認処理
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="確認処理")

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「有期認定到来者一覧出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("有期認定到来者一覧出力処理")

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: 基準年月日「20230501」出力順「証書番号順」選択
        params = [
            {"title": "基準年月日", "type": "text", "value": "20230501"},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_18")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 19 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「外国人在留期限一覧」のNoボタン押下

        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 22 外国人在留期限一覧（PDF）: 表示
        # self.screen_shot("外国人在留期限一覧（PDF）_23")

        # 23 外国人在留期限一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 24 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_24")

        # 25 ジョブ帳票履歴画面: 「拘禁終了予定一覧」のNoボタン押下

        # 26 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 27 拘禁終了予定一覧（PDF）: 表示
        # self.screen_shot("拘禁終了予定一覧（PDF）_28")

        # 28 拘禁終了予定一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 29 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_29")

        # 30 ジョブ帳票履歴画面: 「障害有期認定一覧」のNoボタン押下

        # 31 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 32 障害有期認定一覧（PDF）: 表示
        # self.screen_shot("障害有期認定一覧（PDF）_33")

        # 33 障害有期認定一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 34 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_34")
