import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181302(FukushiSiteTestCaseBase):
    """TestQAC050_181302"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181302"]
        super().setUp()

    # 現況届を出力できることを確認する。
    def test_QAC050_181302(self):
        """現況届等作成"""

        case_data = self.test_data["TestQAC050_181302"]
        atena_code_0 = case_data.get("atena_code_0", "")
        atena_code = case_data.get("atena_code", "")
        pCmbTaisyoNendo = case_data.get("pCmbTaisyoNendo", "")
        pCmbHiyouKbn = case_data.get("pCmbHiyouKbn", "")
        pTxtHakkouYMD = case_data.get("pTxtHakkouYMD", "")
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        pGenkyoY = case_data.get("pGenkyoY", "")
        pStartNo = case_data.get("pStartNo", "")
        pEndNo = case_data.get("pEndNo", "")
        pHakkoYMD = case_data.get("pHakkoYMD", "")
        pHakkoYMD_2 = case_data.get("pHakkoYMD_2", "")

#        # 「1813-24」用データ入力
#        self.do_login()
#        self.shinsei_shikaku_kanri_click()
#        self.kojin_kensaku_by_atena_code(atena_code=atena_code_0)
#        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code_0, gyoumu_code="QAC050")
#        self.click_button_by_label("修正")
#        self.find_common_buttons()
#        self.open_common_buttons_area()
#        self.common_button_click(button_text="現況情報")
#        self.click_button_by_label("追加")
#        # 対象年度
#        self.form_input_by_id(idstr="CmbTaisyoNendo", text=pCmbTaisyoNendo)
#        # 現況区分
#        self.form_input_by_id(idstr="CmbHiyouKbn", text=pCmbHiyouKbn)
#        # 発行年月日
#        self.form_input_by_id(idstr="TxtHakkouYMD", value=pTxtHakkouYMD)
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#
#        self.do_login()
#        # 1 メインメニュー画面: 表示
#        self.screen_shot("メインメニュー画面_1")
#
#        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
#        self.shinsei_shikaku_kanri_click()
#
#        # 3 個人検索画面: 表示
#        self.screen_shot("個人検索画面_3")
#
#        # 4 個人検索画面: 「住民コード」入力
#        # self.screen_shot("個人検索画面_4")
#
#        # 5 個人検索画面: 「検索」ボタン押下
#        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
#
#        # 6 受給状況画面: 表示
#        self.screen_shot("受給状況画面_7")
#
#        # 7 受給状況画面: 「児童扶養手当」ボタン押下
#        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
#
#        # 8 児童扶養手当資格管理画面: 表示
#        self.screen_shot("児童扶養手当資格管理画面_8")
#
#        # ＜対象者の条件＞
#        # ・児童扶養手当の資格（児童2人）を持っている住民
#        # ・本人と児童がいる世帯の住民
#        # ・児童扶養手当の支給区分が「全部支給」
#        # ・児童扶養手当で令和５年度の現況履歴が未提出
#        self.click_button_by_label("修正")
#        self.find_common_buttons()
#        self.open_common_buttons_area()
#        self.common_button_click(button_text="現況情報")
#        self.click_button_by_label("追加")
#        # 対象年度
#        self.form_input_by_id(idstr="CmbTaisyoNendo", text=pCmbTaisyoNendo)
#        # 現況区分
#        self.form_input_by_id(idstr="CmbHiyouKbn", text=pCmbHiyouKbn)
#        # 発行年月日
#        self.form_input_by_id(idstr="TxtHakkouYMD", value=pTxtHakkouYMD)
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#
#        # 9 児童扶養手当資格管理画面: 「修正」ボタン押下
#        self.click_button_by_label("修正")
#
#        # 10 児童扶養手当資格管理画面: 「現況情報」ボタン押下
#        self.find_common_buttons()
#        self.open_common_buttons_area()
#        self.common_button_click(button_text="現況情報")
#
#        # 11 現況履歴画面: 表示
#        self.screen_shot("現況履歴画面_11")
#
#        # 12 現況履歴画面: 現況履歴一覧「1」Noボタン押下
#        self.click_button_by_label("1")
#
#        # 13 現況履歴画面: 発行年月日「20230702」
#        self.form_input_by_id(idstr="TxtHakko", value=pTxtHakkouYMD)
#        self.screen_shot("現況履歴画面_13")
#
#        # 14 現況履歴画面: 「印刷」ボタン押下
#        # 15 現況履歴画面: 児童扶養手当現況届「ファイルを開く(O)」ボタンを押下
#        self.pdf_output_and_download_no_alert(button_id="BtnInsatsu",case_name="現況届")
#        self.assert_message_area("プレビューを表示しました")
#        self.screen_shot("現況履歴画面_15")
#
#        # 16 児童扶養手当現況届（PDF）: 表示
#        # self.screen_shot("児童扶養手当現況届（PDF）_16")
#
#        # 17 児童扶養手当現況届（PDF）: 2ページ目表示
#        # Assert: 中面が出力されていることを確認する。
#        # self.screen_shot("児童扶養手当現況届（PDF）_18")
#
#        # 18 児童扶養手当現況届（PDF）: 3ページ目表示
#        # Assert: 裏面が出力されていることを確認する。
#        # self.screen_shot("児童扶養手当現況届（PDF）_18")
#
#        # 19 児童扶養手当現況届（PDF）: ×ボタン押下でPDFを閉じる
#
#        # 20 現況履歴画面: 「戻る」ボタン押下
#        self.return_click()
#
#        # 21 児童扶養手当資格管理画面: 表示
#        self.screen_shot("児童扶養手当資格管理画面_21")
#
#        # 22 児童扶養手当資格管理画面: 「月額計算」ボタン押下
#        self.click_button_by_label("月額計算")
#
#        # 23 児童扶養手当資格管理画面: 「登録」ボタン押下
#        self.click_button_by_label("登録")
#        self.alert_ok()
#
#        # 24 児童扶養手当資格管理画面: 表示
#        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
#        self.assert_message_base_header("登録しました。")
#        self.screen_shot("児童扶養手当資格管理画面_24")
#
        # 25 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_25")

        # 26 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")

        # 27 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_27")

        # 28 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：年次処理分類：現況出力
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect)

        # 29 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_29")

        # 30 バッチ起動画面: 「お知らせ 」のNoボタン押下
        self.click_batch_job_button_by_label("お知らせ")

        # 31 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_31")

        # 32 バッチ起動画面: 現況年度「令和５年度」開始連番「000001」終了連番「999999」発行年月日「20230702」
        params = [
            {"title": "現況年度", "type": "select", "value": pGenkyoY},
            {"title": "開始連番", "type": "text", "value": pStartNo},
            {"title": "終了連番", "type": "text", "value": pEndNo},
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_32")

        # 33 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 34 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_36")

        # 35 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 36 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_38")

        # 37 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 38 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_38")

        # 39 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # self.screen_shot("ジョブ実行履歴画面_39")

        # 40 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 41 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_41")

        # 42 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 43 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_43")

        # 44 ジョブ帳票履歴画面: 「現況届案内」のNoボタン押下

        # 45 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 46 現況届案内（PDF）: 表示
        # self.screen_shot("現況届案内（PDF）_46")

        # 47 現況届案内（PDF）: ×ボタン押下でPDFを閉じる

        # 48 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_48")

        # 49 ジョブ帳票履歴画面: 「現況届提出前のおねがい」のNoボタン押下
        # self.click_batch_job_button_by_label("現況届提出前のおねがい")

        # 50 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # self.pdf_output_and_download(case_name="ジョブ帳票履歴画面")

        # 51 現況届提出前のおねがい（PDF）: 表示
        # self.screen_shot("現況届提出前のおねがい（PDF）_51")

        # 52 現況届提出前のおねがい（PDF）: ×ボタン押下でPDFを閉じる

        # 53 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_53")

        # 54 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 55 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_55")

        # 56 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 57 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_57")

        # 58 バッチ起動画面: 「現況届出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("現況届出力処理")

        # 59 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_59")

        # 60 バッチ起動画面: 発行年月日「20230702」
        params = [
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD_2}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_60")

        # 61 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 62 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_62")

        # 63 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 64 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_64")

        # 65 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 66 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_66")

        # 67 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # self.screen_shot("ジョブ実行履歴画面_67")

        # 68 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 69 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_69")

        # 70 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 71 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_71")

        # 72 ジョブ帳票履歴画面: 「児童扶養手当現況届」のNoボタン押下

        # 73 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 74 児童扶養手当現況届（PDF）: 表示
        # self.screen_shot("児童扶養手当現況届（PDF）_74")

        # 75 児童扶養手当現況届（PDF）: 2ページ目表示
        # Assert: 中面が出力されていることを確認する。
        # self.screen_shot("児童扶養手当現況届（PDF）_75")

        # 76 児童扶養手当現況届（PDF）: 3ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("児童扶養手当現況届（PDF）_76")

        # 77 児童扶養手当現況届（PDF）: ×ボタン押下でPDFを閉じる

        # 78 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_78")
