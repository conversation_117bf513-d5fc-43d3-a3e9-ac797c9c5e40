import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_182401(FukushiSiteTestCaseBase):
    """TestQAC050_182401"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_182401"]
        super().setUp()

    # 同一住所者リスト、受給者メモ情報リスト、手帳異動リストを出力できることを確認する。
    def test_QAC050_182401(self):
        """必要に応じて確認処理"""

        case_data = self.test_data["TestQAC050_182401"]
        atena_code = case_data.get("atena_code", "")
        pHakkou_ymd = case_data.get("pHakkou_ymd", "")
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        pJyukyuKubun = case_data.get("pJyukyuKubun", "")
        pKijyunYMD = case_data.get("pKijyunYMD", "")
        pMatchCond = case_data.get("pMatchCond", "")
        pOutputOrder = case_data.get("pOutputOrder", "")
        pStartYMD = case_data.get("pStartYMD", "")
        pEndYMD = case_data.get("pEndYMD", "")
        pTaishoshaKubun = case_data.get("pTaishoshaKubun", "")
        pIdoStartYMD = case_data.get("pIdoStartYMD", "")
        pIdoEndYMD = case_data.get("pIdoEndYMD", "")
        pOutputOrder_2 = case_data.get("pOutputOrder_2", "")

#        # 1 児童扶養手当資格管理画面: 「印刷」ボタン押下
#        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
#        self.click_button_by_label("印刷")
#
#        # 2 帳票印刷画面: 表示
#        self.screen_shot("帳票印刷画面_2")
#
#        # 3 帳票印刷画面: 「受給者メモ情報リスト」行の印刷チェックボックス選択「受給者メモ情報リスト」行の発行年月日チェックボックス選択「受給者メモ情報リスト」行の発行年月日「20231202」
#        self.print_online_reports(case_name="帳票印刷", report_name="受給者メモ情報リスト", hakkou_ymd=pHakkou_ymd)
#
#        # 4 帳票印刷画面: 「印刷」ボタン押下
#        # self.exec_online_print("印刷")
#
#        # 5 帳票印刷画面: 受給者メモ情報リスト「ファイルを開く(O)」ボタンを押下
#
#        # 6 受給者メモ情報リスト（PDF）: 表示
#        self.screen_shot("受給者メモ情報リスト（PDF）_6")
#
#        # 7 受給者メモ情報リスト（PDF）: ×ボタン押下でPDFを閉じる
#
#        # 8 帳票印刷画面: 「戻る」ボタン押下
#        self.return_click()
#
#        # 9 児童扶養手当資格管理画面: 表示
#        self.screen_shot("児童扶養手当資格管理画面_9")
#
        # 10 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_10")

        # 11 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 12 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_12")

        # 13 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：確認処理
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect)

        # 14 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_14")

        # 15 バッチ起動画面: 「同一住所者リスト出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("同一住所者リスト出力処理")

        # 16 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_16")

        # 17 バッチ起動画面: 現在受給区分「現受給者」選択基準年月日「20230501」マッチング条件「1」出力順「証書番号順」選択開始異動日「20230401」終了異動日「20230430」
        params = [
            {"title": "現在受給区分", "type": "select", "value": pJyukyuKubun},
            {"title": "基準年月日", "type": "text", "value": pKijyunYMD},
            {"title": "マッチング条件", "type": "text", "value": pMatchCond},
            {"title": "出力順", "type": "select", "value": pOutputOrder},
            {"title": "開始異動日", "type": "text", "value": pStartYMD},
            {"title": "終了異動日", "type": "text", "value": pEndYMD},
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_17")

        # 18 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 19 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_19")

        # 20 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 21 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_21")

        # 22 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 23 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_23")

        # 24 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_24")

        # 25 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 26 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_26")

        # 27 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="同一住所者リスト")

        # 28 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_28")

        # 29 ジョブ帳票履歴画面: 「同一住所者リスト」のNoボタン押下

        # 30 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 31 同一住所者リスト（PDF）: 表示
        # self.screen_shot("同一住所者リスト（PDF）_33")

        # 32 同一住所者リスト（PDF）: ×ボタン押下でPDFを閉じる

        # 33 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_33")

        # 34 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 35 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_35")

        # 36 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 37 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_37")

        # 38 バッチ起動画面: 「受給者メモ情報リスト出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("受給者メモ情報リスト出力処理")

        # 39 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_39")

        # 40 バッチ起動画面: 対象者区分「現受給者」選択
        params = [
            {"title": "対象者区分", "type": "select", "value": pTaishoshaKubun},
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_40")

        # 41 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 42 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_44")

        # 43 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 44 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_44")

        # 45 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 46 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_46")

        # 47 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_47")

        # 48 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 49 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_49")

        # 50 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="受給者メモ情報リスト")

        # 51 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_53")

        # 52 ジョブ帳票履歴画面: 「受給者メモ情報リスト」のNoボタン押下

        # 53 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 54 受給者メモ情報リスト（PDF）: 表示
        # self.screen_shot("受給者メモ情報リスト（PDF）_54")

        # 55 受給者メモ情報リスト（PDF）: ×ボタン押下でPDFを閉じる

        # 56 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_56")

        # 57 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 58 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_58")

        # 59 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 60 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_60")

        # 61 バッチ起動画面: 「手帳異動リスト出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("手帳異動リスト出力処理")

        # 62 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_62")

        # 63 バッチ起動画面: 異動日開始「20230401」異動日終了「20230430」出力順「証書番号順」選択
        params = [
            {"title": "異動日開始", "type": "text", "value": pIdoStartYMD},
            {"title": "異動日終了", "type": "text", "value": pIdoEndYMD},
            {"title": "出力順", "type": "select", "value": pOutputOrder_2},
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_63")

        # 64 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 65 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_65")

        # 66 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 67 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_67")

        # 68 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 69 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_69")

        # 70 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_70")

        # 71 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 72 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_72")

        # 73 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="手帳異動リスト一覧")

        # 74 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_74")

        # 75 ジョブ帳票履歴画面: 「手帳異動リスト一覧」のNoボタン押下

        # 76 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 77 手帳異動リスト一覧（PDF）: 表示
        # self.screen_shot("手帳異動リスト一覧（PDF）_77")

        # 78 手帳異動リスト一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 79 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_79")
