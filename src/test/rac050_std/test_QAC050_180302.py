import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180302(FukushiSiteTestCaseBase):
    """TestQAC050_180302"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180302"]
        super().setUp()

    # 不備があった書類の登録ができることを確認する。
    def test_QAC050_180302(self):
        """不備書類入力"""

        case_data = self.test_data["TestQAC050_180302"]
        atena_codes = [case_data.get("atena_code", ""), case_data.get("atena_code2", ""), case_data.get("atena_code3", "")]

        for i,atena_code in enumerate(atena_codes):
            if i == 0: atena_no = "①"
            if i == 1: atena_no = "②"
            if i == 2: atena_no = "③"

            self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
            # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
            self.click_button_by_label("修正")
            self.open_common_buttons_area()

            # 2 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
            self.click_button_by_label("提出書類管理")

            # 3 提出書類管理: 表示
            self.screen_shot(f"提出書類管理_3_{atena_no}")

            # 4 提出書類管理: 「追加」ボタン押下
            self.click_button_by_label("追加")

            # 5 提出書類管理: 所得証明書にチェック
            self.form_input_by_id(idstr="ChkTeisyutsu_3", value="1")
            self.screen_shot(f"提出書類管理_5_{atena_no}")

            # 6 提出書類管理: 「入力完了」ボタン押下
            self.click_button_by_label("入力完了")
            self.click_button_by_label("確定")

            # 7 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_7_{atena_no}")

            # 8 児童扶養手当資格管理画面: 「月額計算」ボタン押下
            self.click_button_by_label("月額計算")

            # 9 児童扶養手当資格管理画面: 「登録」ボタン押下
            self.click_button_by_label("登録")
            self.alert_ok()

            # 10 児童扶養手当資格管理画面: 表示
            # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
            self.assert_message_area("登録しました。")
            self.screen_shot(f"児童扶養手当資格管理画面_10_{atena_no}")
