import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_182501(FukushiSiteTestCaseBase):
    """TestQAC050_182501"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_182501"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC050", "YUKO_START_YMD": case_data.get("txtYukoKStrYMD", "")}
        self.exec_sqlfile("Test_QAC050_182501.sql", params=sql_params)
        super().setUp()

    # 手当額改定情報を受理後、マスタメンテナンス画面で単価を登録できることを確認する。
    def test_QAC050_182501(self):
        """マスタ更新"""

        case_data = self.test_data["TestQAC050_182501"]
        gyomu_name = case_data.get("gyomu_name", "")
        tankaKb = case_data.get("tankaKb", "")
        tankaMe = case_data.get("tankaMe", "")
        yukoKEndYMD = case_data.get("yukoKEndYMD", "")
        tankaCd = case_data.get("tankaCd", "")
        tanka = case_data.get("tanka", "")
        txtYukoKStrYMD = case_data.get("txtYukoKStrYMD", "")
        txtYukoKEndYMD = case_data.get("txtYukoKEndYMD", "")
        txtTankaRyaku = case_data.get("txtTankaRyaku", "")
        txtTankaSeishiki = case_data.get("txtTankaSeishiki", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「マスタメンテナンス」ボタン押下
        self.master_maintenance_click()

        # 3 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_3")

        # 4 サブメニュー画面: 「単価マスタメンテナンス」ボタン押下
        self.click_button_by_label("単価マスタメンテナンス")

        # 5 単価マスタ画面: 表示
        self.screen_shot("メインメニュー画面_5")

        # 6 単価マスタ画面: 事業「児童扶養手当」選択単価区分「手当月額単価」選択単価名称「手当月額_全部支給」
        self.form_input_by_id(idstr="CmbGyomu", text=gyomu_name)
        self.form_input_by_id(idstr="CmbTankaKb", text=tankaKb)
        self.form_input_by_id(idstr="CmbTankaMe", text=tankaMe)
        self.screen_shot("単価マスタ画面_6")

        # 7 単価マスタ画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 8 単価マスタ画面: 表示
        self.screen_shot("単価マスタ画面_8")

        # 9 単価マスタ画面: 単価マスタ一覧「1」Noボタン押下
        self.click_button_by_label("1")

        # 10 単価マスタ画面: 表示
        self.screen_shot("単価マスタ画面_10")

        # 11 単価マスタ画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 12 単価マスタ画面: 有効期間終了「20240331」
        self.form_input_by_id(idstr="TxtYukoKEndYMD", value=yukoKEndYMD)
        self.screen_shot("単価マスタ画面_12")

        # 13 単価マスタ画面: 「登録/復活」ボタン押下
        self.click_button_by_label("登録／復活")
        self.alert_ok()

        # 14 単価マスタ画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("単価マスタ画面_14")

        # 15 単価マスタ画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 16 単価マスタ画面: 表示
        self.screen_shot("単価マスタ画面_16")

        # 17 単価マスタ画面: 単価コード「3]単価「45000」有効期間開始「20240401」有効期間終了「（空白）」単価略称「手当月額_全部支給」単価正式名称「手当月額_全部支給」
        self.form_input_by_id(idstr="TxtTankaCd", value=tankaCd)
        self.form_input_by_id(idstr="TxtTanka", value=tanka)
        self.form_input_by_id(idstr="TxtYukoKStrYMD", value=txtYukoKStrYMD)
        self.form_input_by_id(idstr="TxtYukoKEndYMD", value=txtYukoKEndYMD)
        self.form_input_by_id(idstr="TxtTankaRyaku", value=txtTankaRyaku)
        self.form_input_by_id(idstr="TxtTankaSeishiki", value=txtTankaSeishiki)

        # 18 単価マスタ画面: 「登録/復活」ボタン押下
        self.click_button_by_label("登録／復活")
        self.alert_ok()

        # 19 単価マスタ画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("単価マスタ画面_19")

        # 20 単価マスタ画面: 事業「児童扶養手当」選択単価区分「手当月額単価」選択単価名称「手当月額_全部支給」
        self.form_input_by_id(idstr="CmbGyomu", text=gyomu_name)
        self.form_input_by_id(idstr="CmbTankaKb", text=tankaKb)
        self.form_input_by_id(idstr="CmbTankaMe", text=tankaMe)

        # 21 単価マスタ画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 22 単価マスタ画面: 表示
        self.screen_shot("単価マスタ画面_22")
