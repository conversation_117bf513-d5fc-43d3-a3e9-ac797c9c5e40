import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181511(FukushiSiteTestCaseBase):
    """TestQAC050_181511"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181511"]
        super().setUp()

    # 支払対象者の一覧等を出力できることを確認する。
    def test_QAC050_181511(self):
        """支払データ作成"""

        case_data = self.test_data["TestQAC050_181511"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：支払処理
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="支払処理")

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「支払更新処理」のNoボタン押下
        self.click_batch_job_button_by_label("支払更新処理")

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: 支払区分「定例」選択対象年月「202309」振込年月日「20230910」
        params = [
            {"title": "支払区分", "type": "select", "value": "定例"},
            {"title": "対象年月", "type": "text", "value": "202309"},
            {"title": "振込年月日", "type": "text", "value": "20230910"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()  # Button with ID: ExecuteButton

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()  # Button with ID: JobListButton

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.assert_job_normal_end(exec_datetime=exec_datetime)  # Button with ID: SearchButton
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # TODO unknown how to code this case
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「ダウンロード」ボタン押下
        # self.get_job_output_files(case_name="ジョブ実行履歴画面")
        exists_down_load_page = self.goto_output_files_dl_page(exec_datetime=exec_datetime)
        if(exists_down_load_page):
            # 作成データぺージに遷移出来た場合は、全ファイルの取得を行う。（戻値はDLしたファイル数）
            output_file_dl_count = self.get_job_output_files(case_name="dl_file")
            # 作成データページに遷移出来てる場合は戻るボタンで実行履歴に戻る。
            self.return_click()

        # 17 ダウンロード画面: 表示
        self.screen_shot("ジョブ実行履歴画面_17")

        # 18 ダウンロード画面: ダウンロードファイル一覧「1」Noボタン押下
        # self.click_button_by_label("1") # Button with ID: No1

        # 19 ジョブ実行履歴画面: 「ファイルを開く」ボタン押下

        # 20 QAC050: 表示
        # self.screen_shot("QAC050_20")

        # 21 QAC050: ×ボタン押下でファイルを閉じる

        # 22 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()  # Button with ID: ReportListButton

        # 23 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_23")

        # 24 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)  # Button with ID: SearchButton

        # 25 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_26")

        # 26 ジョブ帳票履歴画面: 「口座振込依頼書」のNoボタン押下

        # 27 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 28 口座振込依頼書（PDF）: 表示
        # self.screen_shot("口座振込依頼書（PDF）_28")

        # 29 口座振込依頼書（PDF）: ×ボタン押下でPDFを閉じる

        # 30 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_30")
