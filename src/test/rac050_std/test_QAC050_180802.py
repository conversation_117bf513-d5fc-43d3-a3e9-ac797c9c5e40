import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180802(FukushiSiteTestCaseBase):
    """TestQAC050_180802"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180802"]
        super().setUp()

    # 児童扶養手当氏名変更届、児童扶養手当住所変更届、支払金融機関変更届を提出した住民に対し、その他必要な情報の登録ができることを確認する。
    def test_QAC050_180802(self):
        """届出情報入力"""

        case_data = self.test_data["TestQAC050_180802"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.do_login()
        # 1 児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 2 児童扶養手当資格管理画面: 申請種別「変更」申請理由「市内住所変更」
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="変更")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="市内住所変更")

        # 3 児童扶養手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 4 児童扶養手当資格管理画面: 申請年月日「20230601」事由発生日「20230531」
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230601")
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20230531")

        # 5 児童扶養手当資格管理画面: 児童「1」Noボタン押下
        self.click_button_by_label("1")  # Instead of self.click_by_id("CmdNo1")

        # 6 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_6")

        # 7 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 8 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_8")

        # 9 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("提出書類管理")

        # 10 提出書類管理: 表示
        self.screen_shot("提出書類管理_10")

        # 11 提出書類管理: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 12 提出書類管理: 戸籍謄抄本にチェック
        self.form_input_by_id(idstr="ChkTeisyutsu_1", value="1")  # Not sure with self.click_by_id("ChkTeisyutsu_1")
        self.screen_shot("提出書類管理_12")

        # 13 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 14 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_14")

        # 15 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 16 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")  # Button with ID: CmdTouroku
        self.alert_ok()

        # 17 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_17")
