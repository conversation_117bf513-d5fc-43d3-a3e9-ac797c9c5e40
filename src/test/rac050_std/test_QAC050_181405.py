import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181405(FukushiSiteTestCaseBase):
    """TestQAC050_181405"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181405"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC050",
                      "TARGET_NENDO": case_data.get("nendoY", ""),
                      "TARGET_NENDO_2": case_data.get("nendoY_2", ""),
                      "INSERT_ATENA_CODE_1_1": case_data.get("atena_code_1_1", ""),
                      "INSERT_ATENA_CODE_2_1": case_data.get("atena_code_2_1", ""),
                      "INSERT_ATENA_CODE_3_1": case_data.get("atena_code_3_1", ""),
                      "INSERT_ATENA_CODE_4_1": case_data.get("atena_code_4_1", "")}
        self.exec_sqlfile("Test_QAC050_181405.sql", params=sql_params)
        super().setUp()

    # 一部支給停止除外事由届を提出した住民に対し、その他必要な情報の登録ができることを確認する。
    def test_QAC050_181405(self):
        """届出情報入力"""

        case_data = self.test_data["TestQAC050_181405"]
        atena_code_1_1 = case_data.get("atena_code_1_1", "")
        preShinseiShubetsuCmb_1 = case_data.get("preShinseiShubetsuCmb_1", "")
        preShinseiRiyuuCmb_1 = case_data.get("preShinseiRiyuuCmb_1", "")
        preTxtShinseiYMD_1 = case_data.get("preTxtShinseiYMD_1", "")
        preJyukyusyaKbnCmb_1 = case_data.get("preJyukyusyaKbnCmb_1", "")
        preTxtpreShikyuKaishiYMD_1 = case_data.get("preTxtpreShikyuKaishiYMD_1", "")
        preGengakuYMChkBox_1 = case_data.get("preGengakuYMChkBox_1", "")
        preTxtGengakuYM_1 = case_data.get("preTxtGengakuYM_1", "")
        preTxtKaitei_1 = case_data.get("preTxtKaitei_1", "")
        preTxtGaitoYMD_1 = case_data.get("preTxtGaitoYMD_1", "")
        preCmbGaitoJiyu_1 = case_data.get("preCmbGaitoJiyu_1", "")
        preTxtJiyuYMD_1 = case_data.get("preTxtJiyuYMD_1", "")
        preTxtToushoShikyuYM_1 = case_data.get("preTxtToushoShikyuYM_1", "")
        preRdoKbn1_1 = case_data.get("preRdoKbn1_1", "")
        preTxtGonenKeikaYm_1 = case_data.get("preTxtGonenKeikaYm_1", "")
        preTxtShintatsu1YMD_1 = case_data.get("preTxtShintatsu1YMD_1", "")
        preShintatsu1HanteiCmb_1 = case_data.get("preShintatsu1HanteiCmb_1", "")
        preTxtKetteiYMD_1 = case_data.get("preTxtKetteiYMD_1", "")
        preKetteiKekkaCmb_1 = case_data.get("preKetteiKekkaCmb_1", "")
        shinseiShubetsu = case_data.get("shinseiShubetsu", "")
        shinseiRiyuu = case_data.get("shinseiRiyuu", "")
        shinseiYMD = case_data.get("shinseiYMD", "")
        pTxtJiyuHasseiYMD = case_data.get("pTxtJiyuHasseiYMD", "")
        pTxtKaitei_1 = case_data.get("pTxtKaitei_1", "")
        pRdoHanteiKekka2 = case_data.get("pRdoHanteiKekka2", "")
        chkTeisyutsu = case_data.get("chkTeisyutsu", "")
        txtShintatsu1YMD = case_data.get("txtShintatsu1YMD", "")
        shintatsu1HanteiCmb = case_data.get("shintatsu1HanteiCmb", "")
        atena_code_2_1 = case_data.get("atena_code_2_1", "")
        preTxtTJogaiTeishutsuYmd_2 = case_data.get("preTxtTJogaiTeishutsuYmd_2", "")
        atena_code_3_1 = case_data.get("atena_code_3_1", "")
        shinseiShubetsu_3 = case_data.get("shinseiShubetsu_3", "")
        shinseiRiyuu_3 = case_data.get("shinseiRiyuu_3", "")
        preTxtShinseiYMD_3 = case_data.get("preTxtShinseiYMD_3", "")
        preTxtpreShikyuKaishiYMD_3 = case_data.get("preTxtpreShikyuKaishiYMD_3", "")
        preTxtGengakuYM_3 = case_data.get("preTxtGengakuYM_3", "")
        preTxtKaitei_3 = case_data.get("preTxtKaitei_3", "")
        preTxtGaitoYMD_3 = case_data.get("preTxtGaitoYMD_3", "")
        preTxtJiyuYMD_3 = case_data.get("preTxtJiyuYMD_3", "")
        preTxtToushoShikyuYM_3 = case_data.get("preTxtToushoShikyuYM_3", "")
        preTxtTJogaiTeishutsuYmd_3 = case_data.get("preTxtTJogaiTeishutsuYmd_3", "")
        preTxtShintatsu1YMD_3 = case_data.get("preTxtShintatsu1YMD_3", "")
        preTxtKetteiYMD_3 = case_data.get("preTxtKetteiYMD_3", "")
        pRdoHanteiKekka1 = case_data.get("pRdoHanteiKekka1", "")
        atena_code_4_1 = case_data.get("atena_code_4_1", "")

# ①適用除外事由届が未提出 -s
        self.do_login()
        # 1メインメニュー画面: 表示
        self.screen_shot("1_メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("1_個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.screen_shot("1_個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code_1_1)

        # 6 受給状況画面: 表示
        self.screen_shot("1_受給状況画面_6")

        # 7 受給状況画面: 「児童扶養手当」ボタン押下
        self.click_button_by_label("児童扶養手当")

        # 8 児童扶養手当資格管理画面: 表示
        self.screen_shot("1_児童扶養手当資格管理画面_8")

        # ＜対象者の条件＞
        # ・児童扶養手当の資格（児童2人）を持っている住民
        # ・本人と児童がいる世帯の住民
        # ・児童扶養手当の除外届情報があり
        # ・児童扶養手当の除外届情報の届出年月日が未登録
        self.click_button_by_label("申請内容入力")
        # 申請種別
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=preShinseiShubetsuCmb_1)
        # 申請理由
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=preShinseiRiyuuCmb_1)
        self.click_button_by_label("確定")
        # 請求年月日
        self.form_input_by_id(idstr="TxtShinseiYMD", value=preTxtShinseiYMD_1)
        # 受給者区分
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text=preJyukyusyaKbnCmb_1)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value=preTxtpreShikyuKaishiYMD_1)
        # 減額開始年月
        self.form_input_by_id(idstr="GengakuYMChkBox", value=preGengakuYMChkBox_1)
        self.form_input_by_id(idstr="TxtGengakuYM", value=preTxtGengakuYM_1)
        # 開始年月
        self.form_input_by_id(idstr="TxtKaitei", value=preTxtKaitei_1)
        self.click_button_by_label("児童追加")
        self.click_button_by_label("2")
        # 該当年月日
        self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
        # 該当事由
        self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
        self.click_button_by_label("入力完了")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("3")
        # 該当年月日
        self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
        # 該当事由
        self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
        self.click_button_by_label("入力完了")
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="除外届情報")
        self.click_button_by_label("追加")
        # 区分
        self.form_input_by_id(idstr="RdoKbn1", value=preRdoKbn1_1)
        # 五年満了年月
        self.form_input_by_id(idstr="TxtGonenKeikaYm", value=preTxtGonenKeikaYm_1)
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.return_click()
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達入力")
        # 進達年月日
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=preTxtShintatsu1YMD_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達結果入力")
        # 進達結果
        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=preShintatsu1HanteiCmb_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("決定内容入力")
        # 決定年月日
        self.form_input_by_id(idstr="TxtKetteiYMD", value=preTxtKetteiYMD_1)
        # 決定結果
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=preKetteiKekkaCmb_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        # 9 児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 児童扶養手当資格管理画面: 申請種別「減額適用」申請理由「減額適用」
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinseiShubetsu)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinseiRiyuu)

        # 11 児童扶養手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 児童扶養手当資格管理画面: 請求年月日「20230801」 事由発生年月日「20230801」 改定年月「202309」
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinseiYMD)
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value=pTxtJiyuHasseiYMD)
        self.form_input_by_id(idstr="TxtKaitei", value=pTxtKaitei_1)

        # 13 児童扶養手当資格管理画面: 児童「1」Noボタン押下
        self.click_button_by_label("1")

        # 14 支給対象児童入力画面: 表示
        self.screen_shot("1_支給対象児童入力画面_14")

        # 15 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 16 児童扶養手当資格管理画面: 表示
        self.screen_shot("1_児童扶養手当資格管理画面_16")

        # 17 児童扶養手当資格管理画面: 「除外届情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="除外届情報")
        
        # 18 減額適用除外届管理情報画面: 表示
        self.screen_shot("1_減額適用除外届管理情報画面_18")

        # 19 減額適用除外届管理情報画面: 減額適用除外届履歴一覧「1」Noボタン押下
        self.click_button_by_label("1")
        self.screen_shot("1_減額適用除外届管理情報画面_20")

        # 20 減額適用除外届管理情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 21 減額適用除外届管理情報画面: 審査結果の適用にチェック
        self.form_input_by_id(idstr="RdoHanteiKekka2", value=pRdoHanteiKekka2)
        self.screen_shot("1_減額適用除外届管理情報画面_21")

        # 22 減額適用除外届管理情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 23 減額適用除外届管理情報画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("1_減額適用除外届管理情報画面_23")

        # 24 減額適用除外届管理情報画面: 「戻る」ボタン押下
        self.return_click()

        # 25 児童扶養手当資格管理画面: 表示
        self.screen_shot("1_児童扶養手当資格管理画面_25")

        # 26 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="提出書類管理")

        # 27 提出書類管理: 表示
        self.screen_shot("1_提出書類管理_27")

        # 28 提出書類管理: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 29 提出書類管理: 戸籍謄抄本にチェック
        self.form_input_by_id(idstr="ChkTeisyutsu_1", value=chkTeisyutsu)
        self.screen_shot("1_提出書類管理_30")

        # 30 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 31 児童扶養手当資格管理画面: 表示
        self.screen_shot("1_児童扶養手当資格管理画面_31")

        # 32 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 33 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 34 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("1_児童扶養手当資格管理画面_34")

        # 後続シナリオのため進達入力をしておく
        self.click_button_by_label("本庁進達入力")
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=txtShintatsu1YMD)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達結果入力")
        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=shintatsu1HanteiCmb)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
# ①適用除外事由届が未提出 -e

# ②適用除外事由届が提出済、かつ書類不備なし、かつ適用除外審査で「適用」にする場合 -s
        self.do_login()
        # 1メインメニュー画面: 表示
        self.screen_shot("2_メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("2_個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.screen_shot("2_個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code_2_1)

        # 6 受給状況画面: 表示
        self.screen_shot("2_受給状況画面_6")

        # 7 受給状況画面: 「児童扶養手当」ボタン押下
        self.click_button_by_label("児童扶養手当")

        # 8 児童扶養手当資格管理画面: 表示
        self.screen_shot("2_児童扶養手当資格管理画面_8")

        # ＜対象者の条件＞
        # ・児童扶養手当の資格（児童2人）を持っている住民
        # ・本人と児童がいる世帯の住民
        # ・児童扶養手当の除外届情報があり
        # ・児童扶養手当の除外届情報の届出年月日が登録済、かつ審査結果が未登録
        self.click_button_by_label("申請内容入力")
        # 申請種別
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=preShinseiShubetsuCmb_1)
        # 申請理由
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=preShinseiRiyuuCmb_1)
        self.click_button_by_label("確定")
        # 請求年月日
        self.form_input_by_id(idstr="TxtShinseiYMD", value=preTxtShinseiYMD_1)
        # 受給者区分
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text=preJyukyusyaKbnCmb_1)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value=preTxtpreShikyuKaishiYMD_1)
        # 減額開始年月
        self.form_input_by_id(idstr="GengakuYMChkBox", value=preGengakuYMChkBox_1)
        self.form_input_by_id(idstr="TxtGengakuYM", value=preTxtGengakuYM_1)
        # 開始年月
        self.form_input_by_id(idstr="TxtKaitei", value=preTxtKaitei_1)
        self.click_button_by_label("児童追加")
        self.click_button_by_label("2")
        # 該当年月日
        self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
        # 該当事由
        self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
        self.click_button_by_label("入力完了")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("3")
        # 該当年月日
        self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
        # 該当事由
        self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
        self.click_button_by_label("入力完了")
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="除外届情報")
        self.click_button_by_label("追加")
        # 区分
        self.form_input_by_id(idstr="RdoKbn1", value=preRdoKbn1_1)
        # 五年満了年月
        self.form_input_by_id(idstr="TxtGonenKeikaYm", value=preTxtGonenKeikaYm_1)
        # 届出年月日
        self.form_input_by_id(idstr="TxtTJogaiTeishutsuYmd", value=preTxtTJogaiTeishutsuYmd_2)
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.return_click()
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達入力")
        # 進達年月日
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=preTxtShintatsu1YMD_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達結果入力")
        # 進達結果
        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=preShintatsu1HanteiCmb_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("決定内容入力")
        # 決定年月日
        self.form_input_by_id(idstr="TxtKetteiYMD", value=preTxtKetteiYMD_1)
        # 決定結果
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=preKetteiKekkaCmb_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        # 9 児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 児童扶養手当資格管理画面: 申請種別「減額適用」申請理由「減額適用」
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinseiShubetsu)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinseiRiyuu)

        # 11 児童扶養手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 児童扶養手当資格管理画面: 請求年月日「20230801」 事由発生年月日「20230801」 改定年月「202309」
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinseiYMD)
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value=pTxtJiyuHasseiYMD)
        self.form_input_by_id(idstr="TxtKaitei", value=pTxtKaitei_1)

        # 13 児童扶養手当資格管理画面: 児童「1」Noボタン押下
        self.click_button_by_label("1")

        # 14 支給対象児童入力画面: 表示
        self.screen_shot("2_支給対象児童入力画面_14")

        # 15 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 16 児童扶養手当資格管理画面: 表示
        self.screen_shot("2_児童扶養手当資格管理画面_16")

        # 17 児童扶養手当資格管理画面: 「除外届情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="除外届情報")
        
        # 18 減額適用除外届管理情報画面: 表示
        self.screen_shot("2_減額適用除外届管理情報画面_18")

        # 19 減額適用除外届管理情報画面: 減額適用除外届履歴一覧「1」Noボタン押下
        self.click_button_by_label("1")
        self.screen_shot("2_減額適用除外届管理情報画面_20")

        # 20 減額適用除外届管理情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 21 減額適用除外届管理情報画面: 審査結果の適用にチェック
        self.form_input_by_id(idstr="RdoHanteiKekka2", value=pRdoHanteiKekka2)
        self.screen_shot("2_減額適用除外届管理情報画面_21")

        # 22 減額適用除外届管理情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 23 減額適用除外届管理情報画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("2_減額適用除外届管理情報画面_23")

        # 24 減額適用除外届管理情報画面: 「戻る」ボタン押下
        self.return_click()

        # 25 児童扶養手当資格管理画面: 表示
        self.screen_shot("2_児童扶養手当資格管理画面_25")

        # 26 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="提出書類管理")

        # 27 提出書類管理: 表示
        self.screen_shot("2_提出書類管理_27")

        # 28 提出書類管理: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 29 提出書類管理: 戸籍謄抄本にチェック
        self.form_input_by_id(idstr="ChkTeisyutsu_1", value=chkTeisyutsu)
        self.screen_shot("2_提出書類管理_30")

        # 30 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 31 児童扶養手当資格管理画面: 表示
        self.screen_shot("2_児童扶養手当資格管理画面_31")

        # 32 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 33 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 34 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("2_児童扶養手当資格管理画面_34")

        # 後続シナリオのため進達入力をしておく
        self.click_button_by_label("本庁進達入力")
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=txtShintatsu1YMD)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達結果入力")
        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=shintatsu1HanteiCmb)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
# ②適用除外事由届が提出済、かつ書類不備なし、かつ適用除外審査で「適用」にする場合 -e

# ③適用除外事由届が提出済、かつ書類不備なし、かつ適用除外審査で「適用除外」にする場合 -s
        self.do_login()
        # 1メインメニュー画面: 表示
        self.screen_shot("3_メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("3_個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.screen_shot("3_個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code_3_1)

        # 6 受給状況画面: 表示
        self.screen_shot("3_受給状況画面_6")

        # 7 受給状況画面: 「児童扶養手当」ボタン押下
        self.click_button_by_label("児童扶養手当")

        # 8 児童扶養手当資格管理画面: 表示
        self.screen_shot("3_児童扶養手当資格管理画面_8")

        # ＜対象者の条件＞
        # ・児童扶養手当の資格（児童2人）を持っている住民
        # ・本人と児童がいる世帯の住民
        # ・児童扶養手当の除外届情報があり
        # ・児童扶養手当の除外届情報の届出年月日が登録済、かつ審査結果が未登録
        self.click_button_by_label("申請内容入力")
        # 申請種別
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=preShinseiShubetsuCmb_1)
        # 申請理由
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=preShinseiRiyuuCmb_1)
        self.click_button_by_label("確定")
        # 請求年月日
        self.form_input_by_id(idstr="TxtShinseiYMD", value=preTxtShinseiYMD_3)
        # 受給者区分
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text=preJyukyusyaKbnCmb_1)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value=preTxtpreShikyuKaishiYMD_3)
        # 減額開始年月
        self.form_input_by_id(idstr="GengakuYMChkBox", value=preGengakuYMChkBox_1)
        self.form_input_by_id(idstr="TxtGengakuYM", value=preTxtGengakuYM_3)
        # 改定年月
        self.form_input_by_id(idstr="TxtKaitei", value=preTxtKaitei_3)
        self.click_button_by_label("児童追加")
        self.click_button_by_label("2")
        # 該当年月日
        self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_3)
        # 該当事由
        self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_3)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_3)
        self.click_button_by_label("入力完了")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("3")
        # 該当年月日
        self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_3)
        # 該当事由
        self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_3)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_3)
        self.click_button_by_label("入力完了")
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="除外届情報")
        self.click_button_by_label("追加")
        # 区分
        self.form_input_by_id(idstr="RdoKbn1", value=preRdoKbn1_1)
        # 五年満了年月
        self.form_input_by_id(idstr="TxtGonenKeikaYm", value=preTxtGonenKeikaYm_1)
        # 届出年月日
        self.form_input_by_id(idstr="TxtTJogaiTeishutsuYmd", value=preTxtTJogaiTeishutsuYmd_3)
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.return_click()
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達入力")
        # 進達年月日
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=preTxtShintatsu1YMD_3)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達結果入力")
        # 進達結果
        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=preShintatsu1HanteiCmb_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("決定内容入力")
        # 決定年月日
        self.form_input_by_id(idstr="TxtKetteiYMD", value=preTxtKetteiYMD_3)
        # 決定結果
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=preKetteiKekkaCmb_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        # 9 児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 児童扶養手当資格管理画面: 申請種別「減額適用除外」申請理由「雇用」
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinseiShubetsu_3)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinseiRiyuu_3)

        # 11 児童扶養手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 児童扶養手当資格管理画面: 請求年月日「20230801」 事由発生年月日「20230801」 改定年月「202309」
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinseiYMD)
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value=pTxtJiyuHasseiYMD)
        self.form_input_by_id(idstr="TxtKaitei", value=pTxtKaitei_1)

        # 13 児童扶養手当資格管理画面: 児童「1」Noボタン押下
        self.click_button_by_label("1")

        # 14 支給対象児童入力画面: 表示
        self.screen_shot("3_支給対象児童入力画面_14")

        # 15 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 16 児童扶養手当資格管理画面: 表示
        self.screen_shot("3_児童扶養手当資格管理画面_16")

        # 17 児童扶養手当資格管理画面: 「除外届情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="除外届情報")
        
        # 18 減額適用除外届管理情報画面: 表示
        self.screen_shot("3_減額適用除外届管理情報画面_18")

        # 19 減額適用除外届管理情報画面: 減額適用除外届履歴一覧「1」Noボタン押下
        self.click_button_by_label("1")
        self.screen_shot("3_減額適用除外届管理情報画面_20")

        # 20 減額適用除外届管理情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 21 減額適用除外届管理情報画面: 審査結果の適用除外にチェック
        self.form_input_by_id(idstr="RdoHanteiKekka1", value=pRdoHanteiKekka1)
        self.screen_shot("3_減額適用除外届管理情報画面_21")

        # 22 減額適用除外届管理情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 23 減額適用除外届管理情報画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("3_減額適用除外届管理情報画面_23")

        # 24 減額適用除外届管理情報画面: 「戻る」ボタン押下
        self.return_click()

        # 25 児童扶養手当資格管理画面: 表示
        self.screen_shot("3_児童扶養手当資格管理画面_25")

        # 26 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="提出書類管理")

        # 27 提出書類管理: 表示
        self.screen_shot("3_提出書類管理_27")

        # 28 提出書類管理: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 29 提出書類管理: 戸籍謄抄本にチェック
        self.form_input_by_id(idstr="ChkTeisyutsu_1", value=chkTeisyutsu)
        self.screen_shot("3_提出書類管理_30")

        # 30 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 31 児童扶養手当資格管理画面: 表示
        self.screen_shot("3_児童扶養手当資格管理画面_31")

        # 32 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 33 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 34 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("3_児童扶養手当資格管理画面_34")

        # 後続シナリオのため進達入力をしておく
        self.click_button_by_label("本庁進達入力")
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=txtShintatsu1YMD)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達結果入力")
        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=shintatsu1HanteiCmb)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
# ③適用除外事由届が提出済、かつ書類不備なし、かつ適用除外審査で「適用除外」にする場合 -e

# ④適用除外事由届が提出済、かつ書類不備ありの場合 -s
        self.do_login()
        # 1メインメニュー画面: 表示
        self.screen_shot("4_メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("4_個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.screen_shot("4_個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code_4_1)

        # 6 受給状況画面: 表示
        self.screen_shot("4_受給状況画面_6")

        # 7 受給状況画面: 「児童扶養手当」ボタン押下
        self.click_button_by_label("児童扶養手当")

        # 8 児童扶養手当資格管理画面: 表示
        self.screen_shot("4_児童扶養手当資格管理画面_8")

        # ＜対象者の条件＞
        # ・児童扶養手当の資格（児童2人）を持っている住民
        # ・本人と児童がいる世帯の住民
        # ・児童扶養手当の除外届情報があり
        # ・児童扶養手当の除外届情報の届出年月日が登録済、かつ審査結果が未登録
        self.click_button_by_label("申請内容入力")
        # 申請種別
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=preShinseiShubetsuCmb_1)
        # 申請理由
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=preShinseiRiyuuCmb_1)
        self.click_button_by_label("確定")
        # 請求年月日
        self.form_input_by_id(idstr="TxtShinseiYMD", value=preTxtShinseiYMD_1)
        # 受給者区分
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text=preJyukyusyaKbnCmb_1)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value=preTxtpreShikyuKaishiYMD_1)
        # 減額開始年月
        self.form_input_by_id(idstr="GengakuYMChkBox", value=preGengakuYMChkBox_1)
        self.form_input_by_id(idstr="TxtGengakuYM", value=preTxtGengakuYM_1)
        # 開始年月
        self.form_input_by_id(idstr="TxtKaitei", value=preTxtKaitei_1)
        self.click_button_by_label("児童追加")
        self.click_button_by_label("2")
        # 該当年月日
        self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
        # 該当事由
        self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
        self.click_button_by_label("入力完了")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("3")
        # 該当年月日
        self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
        # 該当事由
        self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
        self.click_button_by_label("入力完了")
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="除外届情報")
        self.click_button_by_label("追加")
        # 区分
        self.form_input_by_id(idstr="RdoKbn1", value=preRdoKbn1_1)
        # 五年満了年月
        self.form_input_by_id(idstr="TxtGonenKeikaYm", value=preTxtGonenKeikaYm_1)
        # 届出年月日
        self.form_input_by_id(idstr="TxtTJogaiTeishutsuYmd", value=preTxtTJogaiTeishutsuYmd_2)
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.return_click()
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達入力")
        # 進達年月日
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=preTxtShintatsu1YMD_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達結果入力")
        # 進達結果
        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=preShintatsu1HanteiCmb_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("決定内容入力")
        # 決定年月日
        self.form_input_by_id(idstr="TxtKetteiYMD", value=preTxtKetteiYMD_1)
        # 決定結果
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=preKetteiKekkaCmb_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        # 9 児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 児童扶養手当資格管理画面: 申請種別「減額適用」申請理由「減額適用」
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinseiShubetsu)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinseiRiyuu)

        # 11 児童扶養手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 児童扶養手当資格管理画面: 請求年月日「20230801」 事由発生年月日「20230801」 改定年月「202309」
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinseiYMD)
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value=pTxtJiyuHasseiYMD)
        self.form_input_by_id(idstr="TxtKaitei", value=pTxtKaitei_1)

        # 13 児童扶養手当資格管理画面: 児童「1」Noボタン押下
        self.click_button_by_label("1")

        # 14 支給対象児童入力画面: 表示
        self.screen_shot("4_支給対象児童入力画面_14")

        # 15 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 16 児童扶養手当資格管理画面: 表示
        self.screen_shot("4_児童扶養手当資格管理画面_16")

        # 17 児童扶養手当資格管理画面: 「除外届情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="除外届情報")
        
        # 18 減額適用除外届管理情報画面: 表示
        self.screen_shot("4_減額適用除外届管理情報画面_18")

        # 19 減額適用除外届管理情報画面: 減額適用除外届履歴一覧「1」Noボタン押下
        self.click_button_by_label("1")
        self.screen_shot("4_減額適用除外届管理情報画面_20")

        # 20 減額適用除外届管理情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 21 減額適用除外届管理情報画面: 審査結果の適用にチェック
        self.form_input_by_id(idstr="RdoHanteiKekka2", value=pRdoHanteiKekka2)
        self.screen_shot("4_減額適用除外届管理情報画面_21")

        # 22 減額適用除外届管理情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 23 減額適用除外届管理情報画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("4_減額適用除外届管理情報画面_23")

        # 24 減額適用除外届管理情報画面: 「戻る」ボタン押下
        self.return_click()

        # 25 児童扶養手当資格管理画面: 表示
        self.screen_shot("4_児童扶養手当資格管理画面_25")

        # 26 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="提出書類管理")

        # 27 提出書類管理: 表示
        self.screen_shot("4_提出書類管理_27")

        # 28 提出書類管理: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 29 提出書類管理: 戸籍謄抄本にチェック
        self.form_input_by_id(idstr="ChkTeisyutsu_1", value=chkTeisyutsu)
        self.screen_shot("4_提出書類管理_30")

        # 30 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 31 児童扶養手当資格管理画面: 表示
        self.screen_shot("4_児童扶養手当資格管理画面_31")

        # 32 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 33 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 34 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("4_児童扶養手当資格管理画面_34")

        # 後続シナリオのため進達入力をしておく
        self.click_button_by_label("本庁進達入力")
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=txtShintatsu1YMD)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達結果入力")
        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=shintatsu1HanteiCmb)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
# ④適用除外事由届が提出済、かつ書類不備ありの場合 -e
