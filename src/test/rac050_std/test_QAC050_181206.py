import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181206(FukushiSiteTestCaseBase):
    """TestQAC050_181206"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181206"]
        super().setUp()

    # 障害等認定届や在留期間延長届を提出した住民に対し、障害等認定審査ができることを確認する。
    def test_QAC050_181206(self):
        """障害等認定処理"""

        case_data = self.test_data["TestQAC050_181206"]
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230701")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230701")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")

        # 1 児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 2 児童扶養手当資格管理画面: 決定年月日「20230702」決定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230702")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.screen_shot("児童扶養手当資格管理画面_2")

        # 3 児童扶養手当資格管理画面: 児童「1」Noボタン押下
        self.click_button_by_label("1")

        # 4 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_4")

        # 5 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 6 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_6")

        # 7 児童扶養手当資格管理画面: 「公的年金等停止額」ボタン押下
        self.click_button_by_label("公的年金等停止額")

        # 8 年金登録確認画面: 表示
        self.screen_shot("年金登録確認画面_8")

        # 9 年金登録確認画面: 「戻る」ボタン押下
        self.return_click()

        # 10 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_10")

        # 11 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 12 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 13 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_13")
