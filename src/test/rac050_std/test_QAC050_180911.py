import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180911(FukushiSiteTestCaseBase):
    """TestQAC050_180911"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180911"]
        super().setUp()

    # 証書交付年月日を登録できることを確認する。
    def test_QAC050_180911(self):
        """支給停止解除通知書等作成"""

        case_data = self.test_data["TestQAC050_180911"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.do_login()
        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 証書交付年月日「20230602」
        self.form_input_by_id(idstr="TxtShoushoHenpuYMD", value="20230602")
        self.screen_shot("児童扶養手当資格管理画面_2")

        # 3 児童扶養手当資格管理画面: 「所得判定詳細情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("所得判定詳細情報")

        # 4 所得判定詳細情報画面: 表示
        self.screen_shot("所得判定詳細情報画面_4")

        # 5 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 6 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_6")

        # 7 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 8 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 9 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_9")
