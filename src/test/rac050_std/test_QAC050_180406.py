import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180406(FukushiSiteTestCaseBase):
    """TestQAC050_180406"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180406"]
        super().setUp()

    # 額改定届を提出した住民に対し決定登録ができることを確認する。
    def test_QAC050_180406(self):
        """額改定処理"""

        case_data = self.test_data["TestQAC050_180406"]
        atena_code = case_data.get("atena_code", "")
        ketteiymd = case_data.get("ketteiymd", "")
        ketteikekka = case_data.get("ketteikekka", "")
        atena_code2 = case_data.get("atena_code2", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.do_login()

        # 進達関連
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value=ketteiymd)
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value=ketteiymd)
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")

        # 1 児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")  # Button with ID: CmdKettei

        # 2 児童扶養手当資格管理画面: 決定年月日「20230602」決定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value=ketteiymd)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=ketteikekka)
        self.screen_shot("児童扶養手当資格管理画面_2")

        # 3 児童扶養手当資格管理画面: 児童のNoボタン押下
        self.click_by_id("CmdNo1")

        # 4 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_4")

        # 5 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 6 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_6")

        # 7 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")  # Button with ID: CmdGetsugakuKeisan

        # 8 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")  # Button with ID: CmdTouroku
        self.alert_ok()

        # 9 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_9")


        #2人目の情報入力
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code2, gyoumu_code="QAC050")
        # 進達関連
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value=ketteiymd)
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value=ketteiymd)
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
        self.click_button_by_label("決定内容入力")  # Button with ID: CmdKettei
        self.form_input_by_id(idstr="TxtKetteiYMD", value=ketteiymd)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=ketteikekka)
        self.click_button_by_label("月額計算")  # Button with ID: CmdGetsugakuKeisan
        self.click_button_by_label("登録")  # Button with ID: CmdTouroku
        self.alert_ok()
