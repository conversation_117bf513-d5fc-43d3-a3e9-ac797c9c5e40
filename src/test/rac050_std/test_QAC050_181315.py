import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181315(FukushiSiteTestCaseBase):
    """TestQAC050_181315"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181315"]
        super().setUp()

    # 支給停止解除通知書を出力できることを確認する。
    def test_QAC050_181315(self):
        """支給停止解除通知書等作成"""

        case_data = self.test_data["TestQAC050_181315"]
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        pOutputKubun = case_data.get("pOutputKubun", "")
        pStartPage = case_data.get("pStartPage", "")
        pEndPage = case_data.get("pEndPage", "")
        pHakkoYMD = case_data.get("pHakkoYMD", "")
        pNextShiharaiYMD = case_data.get("pNextShiharaiYMD", "")
        pHaishiKaijyoYMD = case_data.get("pHaishiKaijyoYMD", "")
        pHakkoYMD_2 = case_data.get("pHakkoYMD_2", "")

        self.do_login()
        # 1	メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2	メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3	バッチ起動画面: 表示
        self.screen_shot("メインメニュー画面_2")

        # 4	バッチ起動画面: "業務：児童 事業：児童扶養手当 処理区分：年次 処理分類：現況更新処理"
        self.select_by_id("GyomuSelect", text=pGyomuSelect)
        self.select_by_id("JigyoSelect", text=pJigyoSelect)
        self.select_by_id("ShoriKubunSelect", text=pShoriKubunSelect)
        self.select_by_id("ShoriBunruiSelect", text=pShoriBunruiSelect)

        # 5	バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6	バッチ起動画面: 「現況届一括更新処理_通知書」のNoボタン押下
        self.click_batch_job_button_by_label("現況届一括更新処理_通知書")

        # 7	バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8	バッチ起動画面: "出力区分「0」 開始頁「000001」 終了頁「999999」 発行年月日「20230902」 次回手当支払年月日「20231101」 差止解除年月日「20231031」"
        params = [
            {"title": "出力区分", "type": "text", "value": pOutputKubun},
            {"title": "開始頁", "type": "text", "value": pStartPage},
            {"title": "終了頁", "type": "text", "value": pEndPage},
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD},
            {"title": "次回手当支払年月日", "type": "text", "value": pNextShiharaiYMD},
            {"title": "差止解除年月日", "type": "text", "value": pHaishiKaijyoYMD}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9	バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示 メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # Assert: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # self.screen_shot("バッチ起動画面_15")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下

        # 19 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「支給停止解除通知書」のNoボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 22 支給停止解除通知書（PDF）: 表示
        # self.screen_shot("支給停止解除通知書（PDF）_22")

        # 23 支給停止解除通知書（PDF）: 2ページ目表示	裏面が出力されていることを確認する。

        # 24 支給停止解除通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 25 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_25")

        # 26 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 27 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_27")

        # 28 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 29 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_29")

        # 30 バッチ起動画面: 「現況届一括更新処理_証書」のNoボタン押下
        self.click_batch_job_button_by_label("現況届一括更新処理_証書")

        # 31 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_31")

        # 32 バッチ起動画面: 発行年月日「20230902」
        params = [
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD_2}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_32")

        # 33 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 34 バッチ起動画面: 表示 メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_34")

        # 35 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 36 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_36")

        # 37 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 38 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_38")

        # 39 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # Assert: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # self.screen_shot("ジョブ実行履歴画面_39")

        # 40 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 41 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_41")

        # 42 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 43 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_43")

        # 44 ジョブ帳票履歴画面: 「児童扶養手当証書」のNoボタン押下

        # 45 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 46 児童扶養手当証書（PDF）: 表示

        # 47 児童扶養手当証書（PDF）: 2ページ目表示	裏面が出力されていることを確認する

        # 48 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # 49 ジョブ帳票履歴画面	表示
        self.screen_shot("ジョブ帳票履歴画面_49")
