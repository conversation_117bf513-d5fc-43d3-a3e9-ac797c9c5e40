import time
import unittest
import datetime
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181514(FukushiSiteTestCaseBase):
    """TestQAC050_181514"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181514"]
        super().setUp()

    # 振込不能者の口座情報を変更し、再び支払データの作成を行うことができることを確認する。
    def test_QAC050_181514(self):
        """振込不能対応"""

        case_data = self.test_data["TestQAC050_181514"]
        atena_code = case_data.get("atena_code", "")
        report_name = case_data.get("report_name", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")
        date = datetime.date.today()
        today = format(date, '%Y%m%d')

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        self.click_button_by_label("修正")
        self.open_common_buttons_area()
        self.click_button_by_label("住所管理")
        self.click_button_by_label("追加")
        self.click_button_by_label("住記情報索引")
        self.alert_ok()
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()

        # 1 児童扶養手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_2")

        # 3 帳票印刷画面:
        # 「住所変更（転出・転入）・金融機関変更届」行の印刷チェックボックス選択
        # 「住所変更（転出・転入）・金融機関変更届」行の発行年月日チェックボックス選択
        # 「住所変更（転出・転入）・金融機関変更届」行の発行年月日「20230601」
        self.switch_online_report_type("申請書")
        exec_params = [
            {
                "report_name": report_name,
                "params":[
                    {"title": "印刷種別", "value":"1","is_no_print":"1"},
                ]
            }
        ]
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_3")
        self.assert_message_area("プレビューを表示しました")

        # 4 帳票印刷画面: 「印刷」ボタン押下

        # 5 帳票印刷画面: 受給資格者台帳「ファイルを開く(O)」ボタンを押下

        # 6 住所変更（転出・転入）・金融機関変更届（PDF）: 表示
        #self.screen_shot("住所変更（転出・転入）・金融機関変更届（PDF）_6")

        # 7 住所変更（転出・転入）・金融機関変更届（PDF）: ×ボタン押下でPDFを閉じる
        # TODO: I don't know which function to close the pdf tab

        # 8 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 9 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_9")

        # 10 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 11 児童扶養手当資格管理画面: 「口座情報」ボタン押下
        self.click_button_by_label("口座情報")

        # 12 口座情報画面: 表示
        self.screen_shot("口座情報画面_12")

        # 13 口座情報画面: 「口座変更」ボタン押下
        self.click_button_by_label("口座変更")

        # 14 口座情報画面: 有効期間開始「20230901」、金融機関コード「0002」、支店コード「002」、口座種別「普通」選択、口座番号「2345678」、口座名義人カナ「（住記のカナ氏名）」、口座名義人漢字「（住記の漢字氏名」
        self.entry_kouza_info(
            # start_ymd=today,
            ginko_code="0002",
            shiten_code="047",
            kouza_shubetsu_text="普通",
            kouza_bango="2345678",
            koukai=True
        )
        self.screen_shot("口座情報画面_14")

        # 15 口座情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 16 口座情報画面: 表示
        self.screen_shot("口座情報画面_16")

        # 17 口座情報画面: 「戻る」ボタン押下
        self.return_click()

        # 18 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_18")

        # 19 児童扶養手当資格管理画面: 「支払履歴」ボタン押下
        self.click_button_by_label("支払履歴")

        # 20 支払履歴画面: 表示
        self.screen_shot("支払履歴画面_20")

        # 21 支払履歴画面: 支払履歴一覧「6」Noボタン押下
        self.click_button_by_label("6") #変更有

        # 22 支払実績登録画面: 表示
        self.screen_shot("支払実績登録画面_20")

        # 23 支払実績登録画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 24 支払実績登録画面: 支払年月日「20230910」
        self.form_input_by_id(idstr="TbxPayDate", value="20230910")
        # self.form_input_by_id(idstr="TbxDecisionDate", value="20230910")
        self.screen_shot("支払実績登録画面_24")

        # 25 支払実績登録画面: 「最新口座」ボタン押下
        self.click_button_by_label("最新口座")
        self.screen_shot("支払実績登録画面_25")

        # 26 支払実績登録画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 27 支払実績登録画面: 表示
        self.screen_shot("支払実績登録画面_27")

        # 28 支払実績登録画面: 「戻る」ボタン押下
        self.return_click()

        # 29 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 30 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 31 児童扶養手当資格管理画面: 表示
        self.assert_message_area("登録しました")
        self.screen_shot("児童扶養手当資格管理画面_31")

        # 32 バッチ起動画面: 表示
        self.do_login()
        self.batch_kidou_click()
        self.screen_shot("バッチ起動画面_32")

        # 33 バッチ起動画面: 業務：児童、事業：児童扶養手当、処理区分：月次処理、分類：支払処理
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="支払処理")

        # 34 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_34")

        # 35 バッチ起動画面: 「全銀ファイル再作成処理」のNoボタン押下
        self.click_batch_job_button_by_label("全銀ファイル再作成処理")

        # 36 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_36")

        # 37 バッチ起動画面: 支払区分「定例」選択、対象年月「202309」、振込年月日「20230910」
        params = [
            {"title": "支払区分", "type": "select", "value": "定例"},
            {"title": "対象年月", "type": "text", "value": "202309"},
            {"title": "振込年月日", "type": "text", "value": "20230910"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_37")

        # 38 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()  # Button with ID: ExecuteButton

        # 39 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_39")

        # 40 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()  # Button with ID: JobListButton

        # 41 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_41")

        # 42 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.assert_job_normal_end(exec_datetime=exec_datetime)  # Button with ID: SearchButton
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 43 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_43")

        # 44 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # TODO unknown how to code this case
        self.screen_shot("ジョブ実行履歴画面_44")

        # 45 ジョブ実行履歴画面: 「ダウンロード」ボタン押下
        # self.get_job_output_files(case_name="ジョブ実行履歴画面")
        exists_down_load_page = self.goto_output_files_dl_page(exec_datetime=exec_datetime)
        if(exists_down_load_page):
            # 作成データぺージに遷移出来た場合は、全ファイルの取得を行う。（戻値はDLしたファイル数）
            output_file_dl_count = self.get_job_output_files(case_name="dl_file")
            # 作成データページに遷移出来てる場合は戻るボタンで実行履歴に戻る。
            self.return_click()

        # 46 ダウンロード画面: 表示
        self.screen_shot("ジョブ実行履歴画面_17")

        # 47 ダウンロード画面: ダウンロードファイル一覧「1」Noボタン押下
        # self.click_button_by_label("1") # Button with ID: No1

        # 48 ジョブ実行履歴画面: 「ファイルを開く」ボタン押下

        # 49 QAC050: 表示
        # self.screen_shot("QAC050_49")

        # 50 QAC050: ×ボタン押下でファイルを閉じる

        # 51 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()  # Button with ID: ReportListButton

        # 52 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_52")

        # 53 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)  # Button with ID: SearchButton

        # 54 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_54")

        # 55 ジョブ帳票履歴画面: 「口座振込依頼書」のNoボタン押下

        # 56 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 57 口座振込依頼書（PDF）: 表示
        #self.screen_shot("督促用決済名簿（PDF）_31")

        # 58 口座振込依頼書（PDF）: ×ボタン押下でPDFを閉じる
        # TODO: I don't know which function to close the pdf tab

        # 59 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_59")
