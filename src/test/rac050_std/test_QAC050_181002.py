import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181002(FukushiSiteTestCaseBase):
    """TestQAC050_181002"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181002"]
        super().setUp()

    # 不備があった書類の登録ができることを確認する。
    def test_QAC050_181002(self):
        """不備書類入力"""

        case_data = self.test_data["TestQAC050_181002"]
        atena_code = case_data.get("atena_code", "")
        chkTeisyutsu = case_data.get("chkTeisyutsu", "")
        chkFubi = case_data.get("chkFubi", "")
        preTxtKetteiYMD_1 = case_data.get("preTxtKetteiYMD_1", "")
        preKetteiKekkaCmb_1 = case_data.get("preKetteiKekkaCmb_1", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="提出書類管理")

        # 3 提出書類管理: 表示
        self.screen_shot("提出書類管理_3")

        # 4 提出書類管理: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 5 提出書類管理: 所得証明書の未提出にチェックオン 所得証明書の不備にチェックオン
        self.form_input_by_id(idstr="ChkTeisyutsu_3", value=chkTeisyutsu)
        self.form_input_by_id(idstr="ChkFubi_3", value=chkFubi)
        self.screen_shot("提出書類管理_5")

        # 6 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 7 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_7")

        # 8 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 9 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 10 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_10")

        # 11 児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 12 児童扶養手当資格管理画面: 決定年月日「20230801」 決定結果「保留」
        # 決定年月日
        self.form_input_by_id(idstr="TxtKetteiYMD", value=preTxtKetteiYMD_1)
        # 決定結果
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=preKetteiKekkaCmb_1)
        self.screen_shot("児童扶養手当資格管理画面_12")

        # 13 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_13")

        # 14 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 15 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 16 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_16")