import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180207(FukushiSiteTestCaseBase):
    """TestQAC050_180207"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180207"]
        super().setUp()

    # 児童扶養手当証書を出力できることを確認する。
    def test_QAC050_180207(self):
        """証書等作成"""

        case_data = self.test_data["TestQAC050_180207"]
        atena_code = case_data.get("atena_code", "")
        report_name = case_data.get("report_name", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")
        # jidou_fuyou_teate_shousho = case_data.get("jidou_fuyou_teate_shousho", "")
        tsuuchisho_kubun = case_data.get("tsuuchisho_kubun", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 児童扶養手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_2")

        # 3 帳票印刷画面: 「児童扶養手当証書」行の印刷チェックボックス選択「児童扶養手当証書」行の発行年月日チェックボックス選択「児童扶養手当証書」行の発行年月日「20230502」
        # 4 帳票印刷画面: 「印刷」ボタン押下
        exec_params = [
            {
                "report_name": report_name,
                "params":[
                    {"title": "発行年月日", "value": hakkou_ymd, "is_no_print": "1"},
                    {"title": "文書番号", "value": "12345"}
                ]
            }
        ]
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_3")

        # 5 帳票印刷画面: 児童扶養手当証書「ファイルを開く(O)」ボタンを押下
        # 6 児童扶養手当証書（PDF）: 表示
        # 7 児童扶養手当証書（PDF）: 2ページ目表示
        # 8 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # 9 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 10 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_10")

        # 11 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_11")

        # 12 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 13 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_13")

        # 14 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：通知書一括処理
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="通知書一括処理")

        """
        # 15 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_15")

        # 16 バッチ起動画面: 「児童扶養手当証書受領書出力」のNoボタン押下
        self.click_batch_job_button_by_label("児童扶養手当証書受領書出力")

        # 17 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_17")

        # 18 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」宛名コード「」出力順「証書番号順」選択通知書区分「1」発行年月日「20230502」
        params = [
            {"title": "開始決定日", "type": "text", "value": "20230502"},
            {"title": "終了決定日", "type": "text", "value": "20230502"},
            {"title": "宛名コード", "type": "text", "value": ""},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "通知書区分", "type": "text", "value": tsuuchisho_kubun},
            {"title": "発行年月日", "type": "text", "value": "20230502"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_18")

        # 19 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 20 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_20")

        # 21 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 22 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_22")

        # 23 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 24 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_24")

        # 25 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_25")

        # 26 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 27 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_27")

        # 28 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 29 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_29")

        # 30 ジョブ帳票履歴画面: 「児童扶養手当証書受領書」のNoボタン押下
        self.click_batch_job_button_by_label("児童扶養手当証書受領書")

        # 31 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # 32 児童扶養手当証書受領書（PDF）: 表示
        # 33 児童扶養手当証書受領書（PDF）: ×ボタン押下でPDFを閉じる

        # 34 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_34")

        # 35 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 36 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_36")

        # 37 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()
        """

        # 38 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_38")

        # 39 バッチ起動画面: 「児童扶養手当証書一括出力」のNoボタン押下
        self.click_batch_job_button_by_label("児童扶養手当証書一括出力")

        # 40 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_40")

        # 41 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」宛名コード「」出力順「証書番号順」選択通知書区分「1」発行年月日「20230502」
        params = [
            {"title": "開始決定日", "type": "text", "value": "20230502"},
            {"title": "終了決定日", "type": "text", "value": "20230502"},
            {"title": "宛名コード", "type": "text", "value": ""},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "通知書区分", "type": "text", "value": tsuuchisho_kubun},
            {"title": "発行年月日", "type": "text", "value": "20230502"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_41")

        # 42 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 43 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_43")

        # 44 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 45 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_45")

        # 46 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 47 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_47")

        # 48 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_48")

        # 49 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 50 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_50")

        # 51 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 52 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_52")

        # 53 ジョブ帳票履歴画面: 「児童扶養手当証書」のNoボタン押下
        self.click_batch_job_button_by_label("児童扶養手当証書")

        # 54 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # 55 児童扶養手当証書（PDF）: 表示
        # 56 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # 57 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # 58 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 59 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_59")
