import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181320(FukushiSiteTestCaseBase):
    """TestQAC050_181320"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181320"]
        super().setUp()

    # 過払金が発生有無を、支払履歴画面、債権履歴画面、支払調整履歴画面で確認する。※過払金の発生は、遡りで額改定（減）や資格喪失等の決定履歴の登録時に発生するため
    def test_QAC050_181320(self):
        """過払金計算"""

        case_data = self.test_data["TestQAC050_181320"]
        atena_code = case_data.get("atena_code", "")
        kzStart_ymd = case_data.get("kzStart_ymd", "")
        kzGinko_code = case_data.get("kzGinko_code", "")
        kzShiten_code = case_data.get("kzShiten_code", "")
        kzKouza_shubetsu_text = case_data.get("kzKouza_shubetsu_text", "")
        kzKouza_bango = case_data.get("kzKouza_bango", "")
        pTbxPayDate = case_data.get("pTbxPayDate", "")
        pTbxDecisionDate = case_data.get("pTbxDecisionDate", "")
        pCmbPaymentMethod = case_data.get("pCmbPaymentMethod", "")
        pShinseiShubetsuCmb = case_data.get("pShinseiShubetsuCmb", "")
        pShinseiRiyuuCmb = case_data.get("pShinseiRiyuuCmb", "")
        pTantoShokatsukuCmb = case_data.get("pTantoShokatsukuCmb", "")
        pTxtShinseiYMD = case_data.get("pTxtShinseiYMD", "")
        pTxtKaitei = case_data.get("pTxtKaitei", "")
        pCmbTKankatsuKu = case_data.get("pCmbTKankatsuKu", "")
        pCmbHiyouKbn = case_data.get("pCmbHiyouKbn", "")
        pTxtTeisyutsuYMD = case_data.get("pTxtTeisyutsuYMD", "")
        pCmbHKekka = case_data.get("pCmbHKekka", "")
        pCmbSHTaisyo = case_data.get("pCmbSHTaisyo", "")
        pTxtHanteiYMD = case_data.get("pTxtHanteiYMD", "")
        pTxtShintatsu1YMD = case_data.get("pTxtShintatsu1YMD", "")
        pShintatsu1HanteiCmb = case_data.get("pShintatsu1HanteiCmb", "")
        pTxtKetteiYMD = case_data.get("pTxtKetteiYMD", "")
        pKetteiKekkaCmb = case_data.get("pKetteiKekkaCmb", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 過払金の作成→現況情報変更（提出済み→未提出）
        self.click_button_by_label("修正")
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click("口座情報")
        self.click_button_by_label("追加")
        self.entry_kouza_info(
            start_ymd=kzStart_ymd,
            ginko_code=kzGinko_code,
            shiten_code=kzShiten_code,
            kouza_shubetsu_text=kzKouza_shubetsu_text,
            kouza_bango=kzKouza_bango,
            koukai=True
        )
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        # Noボタン1～7(支給対象年月：R05.06～R05.12)について支払い済みとする
        for i in range(1,8):
            self.find_common_buttons()
            self.open_common_buttons_area()
            self.common_button_click("支払履歴")
            # △ボタン押下（ソート：支給対象年月の降順）
            self.click_by_id("CmdSortTargetMonth")
            self.click_button_by_label(str(i))
            self.click_button_by_label("修正")
            # 振込日
            self.form_input_by_id(idstr="TbxPayDate", value=pTbxPayDate)
            # 支給決定日
            self.form_input_by_id(idstr="TbxDecisionDate", value=pTbxDecisionDate)
            # 支払方法
            self.form_input_by_id(idstr="CmbPaymentMethod", text=pCmbPaymentMethod)
            self.click_button_by_label("最新口座")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.return_click()
        self.click_button_by_label("初期表示")

        self.click_button_by_label("申請内容入力")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=pShinseiShubetsuCmb)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=pShinseiRiyuuCmb)
        self.click_button_by_label("確定")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text=pTantoShokatsukuCmb)
        self.form_input_by_id(idstr="TxtShinseiYMD", value=pTxtShinseiYMD)
        self.form_input_by_id(idstr="TxtKaitei", value=pTxtKaitei)
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click("現況情報")
        self.click_button_by_label("1")
        self.click_button_by_label("修正")
        # 提出管理場所
        self.form_input_by_id(idstr="CmbTKankatsuKu", text=pCmbTKankatsuKu)
        # 現況区分
        self.form_input_by_id(idstr="CmbHiyouKbn", text=pCmbHiyouKbn)
        # 提出年月日
        self.form_input_by_id(idstr="TxtTeisyutsuYMD", value=pTxtTeisyutsuYMD)
        # 判定結果
        self.form_input_by_id(idstr="CmbHKekka", text=pCmbHKekka)
        # 所得判定対象者
        self.form_input_by_id(idstr="CmbSHTaisyo", text=pCmbSHTaisyo)
        # 判定年月日
        self.form_input_by_id(idstr="TxtHanteiYMD", value=pTxtHanteiYMD)
        self.click_button_by_label("入力完了")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達入力")
        # 進達年月日
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=pTxtShintatsu1YMD)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達結果入力")
        # 進達結果
        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=pShintatsu1HanteiCmb)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("決定内容入力")
        # 決定年月日
        self.form_input_by_id(idstr="TxtKetteiYMD", value=pTxtKetteiYMD)
        # 決定結果
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=pKetteiKekkaCmb)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        # 1 "児童扶養手当 資格管理画面": 「支払履歴」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click("支払履歴")

        # 2 支払履歴画面: 表示
        self.screen_shot("支払履歴画面_2")

        # 3 支払履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 4 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当資格管理画面_4")

        # 5 "児童扶養手当 資格管理画面": 「債権履歴」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click("債権履歴")

        # 6 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_6")

        # 7 債権履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 8 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当資格管理画面_8")

        # 9 "児童扶養手当 資格管理画面": 「支払調整履歴」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click("支払調整履歴")

        # 10 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_10")

        # 11 支払調整履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 12 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当資格管理画面_12")
