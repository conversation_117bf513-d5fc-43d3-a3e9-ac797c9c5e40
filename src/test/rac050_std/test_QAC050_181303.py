import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181303(FukushiSiteTestCaseBase):
    """TestQAC050_181303"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 現況届未提出者を抽出し、対象者に現況届未提出のお知らせを出力できることを確認する。
    def test_QAC050_181303(self):
        """現況届未提出のお知らせ作成"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：年次処理分類：現況届未提出者督促処理
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="年次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="現況届未提出者督促処理")
        self.screen_shot("バッチ起動画面_4")

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「現況届未提出督促対象者抽出処理」のNoボタン押下
        self.click_batch_job_button_by_label("現況届未提出督促対象者抽出処理")

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: 現況年度「令和５年」選択発行年月日「20230702」提出期限年月日「20230731」出力順「証書番号順」選択
        params = [
            {"title": "現況年度", "type": "select", "value": "令和５年"},
            {"title": "発行年月日", "type": "text", "value": "20230702"},
            {"title": "提出期限年月日", "type": "text", "value": "20230731"},
            {"title": "出力順", "type": "select", "value": "証書番号順"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_16")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 19 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「現況届未提出督促者一覧」のNoボタン押下

        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 22 督促用決済名簿（PDF）: 表示
        # self.screen_shot("督促用決済名簿（PDF）_23")

        # 23 督促用決済名簿（PDF）: ×ボタン押下でPDFを閉じる

        # 24 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_24")

        # 25 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 26 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_26")

        # 27 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 28 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_28")

        # 29 バッチ起動画面: 「現況届督促状出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("現況届督促状出力処理")

        # 30 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_30")

        # 31 バッチ起動画面: 出力区分「0」開始頁「000001」終了頁「999999」
        params = [
            {"title": "出力区分", "type": "text", "value": "0"},
            {"title": "開始頁", "type": "text", "value": "000001"},
            {"title": "終了頁", "type": "text", "value": "999999"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_31")

        # 32 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 33 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_33")

        # 34 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 35 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_35")

        # 36 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 37 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_37")

        # 38 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_39")

        # 39 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 40 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_40")

        # 41 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 42 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_42")

        # 43 ジョブ帳票履歴画面: 「督促状」のNoボタン押下

        # 44 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 45 保留通知書（PDF）: 表示
        # self.screen_shot("保留通知書（PDF）_45")

        # 46 保留通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 47 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_47")

        # 48 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 49 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_49")

        # 50 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 51 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_51")

        # 52 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：年次処理分類：現況未提出者再出力処理
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="年次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="現況未提出者再出力処理")

        # 53 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_53")

        # 54 バッチ起動画面: 「現況未提出者再発行_対象者一覧」のNoボタン押下
        self.click_batch_job_button_by_label("現況未提出者再発行_対象者一覧")
        self.screen_shot("バッチ起動画面_54")

        # 55 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_55")

        # 56 バッチ起動画面: 現況年度「令和５年」選択出力順「証書番号順」選択
        params = [
            {"title": "現況年度", "type": "select", "value": "令和５年"},
            {"title": "出力順", "type": "select", "value": "証書番号順"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_56")

        # 57 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 58 バッチ起動画面: 表示
        self.screen_shot("ジョブ実行履歴画面_58")

        # 59 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.screen_shot("ジョブ実行履歴画面_59")

        # 60 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_60")

        # 61 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        # self.screen_shot("ジョブ実行履歴画面_61")

        # 62 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_62")

        # 63 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_63")

        # 64 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 65 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_65")

        # 66 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 67 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_67")

        # 68 ジョブ帳票履歴画面: 「現況届対象者一覧」のNoボタン押下

        # 69 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 70 現況届対象者一覧（PDF）: 表示

        # 71 現況届対象者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 72 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_72")

        # 73 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 74 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_74")

        # 75 バッチ起動画面: 「処理一覧」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 76 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_76")

        # 77 バッチ起動画面: 「現況未提出者再発行_現況出力」のNoボタン押下
        self.click_batch_job_button_by_label("現況未提出者再発行_現況出力")

        # 78 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_78")

        # 79 バッチ起動画面: 開始通番「000001」終了通番「999999」
        params = [
            {"title": "開始通番", "type": "text", "value": "000001"},
            {"title": "終了通番", "type": "text", "value": "999999"}
        ]
        self.set_job_params(params)

        # 80 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 81 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_81")

        # 82 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 83 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_83")

        # 84 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 85 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_85")

        # 86 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_86")

        # 87 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 88 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_88")

        # 89 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 90 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_90")

        # 91 ジョブ帳票履歴画面: 「児童扶養手当現況届」のNoボタン押下

        # 92 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 93 児童扶養手当現況届（PDF）: 表示

        # 94 児童扶養手当現況届（PDF）: 2ページ目表示

        # 95 児童扶養手当現況届（PDF）: 3ページ目表示

        # 96 児童扶養手当現況届（PDF）: ×ボタン押下でPDFを閉じる

        # 97 ジョブ帳票履歴画面: 表示

        # 98 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 99 バッチ起動画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_99")

        # 100 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 101 バッチ起動画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_101")

        # 102 バッチ起動画面: 「現況未提出者再発行_お知らせ出力」のNoボタン押下
        self.click_batch_job_button_by_label("現況未提出者再発行_お知らせ出力")

        # 103 バッチ起動画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_103")

        # 104 バッチ起動画面: 開始通番「000001」終了通番「999999」現況年度「令和５年」選択発行年月日「20230702」受付年月日（開始）「20230801」受付年月日（終了）「20230815」
        params = [
            {"title": "開始通番", "type": "text", "value": "000001"},
            {"title": "終了通番", "type": "text", "value": "999999"},
            {"title": "現況年度", "type": "select", "value": "令和５年"},
            {"title": "発行年月日", "type": "text", "value": "20230702"},
            {"title": "受付年月日（開始）", "type": "text", "value": "20230801"},
            {"title": "受付年月日（終了）", "type": "text", "value": "20230815"}
        ]
        self.set_job_params(params)

        # 105 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 106 バッチ起動画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_106")

        # 107 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 108 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_108")

        # 109 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 110 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_110")

        # 111 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_111")

        # 112 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 113 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_113")

        # 114 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 115 ジョブ帳票履歴画面: 表示

        # 116 ジョブ帳票履歴画面: 「現況届案内」のNoボタン押下

        # 117 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 118 現況届案内（PDF）: 表示

        # 119 現況届案内（PDF）: ×ボタン押下でPDFを閉じる

        # 120 ジョブ帳票履歴画面: 表示

        # 121 ジョブ帳票履歴画面: 「現況届提出前のおねがい」のNoボタン押下

        # 122 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 123 現況届提出前のおねがい（PDF）: 表示

        # 124 現況届提出前のおねがい（PDF）: ×ボタン押下でPDFを閉じる

        # 125 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_125")
