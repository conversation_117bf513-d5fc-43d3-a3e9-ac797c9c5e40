import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180108(FukushiSiteTestCaseBase):
    """TestQAC050_180108"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180108"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC050", "DELETE_ATENA_CODE": case_data.get("atena_code", ""),"DELETE_ATENA_CODE2": case_data.get("atena_code2", ""),
                      "TARGET_NENDO":case_data.get("nendo", ""),"TARGET_SOUSYOTOKU":case_data.get("soushotoku", ""),"TARGET_KOUJOGAKU":case_data.get("koujogaku", "")}
        self.exec_sqlfile("Test_QAC050_180108.sql", params=sql_params)
        super().setUp()

    # 認定請求した住民に対し却下登録ができることを確認する。
    def test_QAC050_180108(self):
        """認定却下処理"""

        case_data = self.test_data["TestQAC050_180108"]
        atena_code = case_data.get("atena_code", "")
        kaitei_ymd = case_data.get("kaitei_ymd", "")

        # 申請内容入力
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        self.click_button_by_label("申請内容入力")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
        self.click_button_by_label("確定")
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230325")
        self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("2")
        self.form_input_by_id(idstr="CmbZokugara", text="子")
        self.form_input_by_id(idstr="RdoDokyo1", value="1")
        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        self.form_input_by_id(idstr="KojiGaitou2", value="1")
        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        self.form_input_by_id(idstr="RdoShogai2", value="1")
        self.click_button_by_label("入力完了")
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20230501")
        self.form_input_by_id(idstr="TxtKaitei", value="202305")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("所得情報")
        self.click_button_by_label("1")
        self.return_click()
        self.return_click()
        self.click_button_by_label("年金情報登録")
        self.click_button_by_label("受給年金")
        self.click_button_by_label("追加")
        self.form_input_by_id(idstr="CmbTJNenkinBunrui", text="障害年金")
        self.form_input_by_id(idstr="CmbTJNenkinShurui", text="厚生年金")
        self.form_input_by_id(idstr="TxtTJJukyuSYMD", value="令和04年08月01日")
        self.form_input_by_id(idstr="TxtTNenkinnadonengaku", value="1000000")
        self.form_input_by_id(idstr="CmbNendoYM", text="令和04年")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.click_button_by_label("加入年金")
        self.return_click()
        self.click_button_by_label("福祉世帯情報")
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20210501")
        self.form_input_by_id(idstr="HoninCmb_2", text="子")
        self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20210501")
        self.click_button_by_label("入力完了")
        self.open_common_buttons_area()
        self.click_button_by_label("メモ情報")
        self.click_button_by_label("追加")
        self.form_input_by_id(idstr="TxtNaiyo", value="あいうえお")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.open_common_buttons_area()
        self.click_button_by_label("提出書類管理")
        self.click_button_by_label("追加")
        self.form_input_by_id(idstr="ChkTeisyutsu_1", value="1")
        self.form_input_by_id(idstr="ChkTeisyutsu_3", value="1")
        self.click_button_by_label("入力完了")
        self.open_common_buttons_area()
        self.click_button_by_label("住所管理")
        self.click_button_by_label("追加")
        self.click_button_by_label("住記情報索引")
        self.alert_ok()
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.open_common_buttons_area()
        self.click_button_by_label("連絡先管理")
        self.click_button_by_label("追加")
        self.form_input_by_id(idstr="CmbYusenTEL", text="携帯電話番号")
        self.form_input_by_id(idstr="kokai0", value="1")
        self.form_input_by_id(idstr="TxtTelJitaku", value="************")
        self.form_input_by_id(idstr="TxtTelKeitai", value="090-1234-5678")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.click_button_by_label("口座情報")
        self.click_button_by_label("追加")
        self.entry_kouza_info(
            start_ymd=kaitei_ymd,
            ginko_code="0001",
            shiten_code="001",
            kouza_shubetsu_text="普通",
            kouza_bango="1234567",
            koukai=True
        )
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.form_input_by_id(idstr="GengakuYMChkBox", value="1")
        self.form_input_by_id(idstr="TxtGengakuYM", value="202305") 
        self.click_button_by_label("月額計算")
        self.click_button_by_label("所得判定詳細情報")
        self.return_click()
        self.click_button_by_label("公的年金等停止額")
        self.return_click()
        self.find_common_buttons_open_button().click()
        can_shintatsu_button = self.click_button_by_label("不現住情報")
        if (can_shintatsu_button):
            self.click_button_by_label("不現住情報") 
            self.click_button_by_label("追加")
            self.click_button_by_label("住記情報索引")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.return_click()
        self.click_button_by_label("住記情報")
        self.click_button_by_label("1")
        self.return_click()
        self.return_click()
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")

        # 進達入力
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230501")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230501")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.do_login()
        # 1	"児童扶養手当: 資格管理画面" 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 2	"児童扶養手当: 資格管理画面" "決定年月日「20230502」 決定結果「却下」 決定理由「添付書類不備」"		〇
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230502")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="却下")
        self.form_input_by_id(idstr="KetteiRiyuCmb", text="申請書類不足")
        self.screen_shot("児童扶養手当資格管理画面_2")

        # 3	"児童扶養手当: 資格管理画面" 児童のNoボタン押下
        self.click_by_id("CmdNo1")

        # 4	支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_4")

        # 5	支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 6	"児童扶養手当: 資格管理画面" 表示
        self.screen_shot("児童扶養手当資格管理画面_6")

        # 7	"児童扶養手当: 資格管理画面" 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 8	"児童扶養手当: 資格管理画面" 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 9	"児童扶養手当: 資格管理画面" 表示	メッセージエリアに「登録しました。 」と表示されていることを確認する。
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("児童扶養手当資格管理画面_9")
