import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181504(FukushiSiteTestCaseBase):
    """TestQAC050_181504"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181504"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC050", "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
                      "TARGET_NENDO":case_data.get("nendo", ""),"TARGET_SOUSYOTOKU":case_data.get("soushotoku", ""),"TARGET_KOUJOGAKU":case_data.get("koujogaku", "")}
        self.exec_sqlfile("Test_QAC050_181504.sql", params=sql_params)
        super().setUp()

    # 過払金の情報を確認する。
    def test_QAC050_181504(self):
        """過払金額計算"""

        case_data = self.test_data["TestQAC050_181504"]
        atena_code = case_data.get("atena_code", "")
        kaitei_ymd = case_data.get("kaitei_ymd", "")

        # 申請内容入力
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        self.click_button_by_label("申請内容入力")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
        self.click_button_by_label("確定")
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230325")
        self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("2")
        self.form_input_by_id(idstr="CmbZokugara", text="子")
        self.form_input_by_id(idstr="RdoDokyo1", value="1")
        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        self.form_input_by_id(idstr="KojiGaitou2", value="1")
        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        self.form_input_by_id(idstr="RdoShogai2", value="1")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("3")
        self.form_input_by_id(idstr="CmbZokugara", text="子")
        self.form_input_by_id(idstr="RdoDokyo1", value="1")
        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        self.form_input_by_id(idstr="KojiGaitou2", value="1")
        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        self.form_input_by_id(idstr="RdoShogai2", value="1")
        self.click_button_by_label("入力完了")
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20230501")
        self.form_input_by_id(idstr="TxtKaitei", value="202305")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("所得情報")
        self.click_button_by_label("1")
        self.return_click()
        self.return_click()
        self.click_button_by_label("年金情報登録")
        self.click_button_by_label("受給年金")
        self.click_button_by_label("追加")
        self.form_input_by_id(idstr="CmbTJNenkinBunrui", text="障害年金")
        self.form_input_by_id(idstr="CmbTJNenkinShurui", text="厚生年金")
        self.form_input_by_id(idstr="TxtTJJukyuSYMD", value="令和04年08月01日")
        self.form_input_by_id(idstr="TxtTNenkinnadonengaku", value="1000000")
        self.form_input_by_id(idstr="CmbNendoYM", text="令和04年")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.click_button_by_label("加入年金")
        self.return_click()
        self.click_button_by_label("福祉世帯情報")
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20210501")
        self.form_input_by_id(idstr="HoninCmb_2", text="子")
        self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20210501")
        self.form_input_by_id(idstr="HoninCmb_3", text="子")
        self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20210501")
        self.click_button_by_label("入力完了")
        self.open_common_buttons_area()
        self.click_button_by_label("メモ情報")
        self.click_button_by_label("追加")
        self.form_input_by_id(idstr="TxtNaiyo", value="あいうえお")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.open_common_buttons_area()
        self.click_button_by_label("連絡先管理")
        self.click_button_by_label("追加")
        self.form_input_by_id(idstr="CmbYusenTEL", text="携帯電話番号")
        self.form_input_by_id(idstr="kokai0", value="1")
        self.form_input_by_id(idstr="TxtTelJitaku", value="************")
        self.form_input_by_id(idstr="TxtTelKeitai", value="090-1234-5678")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.click_button_by_label("口座情報")
        self.click_button_by_label("追加")
        self.entry_kouza_info(
            start_ymd=kaitei_ymd,
            ginko_code="0001",
            shiten_code="001",
            kouza_shubetsu_text="普通",
            kouza_bango="1234567",
            koukai=True
        )
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.form_input_by_id(idstr="GengakuYMChkBox", value="1")
        self.form_input_by_id(idstr="TxtGengakuYM", value="202305") 
        self.click_button_by_label("月額計算")
        self.click_button_by_label("所得判定詳細情報")
        self.return_click()
        self.click_button_by_label("公的年金等停止額")
        self.return_click()
        self.click_button_by_label("住記情報")
        self.click_button_by_label("1")
        self.return_click()
        self.return_click()
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")

        # 進達入力
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230501")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230501")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")

        # 決定内容入力
        self.click_button_by_label("決定内容入力") 
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230501")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.form_input_by_id(idstr="TxtShoushoBango", value="181504")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")

        # 支払情報
        self.click_button_by_label("修正")
        self.click_button_by_label("支払履歴")
        self.click_button_by_label("6")
        self.click_button_by_label("修正")
        self.form_input_by_id(idstr="TbxPayDate", value="20230910")
        self.form_input_by_id(idstr="TbxDecisionDate", value="20230910")
        self.form_input_by_id(idstr="CmbPaymentMethod", text="窓口")
        self.click_button_by_label("最新口座")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")
        self.click_button_by_label("5")
        self.click_button_by_label("修正")
        self.form_input_by_id(idstr="TbxPayDate", value="20230901")
        self.form_input_by_id(idstr="TbxDecisionDate", value="20230901")
        self.form_input_by_id(idstr="CmbPaymentMethod", text="窓口")
        self.click_button_by_label("最新口座")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")
        self.return_click()
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")

        # 申請内容入力
        self.click_button_by_label("申請内容入力")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=case_data.get("shinsei_shubetsu", ""))
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("shinsei_riyu", ""))
        self.click_button_by_label("確定")
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230601")
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230531")
        self.form_input_by_id(idstr="TxtKaitei", value="20230531")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")

        self.click_button_by_label("修正")
        self.click_button_by_label("確定")
        self.open_common_buttons_area()
        self.click_button_by_label("提出書類管理")
        self.click_button_by_label("追加")
        self.form_input_by_id(idstr="ChkTeisyutsu_1", value="1")
        self.form_input_by_id(idstr="ChkTeisyutsu_3", value="1")
        self.form_input_by_id(idstr="TxtSyoruiYMD_1", value="20230702")
        self.form_input_by_id(idstr="TxtSyoruiYMD_3", value="20230702")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")

        # 進達入力
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230701")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230701")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")

        # 決定内容入力
        self.click_button_by_label("決定内容入力")
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230702")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")

        self.click_button_by_label("修正")
        self.open_common_buttons_area()
        self.click_button_by_label("債権履歴")
        self.click_button_by_label("1")
        self.click_button_by_label("修正")
        self.form_input_by_id(idstr="RadioZengakuS", value="1")
        self.click_button_by_label("計算")
        self.click_button_by_label("債権入力")
        self.form_input_by_id(idstr="TxtSaimuYMD", value="20230702")
        self.form_input_by_id(idstr="CmbHennou", text="口座振込")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.click_button_by_label("修正")
        self.open_common_buttons_area()
        self.click_button_by_label("債権履歴")
        self.click_button_by_label("1")
        self.click_button_by_label("修正")
        self.click_button_by_label("計算")
        self.click_button_by_label("計画作成")
        self.click_button_by_label("再計画")
        self.form_input_by_id(idstr="TxtYoteigaku", value="1000")
        self.form_input_by_id(idstr="TxtKaishi", value="202307")
        self.click_button_by_label("計画作成")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.click_button_by_label("1")
        self.click_button_by_label("修正")
        self.click_button_by_label("1")
        self.form_input_by_id(idstr="TxtNyukinbi", value="20230702")
        self.form_input_by_id(idstr="TxtNyukingaku", value="100")
        self.form_input_by_id(idstr="RdoSainyu", value="1")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「債権管理」ボタン押下
        self.saiken_kanri_click()  # Button with ID: CmdProcess7_2 instead of self.click_button_by_label("債権管理")

        # 3 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_3")

        # 4 債権履歴画面: 事業「児童扶養手当」選択絞り込み条件「返納中のみ」チェック
        self.form_input_by_id(idstr="CmbGyomu", text="児童扶養手当")
        self.form_input_by_id(idstr="RadioC", value="1")
        self.screen_shot("債権履歴画面_4")

        # 5 債権履歴画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 6 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_6")

        # 7 債権履歴画面: 該当者一覧 Noボタン押下
        atena_col = '3'
        table_idx = 0
        sonzaiFlg = False
        tb_Page = self.find_element_by_xpath('//*[@id="_wr_body_panel"]/table[5]/tbody/tr/td/b')
        maxPage = str(tb_Page.text).replace("／","")
        for page_index in range(int(maxPage)):
            tr_elem = self.find_elements_by_css_selector("#_wrFk_TitleTable_Div_1 > table > tbody > tr")
            table_idx = 0
            for elem in tr_elem:
                table_idx += 1
                td_elem = elem.find_element(By.CSS_SELECTOR, "td:nth-child(" + atena_col + ")")
                if atena_code == td_elem.text:
                    sonzaiFlg = True
                    self.click_by_id("Sel" + str(table_idx))
                    break
            if sonzaiFlg == False:
                self.click_by_id("CmdNextPage")
            else:
                break

        # 8 債権情報画面: 表示
        self.screen_shot("債権情報画面_8")

        # 9 債権情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 10 債権情報画面: 「計算」ボタン押下
        self.click_button_by_label("計算")
        self.click_button_by_label("計画作成")

        # 11 返済計画作成画面: 表示
        self.screen_shot("返済計画作成画面_11")

        # 12 返済計画作成画面: 「戻る」ボタン押下
        self.return_click()

        # 13 債権情報画面: 表示
        self.screen_shot("債権情報画面_13")

        # 14 債権情報画面: 「初期表示」ボタン押下
        self.click_button_by_label("初期表示")

        # 15 債権情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 16 債権情報画面: 返納状況一覧 「1」Noボタン押下
        self.click_button_by_label("1")

        # 17 債権入金登録: 表示
        self.screen_shot("債権入金登録_17")

        # 18 債権入金登録: 「戻る」ボタン押下
        self.return_click()

        # 19 債権情報画面: 表示
        self.screen_shot("債権情報画面_19")

        # 20 債権情報画面: 「戻る」ボタン押下
        self.return_click()

        # 21 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_21")

        # 22 債権履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 23 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_23")
