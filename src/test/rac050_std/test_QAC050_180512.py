import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180512(FukushiSiteTestCaseBase):
    """TestQAC050_180512"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180512"]
        super().setUp()

    # 返納について計画情報を作成できることを確認する。
    def test_QAC050_180512(self):
        """内払調整計画情報入力"""

        case_data = self.test_data["TestQAC050_180512"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 「債権履歴画面」ボタン押下
        self.open_common_buttons_area()
        self.common_button_click("債権履歴")

        # 3 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_3")

        # 4 債権履歴画面: 該当者一覧 「1」Noボタン押下
        self.click_button_by_label("1")

        # 5 債権情報画面: 表示
        self.screen_shot("債権情報画面_5")

        # 6 債権情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 7 債権情報画面: 「計算」ボタン押下
        self.click_button_by_label("計算")

        # 8 債権情報画面: 「計画作成」ボタン押下
        self.click_button_by_label("計画作成")

        # 9 返済計画作成画面: 表示
        self.screen_shot("返済計画作成画面_9")

        # 10 返済計画作成画面: 「再計画」ボタン押下
        self.click_button_by_label("再計画")

        # 11 返済計画作成画面: 返納予定月額「1000」返納予定開始年月「202307」
        # self.screen_shot("返済計画作成画面_12")
        # NG -> no ID in spec
        self.form_input_by_id(idstr="TxtYoteigaku", value="1000")
        self.form_input_by_id(idstr="TxtKaishi", value="202307")
        # self.screen_shot("返済計画作成画面_11")

        # 12 返済計画作成画面: 「計画作成」ボタン押下
        self.click_button_by_label("計画作成")

        # 13 返済計画作成画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 14 債権情報画面: 表示
        self.screen_shot("債権情報画面_14")
        self.click_button_by_label("入力完了")

        # 登録しないと債権情報画面の返納状況一覧「1」のNoボタンが押せない
        self.click_button_by_label("登録")
        self.alert_ok()
        # 登録後は債権履歴に戻るのでNoボタン押下
        self.click_button_by_label("1")

        # 15 債権情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 16 債権情報画面: 返納状況一覧「1」のNoボタン押下
        self.click_button_by_label("1")

        # 17 債権入金登録画面: 表示
        self.screen_shot("債権入金登録画面_17")

        # 18 債権入金登録画面: 入金日「20230702」入金額「1000」区分「歳入」チェック
        # self.screen_shot("債権入金登録画面_19")
        # NG -> no ID in spec
        self.form_input_by_id(idstr="TxtNyukinbi", value="20230702")
        #self.form_input_by_id(idstr="TxtNyukingaku", value="1000")
        self.form_input_by_id(idstr="TxtNyukingaku", value="560")
        self.form_input_by_id(idstr="RdoSainyu", value="1")
        self.screen_shot("債権入金登録画面_18")

        # 19 債権入金登録画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 20 債権情報画面: 表示
        self.screen_shot("債権情報画面_20")

        # 21 債権情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 22 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_22")

        # 23 債権情報画面: 「戻る」ボタン押下
        self.return_click()

        # 24 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_24")

        # 25 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 26 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 27 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_27")
