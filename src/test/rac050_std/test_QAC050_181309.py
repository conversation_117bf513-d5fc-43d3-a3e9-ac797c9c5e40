import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181309(FukushiSiteTestCaseBase):
    """TestQAC050_181309"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181309"]
        super().setUp()

    # 所得判定で全部支給停止の場合、全部支給停止の情報を登録できることを確認する。
    def test_QAC050_181309(self):
        """全部支給停止処理"""

        case_data = self.test_data["TestQAC050_181309"]
        atena_code = case_data.get("atena_code", "")
        pShinseiShubetsuCmb = case_data.get("pShinseiShubetsuCmb", "")
        pShinseiRiyuuCmb = case_data.get("pShinseiRiyuuCmb", "")
        pTantoShokatsukuCmb = case_data.get("pTantoShokatsukuCmb", "")
        pTxtShinseiYMD = case_data.get("pTxtShinseiYMD", "")
        pTxtJiyuHasseiYMD = case_data.get("pTxtJiyuHasseiYMD", "")
        pTxtKaitei = case_data.get("pTxtKaitei", "")
        pTxtKetteiYMD = case_data.get("pTxtKetteiYMD", "")
        pKetteiKekkaCmb = case_data.get("pKetteiKekkaCmb", "")
        pTxtShoushoKoufuYMD = case_data.get("pTxtShoushoKoufuYMD", "")
        pTxtShintatsu1YMD = case_data.get("pTxtShintatsu1YMD", "")
        pShintatsu1HanteiCmb = case_data.get("pShintatsu1HanteiCmb", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当資格管理画面_1")

        # 2 "児童扶養手当 資格管理画面": 「所得判定詳細情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="所得判定詳細情報")

        # 3 所得判定詳細情報画面: 表示
        self.screen_shot("所得判定詳細情報画面_3")

        # 4 所得判定詳細情報画面: 「次年度＞」ボタン押下
        self.click_button_by_label("次年度＞")

        # 5 所得判定詳細情報画面: 表示
        self.screen_shot("所得判定詳細情報画面_5")

        # 6 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 7 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当資格管理画面_7")

        # 8 "児童扶養手当 資格管理画面": 「差止情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="差止情報")

        # 9 差止情報画面: 表示
        self.screen_shot("差止情報画面_9")

        # 10 差止情報画面: 「戻る」ボタン押下
        self.return_click()

        # 11 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当資格管理画面_11")

        # 12 "児童扶養手当 資格管理画面": 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 13 "児童扶養手当 資格管理画面": "申請種別「支給停止事由変更」選択 申請理由「その他」選択"
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=pShinseiShubetsuCmb)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=pShinseiRiyuuCmb)
        self.screen_shot("児童扶養手当資格管理画面_13")

        # 14 "児童扶養手当 資格管理画面": 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 15 "児童扶養手当 資格管理画面": "申請年月日「20230802」 事由発生日「20230802」 改定年月「202311」"
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text=pTantoShokatsukuCmb)
        self.form_input_by_id(idstr="TxtShinseiYMD", value=pTxtShinseiYMD)
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value=pTxtJiyuHasseiYMD)
        self.form_input_by_id(idstr="TxtKaitei", value=pTxtKaitei)
        self.screen_shot("児童扶養手当資格管理画面_15")

        # 16 "児童扶養手当 資格管理画面": 「月額計算」ボタン押下 支給区分に「全部停止」と表示されているのを確認する。
        self.click_button_by_label("月額計算")

        # 17 "児童扶養手当 資格管理画面": 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 18 "児童扶養手当 資格管理画面": 表示	メッセージエリアに「登録しました。 」と表示されていることを確認する
        # Assert: メッセージエリアに「登録しました。 」と表示されているのを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_18")

        self.click_button_by_label("本庁進達入力")
        # 進達年月日
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=pTxtShintatsu1YMD)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達結果入力")
        # 進達結果
        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=pShintatsu1HanteiCmb)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        # 19 "児童扶養手当 資格管理画面": 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 20 "児童扶養手当 資格管理画面": "決定年月日「20230902」 決定結果「決定」選択 証書交付年月日「20230902」"
        self.form_input_by_id(idstr="TxtKetteiYMD", value=pTxtKetteiYMD)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=pKetteiKekkaCmb)
        self.form_input_by_id(idstr="TxtShoushoKoufuYMD", value=pTxtShoushoKoufuYMD)
        self.screen_shot("児童扶養手当資格管理画面_20")

        # 21 "児童扶養手当 資格管理画面": 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 22 "児童扶養手当 資格管理画面": 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 23 "児童扶養手当 資格管理画面": 表示	メッセージエリアに「登録しました。 」と表示されていることを確認する。
        # Assert: メッセージエリアに「登録しました。」と表示されているのを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_23")
