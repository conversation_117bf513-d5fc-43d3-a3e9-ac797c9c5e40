import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181305(FukushiSiteTestCaseBase):
    """TestQAC050_181305"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181305"]
        super().setUp()

    # 不備があった書類の登録ができることを確認する。
    def test_QAC050_181305(self):
        """不備書類入力"""

        case_data = self.test_data["TestQAC050_181305"]
        atena_code_0 = case_data.get("atena_code_0", "")
        atena_code = case_data.get("atena_code", "")
        pSHissuChkBox = case_data.get("pSHissuChkBox", "")
        pCmbSShubetsu = case_data.get("pCmb<PERSON><PERSON>betsu", "")
        pCmbTKankatsuKu = case_data.get("pCmbTKankatsuKu", "")

#        # 「1813-24」用データ入力
#        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code_0, gyoumu_code="QAC050")
#        self.click_button_by_label("修正")
#        self.find_common_buttons()
#        self.open_common_buttons_area()
#        self.common_button_click(button_text="現況情報")
#        self.click_button_by_label("1")
#        self.click_button_by_label("修正")
#        # 提出管理場所
#        self.form_input_by_id(idstr="CmbTKankatsuKu", text=pCmbTKankatsuKu)
#        self.click_by_id("BtnShoruiTsuika")
#        self.form_input_by_id(idstr="SHissuChkBox", value=pSHissuChkBox)
#        self.form_input_by_id(idstr="CmbSShubetsu", text=pCmbSShubetsu)
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 "児童扶養手当 資格管理画面": 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 "児童扶養手当 資格管理画面": 「現況情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="現況情報")

        # 3 現況履歴画面: 表示
        self.screen_shot("現況履歴画面_3")

        # 4 現況履歴画面: 現況履歴一覧「1」Noボタン押下
        self.click_button_by_label("1")

        # 5 現況履歴画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 提出管理場所
        self.form_input_by_id(idstr="CmbTKankatsuKu", text=pCmbTKankatsuKu)

        # 6 現況履歴画面: 現況提出書類「追加」ボタン押下
        self.click_by_id("BtnShoruiTsuika")

        # 7 現況提出書類情報画面: 表示
        self.screen_shot("現況提出書類情報画面_7")

        # 8 現況提出書類情報画面: "必ず提出いただく書類にチェック 添付書類種別「養育申立書」選択"
        self.form_input_by_id(idstr="SHissuChkBox", value=pSHissuChkBox)
        self.form_input_by_id(idstr="CmbSShubetsu", text=pCmbSShubetsu)
        self.screen_shot("現況提出書類情報画面_8")

        # 9 現況提出書類情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 10 現況履歴画面: 表示
        self.screen_shot("現況履歴画面_10")

        # 11 現況履歴画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 12 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当 資格管理画面_12")

        # 13 "児童扶養手当 資格管理画面": 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 14 "児童扶養手当 資格管理画面": 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 15 "児童扶養手当 資格管理画面": 表示	メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当 資格管理画面_15")
