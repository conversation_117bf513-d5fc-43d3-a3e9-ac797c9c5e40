import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181106(FukushiSiteTestCaseBase):
    """TestQAC050_181106"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181106"]
        super().setUp()

    # 支払差止解除の情報を登録ができることを確認する。
    def test_QAC050_181106(self):
        """支払差止解除処理"""

        case_data = self.test_data["TestQAC050_181106"]
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 「差止情報」ボタン押下
        self.click_button_by_label("差止情報")

        # 3 差止情報画面: 表示
        self.screen_shot("差止情報画面_3")

        # 4 差止情報画面: 差止履歴「1」Noボタン押下
        self.click_button_by_label("1")

        # 5 差止情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 6 差止情報画面: 支払差止解除決定日「20230702」支払差止解除年月「202307」
        self.form_input_by_id(idstr="TxtKaizyo", value="20230702")
        self.form_input_by_id(idstr="TxtKaizyoYM", value="202307")
        self.screen_shot("差止情報画面_6")

        # 7 差止情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 8 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_8")

        # 9 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 10 所得判定詳細情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 11 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_11")
