import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180107(FukushiSiteTestCaseBase):
    """TestQAC050_180107"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180107"]
        super().setUp()

    # 認定請求決定通知書、支給停止通知書、児童扶養手当証書を出力できることを確認する。
    def test_QAC050_180107(self):
        """認定通知書等作成"""

        case_data = self.test_data["TestQAC050_180107"]
        atena_code = case_data.get("atena_code", "")
        tsuuchisho_kubun_1 = case_data.get("tsuuchisho_kubun_1", "")
        tsuuchisho_kubun_2 = case_data.get("tsuuchisho_kubun_2", "")
        report_name_list_1 = case_data.get("report_name_list_1", "")
        report_name_list_2 = case_data.get("report_name_list_2", "")
        report_name_list_3 = case_data.get("report_name_list_3", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 児童扶養手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_2")

        # 3 帳票印刷画面:「認定請求決定通知書」行の印刷チェックボックス選択「認定請求決定通知書」行の発行年月日チェックボックス選択「認定請求決定通知書」行の発行年月日「20230502」「支給停止通知書」行の印刷チェックボックス選択「支給停止通知書」行の発行年月日チェックボックス選択「支給停止通知書」行の発行年月日「20230502」「児童扶養手当証書」行の印刷チェックボックス選択「児童扶養手当証書」行の発行年月日チェックボックス選択「児童扶養手当証書」行の発行年月日「20230502」
        exec_params = [
            {
                "report_name": report_name_list_1,
                "params":[
                    {"title": "発行年月日", "value":hakkou_ymd,"is_no_print":"1"},
                ]
            },
            {
                "report_name": report_name_list_2,
                "params":[
                    {"title": "発行年月日", "value":hakkou_ymd,"is_no_print":"1"},
                ]
            },
            {
                "report_name": report_name_list_3,
                "params":[
                    {"title": "発行年月日", "value":hakkou_ymd,"is_no_print":"1"},
                ]
            }
        ]
        ret = self.print_online_reports(report_param_list=exec_params, case_name="オンライン")
        self.screen_shot("帳票印刷画面_3")

        # 4 帳票印刷画面: 「印刷」ボタン押下
        # self.click_button_by_label("印刷")

        # 5 帳票印刷画面: 認定請求決定通知書「ファイルを開く(O)」ボタンを押下

        # 6 認定請求決定通知書（PDF）: 表示
        # self.screen_shot("認定請求決定通知書（PDF）_7")

        # 7 認定請求決定通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("認定請求決定通知書（PDF）_8")

        # 8 認定請求決定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 9 帳票印刷画面: 支給停止通知書「ファイルを開く(O)」ボタンを押下

        # 10 支給停止通知書（PDF）: 表示
        # self.screen_shot("支給停止通知書（PDF）_11")

        # 11 支給停止通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("支給停止通知書（PDF）_12")

        # 12 支給停止通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 13 帳票印刷画面: 児童扶養手当証書「ファイルを開く(O)」ボタンを押下

        # 14 児童扶養手当証書（PDF）: 表示
        # self.screen_shot("児童扶養手当証書（PDF）_15")

        # 15 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("児童扶養手当証書（PDF）_16")

        # 16 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # 17 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 18 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_18")

        # 19 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_19")

        # 20 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 21 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_21")

        # 22 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：通知書一括処理
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="通知書一括処理")

        # 23 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_23")

        # # 24 バッチ起動画面: 「児童扶養手当証書受領書出力」のNoボタン押下
        # self.click_batch_job_button_by_label("児童扶養手当証書受領書出力")

        # # 25 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_25")

        # # 26 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」宛名コード「」出力順「証書番号順」選択通知書区分「1」発行年月日「20230502」
        # params = [
        #     {"title": "開始決定日", "type": "text", "value": "20230502"},
        #     {"title": "終了決定日", "type": "text", "value": "20230502"},
        #     {"title": "宛名コード", "type": "text", "value": ""},
        #     {"title": "出力順", "type": "select", "value": "証書番号順"},
        #     {"title": "通知書区分", "type": "text", "value": tsuuchisho_kubun_1},
        #     {"title": "発行年月日", "type": "text", "value": "20230502"}
        # ]
        # self.set_job_params(params)
        # self.screen_shot("バッチ起動画面_26")

        # # 27 バッチ起動画面: 「処理開始」ボタン押下
        # exec_datetime = self.exec_batch_job()

        # # 28 バッチ起動画面: 表示
        # # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        # self.assert_message_base_header("ジョブを起動しました")
        # self.screen_shot("バッチ起動画面_28")

        # # 29 バッチ起動画面: 「実行履歴」ボタン押下
        # self.click_job_exec_log()

        # # 30 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_30")

        # # 31 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.wait_job_finished(120,20)
        # self.assert_job_normal_end(exec_datetime=exec_datetime)

        # # 32 ジョブ実行履歴画面: 表示
        # # self.screen_shot("ジョブ実行履歴画面_32")

        # # 33 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # self.screen_shot("ジョブ実行履歴画面_33")

        # # 34 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        # self.click_report_log()

        # # 35 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_35")

        # # 36 ジョブ帳票履歴画面: 「検索」ボタン押下
        # self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="バッチ1")

        # # 37 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_37")

        # # 38 ジョブ帳票履歴画面: 「児童扶養手当証書受領書」のNoボタン押下

        # # 39 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # # 40 児童扶養手当証書受領書（PDF）: 表示
        # # self.screen_shot("児童扶養手当証書受領書（PDF）_42")

        # # 41 児童扶養手当証書受領書（PDF）: ×ボタン押下でPDFを閉じる
        # # self.screen_shot("児童扶養手当証書受領書（PDF）_43")

        # # 42 ジョブ帳票履歴画面: 表示
        # # self.screen_shot("ジョブ帳票履歴画面_42")

        # # 43 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        # self.click_job_list()

        # # 44 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_44")

        # # 45 バッチ起動画面: 「処理一覧」ボタン押下
        # self.click_job_exec_log_search()

        # # 46 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_46")

        # 47 バッチ起動画面: 「児童扶養手当証書一括出力」のNoボタン押下
        self.click_batch_job_button_by_label("児童扶養手当証書一括出力")

        # 48 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_48")

        # 49 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」宛名コード「」出力順「証書番号順」選択通知書区分「1」発行年月日「20230502」
        params = [
            {"title": "開始決定日", "type": "text", "value": "20230502"},
            {"title": "終了決定日", "type": "text", "value": "20230502"},
            {"title": "宛名コード", "type": "text", "value": ""},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "通知書区分", "type": "text", "value": tsuuchisho_kubun_1},
            {"title": "発行年月日", "type": "text", "value": hakkou_ymd}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_49")

        # 50 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 51 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_51")

        # 52 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 53 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_53")

        # 54 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 55 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_55")

        # 56 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_56")

        # 57 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 58 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_58")

        # 59 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="バッチ2")

        # 60 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_60")

        # 61 ジョブ帳票履歴画面: 「児童扶養手当証書」のNoボタン押下

        # 62 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 63 児童扶養手当証書（PDF）: 表示
        # self.screen_shot("児童扶養手当証書（PDF）_65")

        # 64 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("児童扶養手当証書（PDF）_66")

        # 65 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # 66 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_66")

        # 67 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 68 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_68")

        # 69 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 70 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_70")

        # 71 バッチ起動画面: 「決定一括出力処理_一覧」のNoボタン押下
        self.click_batch_job_button_by_label("決定一括出力処理_一覧")

        # 72 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_72")

        # 73 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」宛名コード「」出力順「証書番号順」選択通知書区分「1」発行年月日「20230502」
        params = [
            {"title": "開始決定日", "type": "text", "value": "20230502"},
            {"title": "終了決定日", "type": "text", "value": "20230502"},
            {"title": "宛名コード", "type": "text", "value": ""},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "通知書区分", "type": "text", "value": tsuuchisho_kubun_1},
            {"title": "発行年月日", "type": "text", "value": hakkou_ymd}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_73")

        # 74 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 75 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_75")

        # 76 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 77 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_77")

        # 78 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 79 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_79")

        # 80 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_80")

        # 81 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 82 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_82")

        # 83 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="バッチ3")

        # 84 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_84")

        # 85 ジョブ帳票履歴画面: 「通知書対象者一覧」のNoボタン押下

        # 86 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 87 通知書対象者一覧（PDF）: 表示
        # self.screen_shot("通知書対象者一覧（PDF）_89")

        # 88 通知書対象者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 89 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_89")

        # 90 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 91 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_91")

        # 92 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 93 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_93")

        # 94 バッチ起動画面: 「決定一括出力処理_通知書」のNoボタン押下
        self.click_batch_job_button_by_label("決定一括出力処理_通知書")

        # 95 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_95")

        # 96 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」宛名コード「」出力順「証書番号順」選択通知書区分「1」発行年月日「20230502」
        params = [
            {"title": "開始決定日", "type": "text", "value": "20230502"},
            {"title": "終了決定日", "type": "text", "value": "20230502"},
            {"title": "宛名コード", "type": "text", "value": ""},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "通知書区分", "type": "text", "value": tsuuchisho_kubun_1},
            {"title": "発行年月日", "type": "text", "value": hakkou_ymd}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_96")

        # 97 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 98 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_98")

        # 99 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 100 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_100")

        # 101 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 102 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_102")

        # 103 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_103")

        # 104 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 105 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_105")

        # 106 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="バッチ4")

        # 107 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_107")

        # 108 ジョブ帳票履歴画面: 「認定通知書」のNoボタン押下

        # 109 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 110 認定請求決定通知書（PDF）: 表示
        # self.screen_shot("認定請求決定通知書（PDF）_112")

        # 111 認定請求決定通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("認定請求決定通知書（PDF）_113")

        # 112 認定請求決定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 113 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_113")

        # 114 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 115 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_115")

        # 116 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 117 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_117")

        # 118 バッチ起動画面: 「決定一括出力処理_一覧」のNoボタン押下
        self.click_batch_job_button_by_label("決定一括出力処理_一覧")

        # 119 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_119")

        # 120 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」宛名コード「」出力順「証書番号順」選択通知書区分「5」発行年月日「20230502」
        params = [
            {"title": "開始決定日", "type": "text", "value": "20230502"},
            {"title": "終了決定日", "type": "text", "value": "20230502"},
            {"title": "宛名コード", "type": "text", "value": ""},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "通知書区分", "type": "text", "value": tsuuchisho_kubun_2},
            {"title": "発行年月日", "type": "text", "value": hakkou_ymd}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_120")

        # 121 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 122 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_122")

        # 123 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 124 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_124")

        # 125 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 126 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_126")

        # 127 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_127")

        # 128 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 129 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_129")

        # 130 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="バッチ5")

        # 131 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_131")

        # 132 ジョブ帳票履歴画面: 「通知書対象者一覧」のNoボタン押下

        # 133 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 134 通知書対象者一覧（PDF）: 表示
        # self.screen_shot("通知書対象者一覧（PDF）_136")

        # 135 通知書対象者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 136 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_136")

        # 137 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 138 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_138")

        # 139 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 140 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_140")

        # 141 バッチ起動画面: 「決定一括出力処理_通知書」のNoボタン押下
        self.click_batch_job_button_by_label("決定一括出力処理_通知書")

        # 142 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_142")

        # 143 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」宛名コード「」出力順「証書番号順」選択通知書区分「5」発行年月日「20230502」
        params = [
            {"title": "開始決定日", "type": "text", "value": "20230502"},
            {"title": "終了決定日", "type": "text", "value": "20230502"},
            {"title": "宛名コード", "type": "text", "value": ""},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "通知書区分", "type": "text", "value": tsuuchisho_kubun_2},
            {"title": "発行年月日", "type": "text", "value": hakkou_ymd}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_143")

        # 144 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 145 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_145")

        # 146 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 147 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_147")

        # 148 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 149 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_149")

        # 150 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_150")

        # 151 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 152 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_152")

        # 153 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="バッチ6")

        # 154 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_154")

        # 155 ジョブ帳票履歴画面: 「認定通知書」のNoボタン押下

        # 156 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 157 支給停止通知書（PDF）: 表示
        # self.screen_shot("支給停止通知書（PDF）_159")

        # 158 支給停止通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("支給停止通知書（PDF）_160")

        # 159 支給停止通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 160 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_160")
