import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181412(FukushiSiteTestCaseBase):
    """TestQAC050_181412"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181412"]
        super().setUp()

    # 一部支給停止適用除外通知書等を出力できることを確認する。
    def test_QAC050_181412(self):
        """一部支給停止適用除外通知書等作成"""

        case_data = self.test_data["TestQAC050_181412"]
        atena_code = case_data.get("atena_code", "")
        form_name_0 = case_data.get("form_name_0", "")
        hakkouYMD = case_data.get("hakkouYMD", "")
        form_name_1 = case_data.get("form_name_1", "")
        form_name_2 = case_data.get("form_name_2", "")
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        pStartYMD = case_data.get("pStartYMD", "")
        pEndYMD = case_data.get("pEndYMD", "")
        pAtenaCode = case_data.get("pAtenaCode", "")
        pOutputOrder = case_data.get("pOutputOrder", "")
        noti_category = case_data.get("noti_category", "")
        pHakkoYMD = case_data.get("pHakkoYMD", "")
        pStartYMD_2 = case_data.get("pStartYMD_2", "")
        pEndYMD_2 = case_data.get("pEndYMD_2", "")
        pAtenaCode_2 = case_data.get("pAtenaCode_2", "")
        pOutputOrder_2 = case_data.get("pOutputOrder_2", "")
        noti_category_2 = case_data.get("noti_category_2", "")
        pHakkoYMD_2 = case_data.get("pHakkoYMD_2", "")
        pStartYMD_3 = case_data.get("pStartYMD_3", "")
        pEndYMD_3 = case_data.get("pEndYMD_3", "")
        pAtenaCode_3 = case_data.get("pAtenaCode_3", "")
        pOutputOrder_3 = case_data.get("pOutputOrder_3", "")
        noti_category_3 = case_data.get("noti_category_3", "")
        pHakkoYMD_3 = case_data.get("pHakkoYMD_3", "")
        pStartYMD_4 = case_data.get("pStartYMD_4", "")
        pEndYMD_4 = case_data.get("pEndYMD_4", "")
        pAtenaCode_4 = case_data.get("pAtenaCode_4", "")
        pOutputOrder_4 = case_data.get("pOutputOrder_4", "")
        noti_category_4 = case_data.get("noti_category_4", "")
        pHakkoYMD_4 = case_data.get("pHakkoYMD_4", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 児童扶養手当 資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面1_2")

        # 3 帳票印刷画面: 「一部支給停止適用除外通知書」行の印刷チェックボックス選択
        # 「一部支給停止適用除外通知書」行の発行年月日チェックボックス選択
        # 「一部支給停止適用除外通知書」行の発行年月日「20230902」
        exec_params = [
            {
                "report_name": form_name_0,
                "params":[
                    {"title": "発行年月日", "value":hakkouYMD,"is_no_print":"1"}
                ]
            }
        ]
        self.print_online_reports(report_param_list=exec_params, case_name="帳票印刷画面")
        self.screen_shot("帳票印刷画面1_3")

        self.return_click()

        # 1 児童扶養手当 資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面2_2")

        # 3 帳票印刷画面: 
        # 「支給停止解除通知書」行の印刷チェックボックス選択
        # 「支給停止解除通知書」行の発行年月日チェックボックス選択
        # 「支給停止解除通知書」行の発行年月日「20230902」
        exec_params = [
            {
                "report_name": form_name_1,
                "params":[
                    {"title": "発行年月日", "value":hakkouYMD,"is_no_print":"1"}
                ]
            }
        ]
        self.print_online_reports(report_param_list=exec_params, case_name="帳票印刷画面")
        self.screen_shot("帳票印刷画面2_3")

        self.return_click()

        # 1 児童扶養手当 資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面3_2")

        # 3 帳票印刷画面: 
        # 「児童扶養手当証書」行の印刷チェックボックス選択
        # 「児童扶養手当証書」行の発行年月日チェックボックス選択
        # 「児童扶養手当証書」行の発行年月日「20230902」
        exec_params = [
            {
                "report_name": form_name_2,
                "params":[
                    {"title": "発行年月日", "value":hakkouYMD,"is_no_print":"1"}
                ]
            }
        ]
        self.print_online_reports(report_param_list=exec_params, case_name="帳票印刷画面")
        self.screen_shot("帳票印刷画面3_3")

        # 4 帳票印刷画面: 「印刷」ボタン押下

        # 5 帳票印刷画面: 一部支給停止適用除外通知書「ファイルを開く(O)」ボタンを押下
        # Assert: 「プレビューを表示しました」のメッセージチェック

        # 6 一部支給停止適用除外通知書（PDF）: 表示
        # self.screen_shot("一部支給停止適用除外通知書（PDF）_6")

        # 7 一部支給停止適用除外通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("一部支給停止適用除外通知書（PDF）_7")

        # 8 一部支給停止適用除外通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 9 帳票印刷画面: 支給停止解除通知書「ファイルを開く(O)」ボタンを押下
        # Assert: 「プレビューを表示しました」のメッセージチェック

        # 10 支給停止解除通知書（PDF）: 表示
        # self.screen_shot("支給停止解除通知書（PDF）_10")

        # 11 支給停止解除通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("支給停止解除通知書（PDF）_11")

        # 12 支給停止解除通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 13 帳票印刷画面: 児童扶養手当証書「ファイルを開く(O)」ボタンを押下
        # Assert: 「プレビューを表示しました」のメッセージチェック

        # 14 児童扶養手当証書（PDF）: 表示
        # self.screen_shot("児童扶養手当証書（PDF）_14")

        # 15 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("児童扶養手当証書（PDF）_15")

        # 16 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # 17 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 18 児童扶養手当: 資格管理画面	表示
        self.screen_shot("児童扶養手当資格管理画面_18")

        # 19 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_19")

        # 21 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")

        # 22 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_22")

        # 23 バッチ起動画面: 業務：児童 事業：児童扶養手当 処理区分：月次 処理分類：通知書一括処理
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect)

        # 24 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_24")

#③出荷 -s
#        # 25 バッチ起動画面: 「児童扶養手当証書受領書出力」のNoボタン押下
#        self.click_batch_job_button_by_label("児童扶養手当証書受領書出力")
#
#        # 26 バッチ起動画面: 表示
#        self.screen_shot("バッチ起動画面_26")
#
#        # 27 バッチ起動画面: 開始決定日「20230902」 終了決定日「20230902」 宛名コード「」 出力順「証書番号順」選択 通知書区分「7」 発行年月日「20230902」
#        params = [
#            {"title": "開始決定日", "type": "text", "value": pStartYMD},
#            {"title": "終了決定日", "type": "text", "value": pEndYMD},
#            {"title": "宛名コード", "type": "text", "value": pAtenaCode},
#            {"title": "出力順", "type": "select", "value": pOutputOrder},
#            {"title": "通知書区分", "type": "select", "value": noti_category},
#            {"title": "発行年月日", "type": "text", "value": pHakkoYMD}
#        ]
#        self.set_job_params(params)
#        self.screen_shot("バッチ起動画面_27")
#
#        # 28 バッチ起動画面: 「処理開始」ボタン押下
#        exec_datetime = self.exec_batch_job()
#
#        # 29 バッチ起動画面: 表示 メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
#        self.assert_message_base_header("ジョブを起動しました")
#        self.screen_shot("バッチ起動画面_29")
#
#        # 30 バッチ起動画面: 「実行履歴」ボタン押下
#        self.click_job_exec_log()
#        self.wait_job_finished(120,20)
#        self.assert_job_normal_end(exec_datetime=exec_datetime)
#
#        # 31 ジョブ実行履歴画面: 表示
#        self.screen_shot("ジョブ実行履歴画面_31")
#
#        # 32 ジョブ実行履歴画面: 「検索」ボタン押下
#
#        # 33 ジョブ実行履歴画面: 表示
#        # self.screen_shot("ジョブ実行履歴画面_33")
#
#        # 34 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
#        self.screen_shot("ジョブ実行履歴画面_34")
#
#        # 35 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
#        self.click_report_log()
#
#        # 36 ジョブ帳票履歴画面: 表示
#        self.screen_shot("ジョブ帳票履歴画面_36")
#
#        # 37 ジョブ帳票履歴画面: 「検索」ボタン押下
#        self.click_job_exec_log_search()
#
#        # 38 ジョブ帳票履歴画面: 表示
#        self.screen_shot("ジョブ帳票履歴画面_38")
#
#        # 39 ジョブ帳票履歴画面: 「児童扶養手当証書受領書」のNoボタン押下
#        self.get_job_report_pdf(exec_datetime=exec_datetime)
#
#        # 40 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
#
#        # 41 児童扶養手当証書受領書（PDF）: 表示
#        self.screen_shot("児童扶養手当証書受領書（PDF）_41")
#
#        # 42 児童扶養手当証書受領書（PDF）: ×ボタン押下でPDFを閉じる
#
#        # 43 ジョブ帳票履歴画面: 表示
#        self.screen_shot("ジョブ帳票履歴画面_43")
#
#        # 44 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
#        self.click_job_list()
#
#        # 45 バッチ起動画面: 表示
#        self.screen_shot("バッチ起動画面_45")
#
#        # 46 バッチ起動画面: 「処理一覧」ボタン押下
#        self.click_job_exec_log_search()
#
#        # 47 バッチ起動画面: 表示
#        self.screen_shot("バッチ起動画面_47")
#③出荷 -s

        # 48 バッチ起動画面: 「児童扶養手当証書一括出力」のNoボタン押下
        self.click_batch_job_button_by_label("児童扶養手当証書一括出力")

        # 49 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_49")

        # 50 バッチ起動画面: 開始決定日「20230902」
        # 終了決定日「20230902」
        # 宛名コード「」
        # 出力順「証書番号順」選択
        # 通知書区分「7」
        # 発行年月日「20230902」
        params = [
            {"title": "開始決定日", "type": "text", "value": pStartYMD_2},
            {"title": "終了決定日", "type": "text", "value": pEndYMD_2},
            {"title": "宛名コード", "type": "text", "value": pAtenaCode_2},
            {"title": "出力順", "type": "select", "value": pOutputOrder_2},
            {"title": "通知書区分", "type": "text", "value": noti_category_2},
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD_2}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_50")

        # 51 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 52 バッチ起動画面: 表示 メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_52")

        # 53 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 54 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_54")

        # 55 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 56 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_56")

        # 57 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_57")

        # 58 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 59 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_59")

        # 60 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 61 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_61")

        # 62 ジョブ帳票履歴画面: 「児童扶養手当証書」のNoボタン押下

        # 63 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 64 児童扶養手当証書（PDF）: 表示
        # self.screen_shot("児童扶養手当証書（PDF）_64")

        # 65 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("児童扶養手当証書（PDF）_65")

        # 66 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # 67 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_67")

        # 68 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 69 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_69")

        # 70 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 71 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_71")

        # 72 バッチ起動画面: 「決定一括出力処理_減額適用関係一覧」のNoボタン押下
        self.click_batch_job_button_by_label("決定一括出力処理_減額適用関係一覧")

        # 73 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_73")

        # 74 バッチ起動画面: 開始決定日「20230902」
        # 終了決定日「20230902」
        # 宛名コード「」
        # 出力順「証書番号順」選択
        # 通知書区分「2」
        # 発行年月日「20230902」
        params = [
            {"title": "開始決定日", "type": "text", "value": pStartYMD_3},
            {"title": "終了決定日", "type": "text", "value": pEndYMD_3},
            {"title": "宛名コード", "type": "text", "value": pAtenaCode_3},
            {"title": "出力順", "type": "select", "value": pOutputOrder_3},
            {"title": "通知書区分", "type": "text", "value": noti_category_3},
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD_3}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_74")

        # 75 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 76 バッチ起動画面: 表示 メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_76")

        # 77 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 78 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_78")

        # 79 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 80 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_80")

        # 81 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_81")

        # 82 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 83 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_83")

        # 84 ジョブ帳票履歴画面: 「検索」ボタン押下

        # 85 ジョブ帳票履歴画面: 表示

        # 86 ジョブ帳票履歴画面: 「通知書対象者一覧」のNoボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 87 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 88 通知書対象者一覧（PDF: 表示
        # self.screen_shot("通知書対象者一覧（PDF）_88")

        # 89 通知書対象者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 90 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_90")

        # 91 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 92 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_92")

        # 93 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 94 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_94")

        # 95 バッチ起動画面: 「決定一括出力処理_減額適用関係通知書」のNoボタン押下
        self.click_batch_job_button_by_label("決定一括出力処理_減額適用関係_通知書")

        # 96 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_96")

        # 97 バッチ起動画面: 開始決定日「20230902」
        # 終了決定日「20230902」
        # 宛名コード「」
        # 出力順「証書番号順」選択
        # 通知書区分「2」
        # 発行年月日「20230902」
        params = [
            {"title": "開始決定日", "type": "text", "value": pStartYMD_4},
            {"title": "終了決定日", "type": "text", "value": pEndYMD_4},
            {"title": "宛名コード", "type": "text", "value": pAtenaCode_4},
            {"title": "出力順", "type": "select", "value": pOutputOrder_4},
            {"title": "通知書区分", "type": "text", "value": noti_category_4},
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD_4}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_97")

        # 98 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 99 バッチ起動画面: 表示 メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_99")

        # 100 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        # self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 101 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_101")

        # 102 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 103 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_103")

        # 104 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_104")

        # 105 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 106 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_106")

        # 107 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 108 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_108")

        # 109 ジョブ帳票履歴画面: 「一部支給停止適用除外通知書」のNoボタン押下

        # 110 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 111 一部支給停止適用除外通知書（PDF）: 表示
        # self.screen_shot("一部支給停止適用除外通知書（PDF）_111")

        # 112 一部支給停止適用除外通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("一部支給停止適用除外通知書（PDF）_112")

        # 113 一部支給停止適用除外通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 114 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_114")

        # 115 ジョブ帳票履歴画面: 「支給停止解除通知書」のNoボタン押下

        # 116 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # self.print_online_reports(case_name="バッチ帳票履歴画面")

        # 117 支給停止解除通知書（PDF）: 表示
        # self.screen_shot("支給停止解除通知書（PDF）_117")

        # 118 支給停止解除通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("支給停止解除通知書（PDF）_118")

        # 119 支給停止解除通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 120 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_120")
