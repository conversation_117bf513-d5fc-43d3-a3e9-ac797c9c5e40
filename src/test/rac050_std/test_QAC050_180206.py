import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180206(FukushiSiteTestCaseBase):
    """TestQAC050_180206"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180206"]
        super().setUp()

    # 転入してきた住民に対し決定登録ができることを確認する。かつ証書番号が付与されていることを確認する。
    def test_QAC050_180206(self):
        """受給資格者台帳情報入力"""

        case_data = self.test_data["TestQAC050_180206"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 決定年月日「20230502」決定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230502")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.screen_shot("児童扶養手当資格管理画面_2")

        # 3 児童扶養手当資格管理画面: 児童のNoボタン押下
        self.click_by_id("CmdNo1")

        # 4 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_4")

        # 5 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 6 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_6")

        # 7 児童扶養手当資格管理画面: 「所得情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("所得情報")

        # 8 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_8")

        # 9 住民税世帯情報画面: 対象者世帯一覧「1」ボタン押下
        self.click_button_by_label("1")

        # 10 住民税個人情報画面: 表示
        self.screen_shot("住民税個人情報画面_10")

        # 11 住民税個人情報画面: 「戻る」ボタン押下
        self.return_click()

        # 12 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_12")

        # 13 住民税世帯情報画面: 「戻る」ボタン押下
        self.return_click()

        # 14 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_14")

        # 15 児童扶養手当資格管理画面: 「手帳情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("手帳情報")

        # 16 手帳情報画面: 表示
        self.screen_shot("手帳情報画面_16")

        # 17 手帳情報画面: 「戻る」ボタン押下
        self.return_click()

        # 18 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_18")

        # 19 児童扶養手当資格管理画面: 「年金情報登録」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("年金情報登録")

        # 20 年金情報画面: 表示
        self.screen_shot("年金情報画面_20")

        # 21 年金情報画面: 「戻る」ボタン押下
        self.return_click()

        # 22 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_22")

        # 23 児童扶養手当資格管理画面: 「福祉世帯情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("福祉世帯情報")

        # 24 福祉世帯情報画面: 表示
        self.screen_shot("福祉世帯情報画面_24")

        # 25 福祉世帯情報画面: 「戻る」ボタン押下
        self.return_click()
        self.alert_ok()
        # 25 福祉世帯情報画面: 「入力完了」ボタン押下
        # self.click_button_by_label("入力完了")

        # 26 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_26")

        # 27 児童扶養手当資格管理画面: 「メモ情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("メモ情報")

        # 28 メモ情報画面: 表示
        self.screen_shot("メモ情報画面_28")

        # 29 メモ情報画面: 「戻る」ボタン押下
        self.return_click()

        # 30 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_30")

        # 31 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("提出書類管理")

        # 32 提出書類管理: 表示
        self.screen_shot("提出書類管理_32")

        # 33 提出書類管理: 「戻る」ボタン押下
        self.return_click()

        # 34 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_34")

        # 35 児童扶養手当資格管理画面: 「住所管理」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("住所管理")

        # 36 住所管理画面: 表示
        self.screen_shot("住所管理画面_36")

        # 37 住所管理画面: 「戻る」ボタン押下
        self.return_click()

        # 38 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_38")

        # 39 児童扶養手当資格管理画面: 「連絡先管理」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("連絡先管理")

        # 40 連絡先管理: 表示
        self.screen_shot("連絡先管理_40")

        # 41 連絡先管理: 「戻る」ボタン押下
        self.return_click()

        # 42 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_42")

        # 43 児童扶養手当資格管理画面: 「口座情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("口座情報")

        # 44 口座情報画面: 表示
        self.screen_shot("口座情報画面_44")

        # 45 口座情報画面: 「戻る」ボタン押下
        self.return_click()

        # 46 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_46")

        # 47 児童扶養手当資格管理画面: 開始年月「202305」
        self.form_input_by_id(idstr="TxtKaitei", value="202305")
        self.screen_shot("児童扶養手当資格管理画面_47")

        # 48 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 49 児童扶養手当資格管理画面: 「所得判定詳細情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("所得判定詳細情報")

        # 50 所得判定詳細情報画面: 表示
        self.screen_shot("所得判定詳細情報画面_50")

        # 51 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 52 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_52")

        # 53 児童扶養手当資格管理画面: 「公的年金等停止額」ボタン押下
        self.click_button_by_label("公的年金等停止額")

        # 54 年金登録確認画面: 表示
        self.screen_shot("年金登録確認画面_54")

        # 55 年金登録確認画面: 「戻る」ボタン押下
        self.return_click()

        # 56 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_56")

        """
        # 57 児童扶養手当資格管理画面: 「被災情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("被災情報")

        # 58 被災情報画面: 表示
        self.screen_shot("被災情報画面_58")

        # 59 被災情報画面: 「戻る」ボタン押下
        self.return_click()

        # 60 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_60")
        """

        """
        # 61 児童扶養手当資格管理画面: 「不現住情報」ボタン押下
        self.click_button_by_label("不現住情報")

        # 62 不現住情報画面: 表示
        self.screen_shot("不現住情報画面_62")

        # 63 不現住情報画面: 「戻る」ボタン押下
        self.return_click()

        # 64 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_64")
        """

        # 65 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 66 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 67 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_67")
