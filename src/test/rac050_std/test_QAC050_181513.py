import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181513(FukushiSiteTestCaseBase):
    """TestQAC050_181513"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181513"]
        super().setUp()

    # 振込不能結果より振込不能情報を登録できることを確認する。
    def test_QAC050_181513(self):
        """振込不能登録"""

        case_data = self.test_data["TestQAC050_181513"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 「支払履歴」ボタン押下
        self.click_button_by_label("支払履歴")

        # 3 支払履歴画面: 表示
        self.screen_shot("支払履歴画面_3")

        # 4 支払履歴画面: 支払履歴一覧「1」Noボタン押下
        self.click_button_by_label("6")

        # 5 支払実績登録画面: 表示
        self.screen_shot("支払実績登録画面_5")

        # 6 支払実績登録画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 7 支払実績登録画面: 不能にチェック、振込不能理由「テスト」
        self.form_input_by_id(idstr="CbxImpossible", value="1")
        # self.form_input_by_id(idstr="TbxPayDate", value="20240801")
        self.screen_shot("支払実績登録画面_15")

        # 8 支払実績登録画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 9 支払実績登録画面: 表示
        self.screen_shot("支払履歴画面_9")

        # 10 支払実績登録画面: 「戻る」ボタン押下
        self.return_click()

        # 11 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 12 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 13 児童扶養手当資格管理画面: 表示
        self.assert_message_area("登録しました")
        self.screen_shot("児童扶養手当資格管理画面_21")
