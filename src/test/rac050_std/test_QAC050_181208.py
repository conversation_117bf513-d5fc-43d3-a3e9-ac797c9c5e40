import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181208(FukushiSiteTestCaseBase):
    """TestQAC050_181208"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181208"]
        super().setUp()

    # 障害認定通知書、在留延長認定通知書等を出力できることを確認する、
    def test_QAC050_181208(self):
        """障害等認定通知書等作成"""

        case_data = self.test_data["TestQAC050_181208"]
        atena_code = case_data.get("atena_code", "")
        hakkou_ymd = case_data.get("hakkou_ymd","")
        form_name_0 = case_data.get("form_name_0","")
        form_name_1 = case_data.get("form_name_1","")
        form_name_2 = case_data.get("form_name_2","")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_2")

        # 3 帳票印刷画面: 「障害認定通知書」行の印刷チェックボックス選択「障害認定通知書」行の発行年月日チェックボックス選択「障害認定通知書」行の発行年月日「20230702」「在留期間延長通知書」行の印刷チェックボックス選択「在留期間延長通知書」行の発行年月日チェックボックス選択「在留期間延長通知書」行の発行年月日「20230702」「児童扶養手当証書」行の印刷チェックボックス選択「児童扶養手当証書」行の発行年月日チェックボックス選択「児童扶養手当証書」行の発行年月日「20230702」
        exec_params = [
            {
                "report_name": form_name_0,
                "params":[
                    {"title": "発行年月日", "value":hakkou_ymd,"is_no_print":"1"},
                    {"title": "文書番号", "value":"12345"},
                ]
            }
        ]
        ret = self.print_online_reports(case_name="オンライン",report_param_list=exec_params)
        self.assert_message_area("プレビューを表示しました")
        self.screen_shot("帳票印刷画面_3")

        self.return_click()
        self.click_button_by_label("印刷")
        exec_params = [
            {
                "report_name": form_name_1,
                "params":[
                    {"title": "発行年月日", "value":hakkou_ymd,"is_no_print":"1"},
                    {"title": "文書番号", "value":"12345"},
                ]
            }
        ]
        ret = self.print_online_reports(case_name="オンライン",report_param_list=exec_params)
        self.assert_message_area("プレビューを表示しました")
        self.screen_shot("帳票印刷画面_4")

        self.return_click()
        self.click_button_by_label("印刷")
        exec_params = [
            {
                "report_name": form_name_2,
                "params":[
                    {"title": "発行年月日", "value":hakkou_ymd,"is_no_print":"1"},
                    {"title": "文書番号", "value":"12345"},
                ]
            }
        ]
        ret = self.print_online_reports(case_name="オンライン",report_param_list=exec_params)
        self.assert_message_area("プレビューを表示しました")
        self.screen_shot("帳票印刷画面_5")

        # 4 帳票印刷画面: 「印刷」ボタン押下
        # self.click_button_by_label("印刷")

        # 5 帳票印刷画面: 障害認定通知書「ファイルを開く(O)」ボタンを押下

        # 6 障害認定通知書（PDF）: 表示
        # self.screen_shot("障害認定通知書（PDF）_6")

        # 7 障害認定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 8 帳票印刷画面: 在留期間延長通知書「ファイルを開く(O)」ボタンを押下

        # 9 在留期間延長通知書（PDF）: 表示
        # self.screen_shot("在留期間延長通知書（PDF）_9")

        # 10 在留期間延長通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("在留期間延長通知書（PDF）_10")

        # 11 在留期間延長通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 12 帳票印刷画面: 児童扶養手当証書「ファイルを開く(O)」ボタンを押下

        # 13 児童扶養手当証書（PDF）: 表示
        # self.screen_shot("児童扶養手当証書（PDF）_13")

        # 14 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("児童扶養手当証書（PDF）_14")

        # 15 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # 16 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 17 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_17")

        # 18 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_18")

        # 19 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 20 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_20")

        # 21 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：通知書一括処理
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="通知書一括処理")

        # 22 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_22")

        # 23 バッチ起動画面: 「児童扶養手当証書受領書出力」のNoボタン押下
        # self.click_batch_job_button_by_label("児童扶養手当証書受領書出力")

        # 24 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_24")

        # 25 バッチ起動画面: 開始決定日「20230702」終了決定日「20230702」宛名コード「」出力順「証書番号順」選択通知書区分「5」発行年月日「20230702」
        # params = [
        #     {"title": "開始決定日", "type": "text", "value": "20230702"},
        #     {"title": "終了決定日", "type": "text", "value": "20230702"},
        #     {"title": "宛名コード", "type": "text", "value": ""},
        #     {"title": "出力順", "type": "select", "value": "証書番号順"},
        #     {"title": "通知書区分", "type": "text", "value": case_data.get("noti_category_5", "")},
        #     {"title": "発行年月日", "type": "text", "value": "20230702"}
        # ]
        # self.set_job_params(params)
        # self.screen_shot("バッチ起動画面_25")

        # 26 バッチ起動画面: 「処理開始」ボタン押下
        # exec_datetime = self.exec_batch_job()

        # 27 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        # self.assert_message_base_header("ジョブを起動しました")
        # self.screen_shot("バッチ起動画面_27")

        # 28 バッチ起動画面: 「実行履歴」ボタン押下
        # self.click_job_exec_log()

        # 29 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_29")

        # 30 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.wait_job_finished(120,20)
        # self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 31 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_31")

        # 32 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        #self.screen_shot("ジョブ実行履歴画面_32")

        # 33 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        # self.click_report_log()

        # 34 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_34")

        # 35 ジョブ帳票履歴画面: 「検索」ボタン押下
        # self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 36 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_36")

        # 37 ジョブ帳票履歴画面: 「児童扶養手当証書受領書」のNoボタン押下

        # 38 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 39 児童扶養手当証書受領書（PDF）: 表示
        # self.screen_shot("児童扶養手当証書受領書（PDF）_39")

        # 40 児童扶養手当証書受領書（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("児童扶養手当証書受領書（PDF）_40")

        # 41 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_41")

        # 42 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        # self.click_job_list()

        # 43 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_43")

        # 44 バッチ起動画面: 「処理一覧」ボタン押下
        # self.click_job_exec_log_search()

        # 45 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_45")

        # 46 バッチ起動画面: 「児童扶養手当証書一括出力」のNoボタン押下
        self.click_batch_job_button_by_label("児童扶養手当証書一括出力")

        # 47 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_47")

        # 48 バッチ起動画面: 開始決定日「20230702」終了決定日「20230702」宛名コード「」出力順「証書番号順」選択通知書区分「5」発行年月日「20230702」
        params = [
            {"title": "開始決定日", "type": "text", "value": "20230702"},
            {"title": "終了決定日", "type": "text", "value": "20230702"},
            {"title": "宛名コード", "type": "text", "value": ""},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "通知書区分", "type": "text", "value": case_data.get("noti_category_5", "")},
            {"title": "発行年月日", "type": "text", "value": "20230702"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_48")

        # 49 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 50 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_50")

        # 51 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 52 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_52")

        # 53 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 54 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_54")

        # 55 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_55")

        # 56 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 57 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_57")

        # 58 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 59 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_59")

        # 60 ジョブ帳票履歴画面: 「児童扶養手当証書」のNoボタン押下

        # 61 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 62 児童扶養手当証書（PDF）: 表示
        # self.screen_shot("児童扶養手当証書（PDF）_62")

        # 63 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("児童扶養手当証書（PDF）_63")

        # 64 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # 65 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_65")

        # 66 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 67 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_67")

        # 68 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 69 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_69")

        # 70 バッチ起動画面: 「支給認定通知書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("支給認定通知書出力処理")

        # 71 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_71")

        # 72 バッチ起動画面: 開始決定日「20230702」終了決定日「20230702」宛名コード「」出力順「証書番号順」選択発行年月日「20230702」
        # NG no ID
        params = [
            {"title": "開始決定日", "type": "text", "value": "20230702"},
            {"title": "終了決定日", "type": "text", "value": "20230702"},
            {"title": "宛名コード", "type": "text", "value": ""},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "発行年月日", "type": "text", "value": "20230702"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_72")

        # 73 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 74 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_74")

        # 75 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 76 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_76")

        # 77 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 78 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_78")

        # 79 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_79")

        # 80 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 81 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_81")

        # 82 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 83 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_83")

        # 84 ジョブ帳票履歴画面: 「障害認定通知書」のNoボタン押下

        # 85 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 86 障害認定通知書（PDF）: 表示
        # self.screen_shot("障害認定通知書（PDF）_86")

        # 87 障害認定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 88 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_88")

        # 89 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 90 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_90")

        # 91 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 92 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_92")

        # 93 バッチ起動画面: 「支給期間延長通知書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("支給期間延長通知書出力処理")

        # 94 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_94")

        # 95 バッチ起動画面: 開始決定日「20230702」終了決定日「20230702」宛名コード「」出力順「証書番号順」選択発行年月日「20230702」
        # NG no ID
        params = [
            {"title": "開始決定日", "type": "text", "value": "20230702"},
            {"title": "終了決定日", "type": "text", "value": "20230702"},
            {"title": "宛名コード", "type": "text", "value": ""},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "発行年月日", "type": "text", "value": "20230702"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_95")

        # 96 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 97 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_97")

        # 98 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 99 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_99")

        # 100 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 101 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_101")

        # 102 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_102")

        # 103 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 104 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_104")

        # 105 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 106 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_106")

        # 107 ジョブ帳票履歴画面: 「在留期間延長通知書」のNoボタン押下

        # 108 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 109 在留期間延長通知書（PDF）: 表示
        # self.screen_shot("在留期間延長通知書（PDF）_109")

        # 110 在留期間延長通知書（PDF）: 2ページ目表示
        # self.screen_shot("在留期間延長通知書（PDF）_110")

        # 111 在留期間延長通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 112 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_112")
