import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180401(FukushiSiteTestCaseBase):
    """TestQAC050_180401"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180401"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC050", "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
                      "TARGET_NENDO":case_data.get("nendo", ""),"TARGET_SOUSYOTOKU":case_data.get("soushotoku", "")}
        self.exec_sqlfile("Test_QAC050_180401.sql", params=sql_params)
        sql_params = {"TARGET_GYOUMU_CODE": "QAC050", "DELETE_ATENA_CODE": case_data.get("atena_code2", ""),
                      "TARGET_NENDO":case_data.get("nendo", ""),"TARGET_SOUSYOTOKU":case_data.get("soushotoku", "")}
        self.exec_sqlfile("Test_QAC050_180401.sql", params=sql_params)
        super().setUp()

    # 額改定届を提出した住民および対象児童や、その他必要な情報の登録ができることを確認する。
    def test_QAC050_180401(self):
        """届出情報入力"""

        case_data = self.test_data["TestQAC050_180401"]
        atena_code = case_data.get("atena_code", "")
        txtShinseiYMD = case_data.get("txtShinseiYMD", "")
        txtJiyuHasseiYMD = case_data.get("txtJiyuHasseiYMD", "")
        txtHiGaitoYMD = case_data.get("txtHiGaitoYMD", "")
        higaitojiyu = case_data.get("higaitojiyu", "")
        hiGaitoYMDtxt_3 = case_data.get("hiGaitoYMDtxt_3", "")
        atena_code2 = case_data.get("atena_code2", "")

        ### 認定履歴作成 START###
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        self.click_button_by_label("申請内容入力")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
        self.click_button_by_label("確定")
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230325")
        self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20230501")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("2")
        self.form_input_by_id(idstr="CmbZokugara", text="子")
        self.form_input_by_id(idstr="RdoDokyo1", value="1")
        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        self.form_input_by_id(idstr="KojiGaitou2", value="1")
        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        self.form_input_by_id(idstr="RdoShogai2", value="1")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("3")
        self.form_input_by_id(idstr="CmbZokugara", text="子")
        self.form_input_by_id(idstr="RdoDokyo1", value="1")
        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        self.form_input_by_id(idstr="KojiGaitou2", value="1")
        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        self.form_input_by_id(idstr="RdoShogai2", value="1")
        self.click_button_by_label("福祉世帯情報")
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230501")
        self.form_input_by_id(idstr="ChkFlg_4", value="1")
        self.form_input_by_id(idstr="HoninCmb_2", text="子")
        self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20230501")
        self.form_input_by_id(idstr="HoninCmb_3", text="子")
        self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20230501")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("所得情報")
        self.click_button_by_label("1")
        self.return_click()
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("口座情報")
        self.click_button_by_label("追加")
        self.entry_kouza_info(
            start_ymd="20230501",
            ginko_code="0005",
            shiten_code="001",
            kouza_shubetsu_text="普通",
            kouza_bango="7777777",
            koukai=True
        )
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.form_input_by_id(idstr="TxtKaitei", value="202305")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230401")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
        self.click_button_by_label("決定内容入力") 
        self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230401")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        #self.form_input_by_id(idstr="TxtShoushoBango", value="180701")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        #支払履歴入力(支払済にする)
        self.click_button_by_label("修正")
        self.open_common_buttons_area()
        self.click_button_by_label("支払履歴")
        self.click_button_by_label("6")
        self.click_button_by_label("修正")
        self.form_input_by_id(idstr="TbxPayDate", value="20230901")
        self.form_input_by_id(idstr="TbxDecisionDate", value="20230901")
        self.form_input_by_id(idstr="CmbPaymentMethod", text="窓口")
        self.click_button_by_label("最新口座")
        self.click_button_by_label("登録")
        self.alert_ok()

        self.click_button_by_label("5")
        self.click_button_by_label("修正")
        self.form_input_by_id(idstr="TbxPayDate", value="20230901")
        self.form_input_by_id(idstr="TbxDecisionDate", value="20230901")
        self.form_input_by_id(idstr="CmbPaymentMethod", text="窓口")
        self.click_button_by_label("最新口座")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        ### 認定履歴作成 END###
        ### 認定履歴作成 START###
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code2, gyoumu_code="QAC050")
        self.click_button_by_label("申請内容入力")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
        self.click_button_by_label("確定")
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230325")
        self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20230501")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("2")
        self.form_input_by_id(idstr="CmbZokugara", text="子")
        self.form_input_by_id(idstr="RdoDokyo1", value="1")
        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        self.form_input_by_id(idstr="KojiGaitou2", value="1")
        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        self.form_input_by_id(idstr="RdoShogai2", value="1")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("児童追加")
        self.click_button_by_label("3")
        self.form_input_by_id(idstr="CmbZokugara", text="子")
        self.form_input_by_id(idstr="RdoDokyo1", value="1")
        self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        self.form_input_by_id(idstr="KojiGaitou2", value="1")
        self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        self.form_input_by_id(idstr="RdoShogai2", value="1")
        self.click_button_by_label("福祉世帯情報")
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230501")
        self.form_input_by_id(idstr="ChkFlg_4", value="1")
        self.form_input_by_id(idstr="HoninCmb_2", text="子")
        self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20230501")
        self.form_input_by_id(idstr="HoninCmb_3", text="子")
        self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20230501")
        self.click_button_by_label("入力完了")
        self.click_button_by_label("所得情報")
        self.click_button_by_label("1")
        self.return_click()
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("口座情報")
        self.click_button_by_label("追加")
        self.entry_kouza_info(
            start_ymd="20230501",
            ginko_code="0005",
            shiten_code="001",
            kouza_shubetsu_text="普通",
            kouza_bango="7777777",
            koukai=True
        )
        self.click_button_by_label("登録")
        self.alert_ok()
        self.return_click()
        self.form_input_by_id(idstr="TxtKaitei", value="202305")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("本庁進達入力")
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230401")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
            self.click_button_by_label("本庁進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました")
        self.click_button_by_label("決定内容入力") 
        self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230401")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        #self.form_input_by_id(idstr="TxtShoushoBango", value="180701")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        #支払履歴入力(支払済にする)
        self.click_button_by_label("修正")
        self.open_common_buttons_area()
        self.click_button_by_label("支払履歴")
        self.click_button_by_label("6")
        self.click_button_by_label("修正")
        self.form_input_by_id(idstr="TbxPayDate", value="20230901")
        self.form_input_by_id(idstr="TbxDecisionDate", value="20230901")
        self.form_input_by_id(idstr="CmbPaymentMethod", text="窓口")
        self.click_button_by_label("最新口座")
        self.click_button_by_label("登録")
        self.alert_ok()

        self.click_button_by_label("5")
        self.click_button_by_label("修正")
        self.form_input_by_id(idstr="TbxPayDate", value="20230901")
        self.form_input_by_id(idstr="TbxDecisionDate", value="20230901")
        self.form_input_by_id(idstr="CmbPaymentMethod", text="窓口")
        self.click_button_by_label("最新口座")
        self.click_button_by_label("登録")
        self.alert_ok()
        #二人目は額改定の履歴まで作っておく
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code2, gyoumu_code="QAC050")
        self.click_button_by_label("申請内容入力")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=case_data.get("shinsei_shubetsu", ""))
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("shinsei_riyu", ""))
        self.click_button_by_label("確定")
        self.form_input_by_id(idstr="TxtShinseiYMD", value=txtShinseiYMD)
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value=txtJiyuHasseiYMD)
        self.form_input_by_id(idstr="TxtKaitei", value="202306")
        self.click_by_id("CmdNo2")
        self.form_input_by_id(idstr="TxtHiGaitoYMD", value=txtHiGaitoYMD)
        self.form_input_by_id(idstr="CmbHiGaitoJiyu", text=higaitojiyu)
        self.click_button_by_label("入力完了")
        self.open_common_buttons_area()
        self.common_button_click("福祉世帯情報")
        self.form_input_by_id(idstr="HiGaitoYMDtxt_3", value=hiGaitoYMDtxt_3)
        self.click_button_by_label("入力完了")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        ### 認定履歴作成 END###

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.screen_shot("個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「児童扶養手当」ボタン押下
        self.click_button_by_label("児童扶養手当")

        # 8 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_8")

        # 9 児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 児童扶養手当資格管理画面: 申請種別「額改定（減額）」選択申請理由「未婚」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=case_data.get("shinsei_shubetsu", ""))
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("shinsei_riyu", ""))
        self.screen_shot("児童扶養手当資格管理画面_10")

        # 11 児童扶養手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 児童扶養手当資格管理画面: 請求年月日「20230501」事由発生年月日「20230429」
        self.form_input_by_id(idstr="TxtShinseiYMD", value=txtShinseiYMD)
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value=txtJiyuHasseiYMD)
        self.screen_shot("児童扶養手当資格管理画面_12")

        # 13 児童扶養手当資格管理画面: 児童「2」Noボタン押下
        self.form_input_by_id(idstr="TxtKaitei", value="202306")
        self.click_by_id("CmdNo2")

        # 14 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_14")

        # 15 支給対象児童入力画面: 非該当年月日「20230531」非該当事由「施設入所」
        self.form_input_by_id(idstr="TxtHiGaitoYMD", value=txtHiGaitoYMD)
        self.form_input_by_id(idstr="CmbHiGaitoJiyu", text=higaitojiyu)
        self.screen_shot("支給対象児童入力画面_15")

        # 16 支給対象児童入力画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 17 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_17")

        # 18 児童扶養手当資格管理画面: 「福祉世帯情報」ボタン押下
        self.open_common_buttons_area()
        self.common_button_click("福祉世帯情報")

        # 19 福祉世帯情報画面: 表示
        self.screen_shot("福祉世帯情報画面_19")

        # 20 福祉世帯情報画面: 3に対して非該当日「20230531」
        self.form_input_by_id(idstr="HiGaitoYMDtxt_3", value=hiGaitoYMDtxt_3)
        self.screen_shot("福祉世帯情報画面_20")

        # 21 福祉世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 22 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_22")

        # 23 児童扶養手当資格管理画面: 「メモ情報」ボタン押下
        self.open_common_buttons_area()
        self.common_button_click("メモ情報")

        # 24 メモ情報画面: 表示
        self.screen_shot("メモ情報画面_24")

        # 25 メモ情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 26 メモ情報画面: 内容「あいうえお」
        self.entry_memo_info(text="あいうえお")
        self.screen_shot("メモ情報画面_26")

        # 27 メモ情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 28 メモ情報画面: 「戻る」ボタン押下
        self.return_click()

        # 29 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_29")

        # 30 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.open_common_buttons_area()
        self.common_button_click("提出書類管理")

        # 31 提出書類管理: 表示
        self.screen_shot("提出書類管理_31")

        # 32 提出書類管理: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 33 提出書類管理: 戸籍謄抄本にチェック
        self.form_input_by_id(idstr="ChkTeisyutsu_1", value="1")
        #self.entry_teishutsu_shorui(shorui_name="戸籍謄抄本", is_check=True)
        self.screen_shot("提出書類管理_33")

        # 34 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 35 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_35")

        # 36 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 37 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 38 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_38")
