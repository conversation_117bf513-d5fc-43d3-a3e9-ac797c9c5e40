import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181216(FukushiSiteTestCaseBase):
    """TestQAC050_181216"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181216"]
        super().setUp()

    # 内払い調整の登録ができることを確認する。
    def test_QAC050_181216(self):
        """内払額調整"""

        case_data = self.test_data["TestQAC050_181216"]
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        time.sleep(3)
        self.click_button_by_label("確定")

        # 2 児童扶養手当資格管理画面: 「支払調整履歴画面」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("支払調整履歴")

        # 3 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_3")

        # 4 支払調整履歴画面: 該当者一覧 「1」Noボタン押下
        self.click_button_by_label("1")

        # 5 支払調整登録: 表示
        self.screen_shot("支払調整登録_5")

        # 6 支払調整登録: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # NGStep7-8-9 no ID
        # 7 支払調整登録: 調整債権区分「全額調整」チェック返納予定額「0」「計算」ボタン押下
        self.form_input_by_id(idstr="RadioZengakuC", value="1")
        self.click_button_by_label("計算")

        # 8 支払調整登録: 調整額「10000」「差引支払額計算」ボタン押下
        self.form_input_by_id(idstr="TxtKChoseiGaku", value="10000")
        self.click_button_by_label("期別調整額設定")
        self.click_button_by_label("差引支払額計算")

        # 9 支払調整登録: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.screen_shot("支払調整登録_9")

        # 10 支払調整登録: 「戻る」ボタン押下
        #self.return_click()

        # 11 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_11")

        # 12 支払調整履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 13 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_13")

        # 14 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 15 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 16 児童扶養手当資格管理画面: 表示
        # Assert: ｢登録しました。｣のメッセージのチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_16")
