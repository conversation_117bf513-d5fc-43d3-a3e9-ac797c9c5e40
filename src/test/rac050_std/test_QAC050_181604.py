import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181604(FukushiSiteTestCaseBase):
    """TestQAC050_181604"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181604"]
        super().setUp()

    # 内払い調整の登録ができることを確認する。
    def test_QAC050_181604(self):
        """内払額調整"""

        case_data = self.test_data["TestQAC050_181604"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「支払調整」ボタン押下
        self.shiharai_chousei_click()  # Button with ID: CmdProcess7_1 instead of self.click_button_by_label("支払調整")

        # 3 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_3")

        # 4 支払調整履歴画面: 事業「児童扶養手当」選択絞り込み条件「未調整のみ」チェック
        self.form_input_by_id(idstr="CmbGyomu", text="児童扶養手当")
        self.form_input_by_id(idstr="RadioM", value="1")
        self.screen_shot("支払調整履歴画面_4")

        # 5 支払調整履歴画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 6 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_6")

        # 7 支払調整履歴画面: 該当者一覧 「7」Noボタン押下
        self.click_button_by_label("7")  # Instead of self.click_button_by_label("該当者一覧 1No")

        # 8 支払調整登録: 表示
        self.screen_shot("支払調整登録_8")

        # 9 支払調整登録: 「修正」ボタン押下
        self.click_button_by_label("修正")  # Button with ID: CmdShusei

        # 10 支払調整登録: 調整債権区分「全額調整」チェック返納予定額「0」
        self.form_input_by_id(idstr="RadioZengakuC", value="1")
        self.form_input_by_id(idstr="TxtHYoteiGaku", value="0")
        self.screen_shot("支払調整登録_10")

        # 11 支払調整登録: 「計算」ボタン押下
        self.click_button_by_label("計算")  # Button with ID: CmdKeisan

        # 12 支払調整登録: 調整額「30000」
        self.form_input_by_id(idstr="TxtKChoseiGaku", value="30000")
        self.form_input_by_id(idstr="TxtChoseiGaku_1", value="20000")
        # TODO The spec has an additional id: CmdSSGKeisan but there is no description in the comment
        self.screen_shot("支払調整登録_12")

        # 13 支払調整登録: 「差引支払額計算」ボタン押下
        self.click_button_by_label("差引支払額計算")  # Button with ID: CmdSSGKeisan instead of CmdTouroku (spec note)

        # 14 支払調整登録: 「登録」ボタン押下
        self.click_button_by_label("登録")  # Button with ID: CmdTouroku instead of GOBACK (spec note)
        self.alert_ok()

        # 15 支払調整登録: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("支払調整登録_15")

        # 16 支払調整登録: 「戻る」ボタン押下
        # self.return_click()

        # 17 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_17")

        # 18 支払調整履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 19 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_19")
