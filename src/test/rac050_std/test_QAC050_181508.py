import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181508(FukushiSiteTestCaseBase):
    """TestQAC050_181508"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181508"]
        super().setUp()

    # 内払い決定通知書を出力できることを確認する。
    def test_QAC050_181508(self):
        """内払調整結果通知書作成"""

        case_data = self.test_data["TestQAC050_181508"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「支払調整」ボタン押下
        self.shiharai_chousei_click()  # Button with ID: CmdProcess7_1  instead of self.click_button_by_label("支払調整")

        # 3 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_3")

        # 4 支払調整履歴画面: 事業「児童扶養手当」選択絞り込み条件「調整中のみ」チェック
        self.form_input_by_id(idstr="CmbGyomu", text="児童扶養手当")
        self.form_input_by_id(idstr="RadioC", value="1")
        self.screen_shot("支払調整履歴画面_4")

        # 5 支払調整履歴画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 6 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_6")

        # 7 支払調整履歴画面: 該当者一覧 Noボタン押下
        atena_col = '3'
        table_idx = 0
        sonzaiFlg = False
        tb_Page = self.find_element_by_xpath('//*[@id="_wr_body_panel"]/table[5]/tbody/tr/td/b')
        maxPage = str(tb_Page.text).replace("／","")
        for page_index in range(int(maxPage)):
            tr_elem = self.find_elements_by_css_selector("#_wrFk_TitleTable_Div_1 > table > tbody > tr")
            table_idx = 0
            for elem in tr_elem:
                table_idx += 1
                td_elem = elem.find_element(By.CSS_SELECTOR, "td:nth-child(" + atena_col + ")")
                if atena_code == td_elem.text:
                    sonzaiFlg = True
                    self.click_by_id("Sel" + str(table_idx))
                    break
            if sonzaiFlg == False:
                self.click_by_id("CmdNextPage")
            else:
                break

        # 8 支払調整登録画面: 表示
        self.screen_shot("支払調整登録画面_8")

        # 9 支払調整登録画面: 過払年月日「20230110」発行年月日「20230502」
        self.form_input_by_id(idstr="TxtKabaraiYMD", value="20230110") 
        self.form_input_by_id(idstr="TxtHakkouYMD", value="20230502") 
        self.screen_shot("支払調整登録画面_9")

        # 10 支払調整登録画面: 「印刷」ボタン押下
        self.pdf_output_and_download_no_alert("CmdInnsatuBTN",case_name="内払い決定通知書")

        # 11 支払調整登録画面: 内払い決定通知書「ファイルを開く(O)」ボタンを押下

        # 12 内払い決定通知書（PDF）: 表示
        self.screen_shot("支払調整登録画面_12")

        # 13 内払い決定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 14 支払調整登録画面: 表示
        self.screen_shot("支払調整登録画面_14")

        # 15 支払調整登録画面: 「戻る」ボタン押下
        self.return_click()

        # 16 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_16")

        # 17 支払調整履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 18 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_18")
