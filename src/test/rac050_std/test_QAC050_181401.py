import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181401(FukushiSiteTestCaseBase):
    """TestQAC050_181401"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181401"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC050",
                      "TARGET_NENDO": case_data.get("nendoY", ""),
                      "TARGET_NENDO_2": case_data.get("nendoY_2", ""),
                      "INSERT_ATENA_CODE_1_1": case_data.get("atena_code_1_1", ""),
                      "INSERT_ATENA_CODE_2_1": case_data.get("atena_code_2_1", "")}
        self.exec_sqlfile("Test_QAC050_181401.sql", params=sql_params)
        super().setUp()

    # 一部支給停止措置対象者の抽出ができることを確認する。
    def test_QAC050_181401(self):
        """一部支給停止措置対象抽出"""

        case_data = self.test_data["TestQAC050_181401"]
        atena_code_1_1 = case_data.get("atena_code_1_1", "")
        preShinseiShubetsuCmb_1 = case_data.get("preShinseiShubetsuCmb_1", "")
        preShinseiRiyuuCmb_1 = case_data.get("preShinseiRiyuuCmb_1", "")
        preTxtShinseiYMD_1 = case_data.get("preTxtShinseiYMD_1", "")
        preJyukyusyaKbnCmb_1 = case_data.get("preJyukyusyaKbnCmb_1", "")
        preTxtpreShikyuKaishiYMD_1 = case_data.get("preTxtpreShikyuKaishiYMD_1", "")
        preGengakuYMChkBox_1 = case_data.get("preGengakuYMChkBox_1", "")
        preTxtGengakuYM_1 = case_data.get("preTxtGengakuYM_1", "")
        preTxtKaitei_1 = case_data.get("preTxtKaitei_1", "")
        preTxtGaitoYMD_1 = case_data.get("preTxtGaitoYMD_1", "")
        preCmbGaitoJiyu_1 = case_data.get("preCmbGaitoJiyu_1", "")
        preTxtJiyuYMD_1 = case_data.get("preTxtJiyuYMD_1", "")
        preTxtToushoShikyuYM_1 = case_data.get("preTxtToushoShikyuYM_1", "")
        preTxtShintatsu1YMD_1 = case_data.get("preTxtShintatsu1YMD_1", "")
        preShintatsu1HanteiCmb_1 = case_data.get("preShintatsu1HanteiCmb_1", "")
        preTxtKetteiYMD_1 = case_data.get("preTxtKetteiYMD_1", "")
        preKetteiKekkaCmb_1 = case_data.get("preKetteiKekkaCmb_1", "")
        atena_code_2_1 = case_data.get("atena_code_2_1", "")
        preTxtShinseiYMD_2 = case_data.get("preTxtShinseiYMD_2", "")
        preTxtpreShikyuKaishiYMD_2 = case_data.get("preTxtpreShikyuKaishiYMD_2", "")
        preTxtGengakuYM_2 = case_data.get("preTxtGengakuYM_2", "")
        preTxtKaitei_2 = case_data.get("preTxtKaitei_2", "")
        preTxtGaitoYMD_2 = case_data.get("preTxtGaitoYMD_2", "")
        preTxtJiyuYMD_2 = case_data.get("preTxtJiyuYMD_2", "")
        preTxtToushoShikyuYM_2 = case_data.get("preTxtToushoShikyuYM_2", "")
        preTxtShintatsu1YMD_2 = case_data.get("preTxtShintatsu1YMD_2", "")
        preTxtKetteiYMD_2 = case_data.get("preTxtKetteiYMD_2", "")
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        pTaishoYM = case_data.get("pTaishoYM", "")
        pZenbuTeishi = case_data.get("pZenbuTeishi", "")
        pOutputOrder = case_data.get("pOutputOrder", "")
        pAtenaCodeS = case_data.get("pAtenaCodeS", "")
        pGyomuSelect_2 = case_data.get("pGyomuSelect_2", "")
        pJigyoSelect_2 = case_data.get("pJigyoSelect_2", "")
        pShoriKubunSelect_2 = case_data.get("pShoriKubunSelect_2", "")
        pShoriBunruiSelect_2 = case_data.get("pShoriBunruiSelect_2", "")
        pTaishoY = case_data.get("pTaishoY", "")
        pSabunOutpu_2 = case_data.get("pSabunOutpu_2", "")
        pZenbuTeishi_2 = case_data.get("pZenbuTeishi_2", "")
        pOutputOrder_2 = case_data.get("pOutputOrder_2", "")
        
        # ＜対象者の条件＞
        # ・児童扶養手当の資格を持っている住民
        # ・本人と児童がいる世帯の住民
        # ・児童扶養手当の減額開始年月が202308～202407
        self.do_login()
        self.shinsei_shikaku_kanri_click()
        self.kojin_kensaku_by_atena_code(atena_code=atena_code_1_1)
        self.click_button_by_label("児童扶養手当")
        self.click_button_by_label("申請内容入力")
        # 申請種別
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=preShinseiShubetsuCmb_1)
        # 申請理由
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=preShinseiRiyuuCmb_1)
        self.click_button_by_label("確定")
        # 請求年月日
        self.form_input_by_id(idstr="TxtShinseiYMD", value=preTxtShinseiYMD_1)
        # 受給者区分
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text=preJyukyusyaKbnCmb_1)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value=preTxtpreShikyuKaishiYMD_1)
        # 減額開始年月
        self.form_input_by_id(idstr="GengakuYMChkBox", value=preGengakuYMChkBox_1)
        self.form_input_by_id(idstr="TxtGengakuYM", value=preTxtGengakuYM_1)
        # 開始年月
        self.form_input_by_id(idstr="TxtKaitei", value=preTxtKaitei_1)
        self.click_button_by_label("児童追加")
        self.click_button_by_label("2")
        # 該当年月日
        self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
        # 該当事由
        self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
        self.click_button_by_label("入力完了")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達入力")
        # 進達年月日
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=preTxtShintatsu1YMD_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達結果入力")
        # 進達結果
        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=preShintatsu1HanteiCmb_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("決定内容入力")
        # 決定年月日
        self.form_input_by_id(idstr="TxtKetteiYMD", value=preTxtKetteiYMD_1)
        # 決定結果
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=preKetteiKekkaCmb_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        # ＜対象者の条件＞
        # ・児童扶養手当の資格を持っている住民
        # ・本人と児童がいる世帯の住民
        # ・児童扶養手当の減額開始年月が202307以前
        self.do_login()
        self.shinsei_shikaku_kanri_click()
        self.kojin_kensaku_by_atena_code(atena_code=atena_code_2_1)
        self.click_button_by_label("児童扶養手当")
        self.click_button_by_label("申請内容入力")
        # 申請種別
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=preShinseiShubetsuCmb_1)
        # 申請理由
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=preShinseiRiyuuCmb_1)
        self.click_button_by_label("確定")
        # 請求年月日
        self.form_input_by_id(idstr="TxtShinseiYMD", value=preTxtShinseiYMD_2)
        # 受給者区分
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text=preJyukyusyaKbnCmb_1)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value=preTxtpreShikyuKaishiYMD_2)
        # 減額開始年月
        self.form_input_by_id(idstr="GengakuYMChkBox", value=preGengakuYMChkBox_1)
        self.form_input_by_id(idstr="TxtGengakuYM", value=preTxtGengakuYM_2)
        # 開始年月
        self.form_input_by_id(idstr="TxtKaitei", value=preTxtKaitei_2)
        self.click_button_by_label("児童追加")
        self.click_button_by_label("2")
        # 該当年月日
        self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_2)
        # 該当事由
        self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_2)
        # 当初支給開始年月日
        self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_2)
        self.click_button_by_label("入力完了")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達入力")
        # 進達年月日
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=preTxtShintatsu1YMD_2)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("本庁進達結果入力")
        # 進達結果
        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=preShintatsu1HanteiCmb_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.click_button_by_label("決定内容入力")
        # 決定年月日
        self.form_input_by_id(idstr="TxtKetteiYMD", value=preTxtKetteiYMD_2)
        # 決定結果
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=preKetteiKekkaCmb_1)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：13条の3関連処理分類：月次
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect)

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「事前通知対象者_一覧_月次」のNoボタン押下
        self.click_batch_job_button_by_label("事前通知対象者_一覧_月次")

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: 対象年月「202406」全部停止出力区分「0」出力順「証書番号順」選択 宛名コード開始「100300000100001」
        params = [
            {"title": "対象年月", "type": "text", "value": pTaishoYM},
            {"title": "全部停止出力区分", "type": "text", "value": pZenbuTeishi},
            {"title": "出力順", "type": "select", "value": pOutputOrder},
            {"title": "宛名コード開始", "type": "text", "value": pAtenaCodeS}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 19 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「事前通知対象者一覧月次」のNoボタン押下

        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 22 事前通知対象者一覧月次（PDF）: 表示
        # self.screen_shot("事前通知対象者一覧月次（PDF）_22")

        # 23 事前通知対象者一覧月次（PDF）: ×ボタン押下でPDFを閉じる

        # 24 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_24")

        # 25 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_25")

        # 26 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 27 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_27")

        # 28 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：13条の3関連処理分類：年次
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect_2)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect_2)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect_2)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect_2)

        # 29 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_29")

        # 30 バッチ起動画面: 「事前通知対象者_一覧_年次」のNoボタン押下
        self.click_batch_job_button_by_label("事前通知対象者_一覧_年次")

        # 31 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_31")

        # 32 バッチ起動画面: 対象年度「令和05年」全部停止出力区分「0」出力順「証書番号順」選択
        params = [
            {"title": "対象年度", "type": "select", "value": pTaishoY},
            {"title": "全部停止出力区分", "type": "text", "value": pZenbuTeishi_2},
            {"title": "出力順", "type": "select", "value": pOutputOrder_2}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_32")

        # 33 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 34 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_34")

        # 35 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 36 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_36")

        # 37 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 38 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_38")

        # 39 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_39")

        # 40 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 41 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_41")

        # 42 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 43 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_43")

        # 44 ジョブ帳票履歴画面: 「事前通知対象者一覧年次」のNoボタン押下

        # 45 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 46 事前通知対象者一覧年次（PDF）: 表示
        # self.screen_shot("事前通知対象者一覧年次（PDF）_46")

        # 47 事前通知対象者一覧月次（PDF）: ×ボタン押下でPDFを閉じる

        # 48 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_48")
