import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180402(FukushiSiteTestCaseBase):
    """TestQAC050_180402"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180402"]
        super().setUp()

    # 不備があった書類の登録ができることを確認する。
    def test_QAC050_180402(self):
        """不備書類入力"""

        case_data = self.test_data["TestQAC050_180402"]
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        self.click_button_by_label("確定")

        # 2 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.open_common_buttons_area()
        self.common_button_click("提出書類管理")

        # 3 提出書類管理: 表示
        self.screen_shot("提出書類管理_3")

        # 4 提出書類管理: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 5 提出書類管理: 所得証明書にチェック
        self.form_input_by_id(idstr="ChkTeisyutsu_3", value="1")
        #self.entry_teishutsu_shorui(shorui_name="戸籍謄抄本", is_check=True, date_ymd="")
        self.screen_shot("提出書類管理_5")

        # 6 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 7 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_7")

        # 8 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 9 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 10 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_10")
