import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180807(FukushiSiteTestCaseBase):
    """TestQAC050_180807"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180807"]
        super().setUp()

    # 資格喪失届を提出した住民に対し決定登録ができることを確認する。
    def test_QAC050_180807(self):
        """登録情報変更処理"""

        case_data = self.test_data["TestQAC050_180807"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.do_login()
        # 1 児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("修正")
        # 2 児童扶養手当資格管理画面: 決定年月日「20230602」決定結果「決定」改定年月「202307」
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230602")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.form_input_by_id(idstr="TxtKaitei", value="202307")
        self.screen_shot("児童扶養手当資格管理画面_2")

        # 3 児童扶養手当資格管理画面: 児童のNoボタン押下
        self.click_button_by_label("1")  # self.click_by_id("CmdNo1")

        # 4 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_4")

        # 5 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 6 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_6")

        # 7 児童扶養手当資格管理画面: 「住所管理」ボタン押下
        self.click_button_by_label("住所管理")

        # 8 住所管理画面: 表示
        self.screen_shot("住所管理画面_8")

        # 9 住所管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 10 住所管理画面: 方書「市内住所変更」
        self.form_input_by_id(idstr="TxtKataGaki", value="市内住所変更")
        self.screen_shot("住所管理画面_10")

        # 11 住所管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 12 住所管理画面: 「戻る」ボタン押下
        self.return_click()

        # 13 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_13")

        # 14 児童扶養手当資格管理画面: 「連絡先管理」ボタン押下
        self.click_button_by_label("連絡先管理")

        # 15 連絡先管理: 表示
        self.screen_shot("連絡先管理_15")

        # 16 連絡先管理: 「戻る」ボタン押下
        self.return_click()

        # 17 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_17")

        # 18 児童扶養手当資格管理画面: 「口座情報」ボタン押下
        self.click_button_by_label("口座情報")

        # 19 口座情報画面: 表示
        self.screen_shot("口座情報画面_19")

        # 20 口座情報画面: 「戻る」ボタン押下
        self.return_click()

        # 21 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_21")

        # 22 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 23 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")  # Button with ID: CmdTouroku
        self.alert_ok()

        # 24 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_24")
