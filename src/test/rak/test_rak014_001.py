import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAK014001(FukushiSiteTestCaseBase):
    """TESTRAK014001"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("RAK014.sql", params=atena_list)
        super().setUp()
    
    def test_case_rak014_001(self):
        """test_case_rak014_001"""
        driver = None
        test_data = self.common_test_data
        self.do_login()
        
        self.click_button_by_label("収納情報管理")
        self.save_screenshot_migrate(driver, "RAK014-001-02", True)
        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("rak014_atena_code"))
        self.find_element(By.ID,"Kensaku").click()
        self.save_screenshot_migrate(driver, "RAK014-001-05", True)

        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="納付証明書発行")
        self.save_screenshot_migrate(driver, "RAK014-001-07", True)

        self.find_element(By.ID,"span_CmdDown").click()
        self.save_screenshot_migrate(driver, "RAK014-001-08", True)

        self.find_element(By.ID,"span_CmdUp").click()
        self.save_screenshot_migrate(driver, "RAK014-001-09", True)
        self.find_element(By.ID,"span_CmdDown").click()

        self.find_element(By.ID,"span_CmdKensaku").click()
        self.save_screenshot_migrate(driver, "RAK014-001-10", True)

        self.find_element(By.ID,"TxtHakkoYMD").click()
        self.find_element(By.ID,"TxtHakkoYMD").send_keys("")
        self.find_element(By.ID,"TxtHakkoYMD").send_keys(u"令和03年09月10日")
        self.find_element(By.ID,"CmbChohyo").send_keys("保険料納付証明書")
        
        self.find_element(By.ID,"CmdPrint").click()
        self.assertEqual(u"印刷します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK014-001-13", True)

        self.find_element(By.ID,"NoBtn1").click()
        self.find_element(By.ID,"TxtNouhuYMD").click()
        self.find_element(By.ID,"TxtNouhuYMD").send_keys("")
        self.find_element(By.ID,"TxtNouhuYMD").send_keys(u"令和03年09月14日")
        
        self.find_element(By.ID,"span_CmdComp").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK014-001-18", True)

        self.find_element(By.ID,"CmdAdd").click()
        self.save_screenshot_migrate(driver, "RAK014-001-21", True)

        self.find_element(By.ID,"span_CmdCancel").click()
        self.save_screenshot_migrate(driver, "RAK014-001-22", True)

        self.find_element(By.ID,"ChkPrint").click()
        self.save_screenshot_migrate(driver, "RAK014-001-23", True)
        self.find_element(By.ID,"TxtYoteigaku").click()
        self.find_element(By.ID,"TxtYoteigaku").send_keys("")
        self.find_element(By.ID,"TxtYoteigaku").send_keys("10000")
        self.save_screenshot_migrate(driver, "RAK014-001-24", True)
        
        self.find_element(By.ID,"CmbYoteiMonth").send_keys("9月")  
        self.save_screenshot_migrate(driver, "RAK014-001-25", True)      
        self.find_element(By.ID,"TxtYoteiDay").click()
        self.find_element(By.ID,"TxtYoteiDay").send_keys("")
        self.find_element(By.ID,"TxtYoteiDay").send_keys("24")
        self.save_screenshot_migrate(driver, "RAK014-001-26", True)
        self.find_element(By.ID,"CmdKeisan").click()
        self.save_screenshot_migrate(driver, "RAK014-001-27", True)

        self.find_element(By.ID,"span_CmdShoki").click()
        self.save_screenshot_migrate(driver, "RAK014-001-28", True)

        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"GOBACK").click()
