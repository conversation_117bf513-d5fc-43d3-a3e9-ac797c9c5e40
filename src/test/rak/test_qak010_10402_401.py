import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK01010402401(FukushiSiteTestCaseBase):
    """TESTQAK01010402401"""

    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("test_qak010_10402_401.sql", params=atena_list)
        super().setUp() 
    
    def test_case_qak010_10402_401(self):
        """test_case_qak010_10402_401"""

        case_data = self.test_data[self.__class__.__name__]
        shuno_ymd = case_data.get("収納年月日", "")
        huka_ymd = case_data.get("賦課年度", "")
        soto_ymd = case_data.get("相当年度", "")
        tutisho_bango = case_data.get("通知書番号", "")
        choshu = case_data.get("徴収区分", "")
        kibetsu = case_data.get("期別", "")
        hoken = case_data.get("保険料", "")
        entai = case_data.get("延滞金", "")
        tokusoku = case_data.get("督促手数料", "")
        nounyu = case_data.get("納付区分", "")
        ryoushu_ymd = case_data.get("領収日", "")
        number = case_data.get("ナンバリング", "")
        # ログイン
        self.do_login()

        # メインメニュー画面 「入金消込処理」ボタン押下
        self.click_button_by_label("入金消込処理")
        # サブメニュー画面 「消込エラー修正」ボタン押下
        self.click_button_by_label("消込エラー修正")
        self.screen_shot("10402-401-05")

        # エラー納付書更新一覧「1」Noボタン押下
        self.click_button_by_label("1") 
        self.screen_shot("10402-401-07")

        # 納付書入力画面 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 納付書入力画面 収納年月日
        # 納付書入力画面 賦課年度
        # 納付書入力画面 相当年度
        # 納付書入力画面 通知書番号
        # 納付書入力画面 徴収区分
        # 納付書入力画面 期別
        # 納付書入力画面 保険料
        # 納付書入力画面 延滞金
        # 納付書入力画面 督促手数料
        # 納付書入力画面 納付区分
        # 納付書入力画面 領収日
        # 納付書入力画面 ナンバリング
        self.form_input_by_id(idstr="TxtShunoYMD", value=shuno_ymd)
        
        # 画面の賦課年度
        span_element = self.find_element(By.XPATH, '//span[contains(., "賦課年度")]')
        # 賦課年度 所在ラベルの父級<th>ラベル
        parent_th = span_element.find_element(By.XPATH, "./ancestor::th[1]")
        # 上記<th>ラベル右側の<td>ラベル
        cmdHuka_td_text = parent_th.find_element(By.XPATH, 'following-sibling::td[1]')

        # 画面の相当年度
        span_element = self.find_element(By.XPATH, '//span[contains(., "相当年度")]')
        # 相当年度 所在ラベルの父級<th>ラベル
        parent_th = span_element.find_element(By.XPATH, "./ancestor::th[1]")
        # 上記<th>ラベル右側の<td>ラベル
        cmdSoto_td_text = parent_th.find_element(By.XPATH, 'following-sibling::td[1]')

        # パラメータ設定の賦課年度と相当年度　変換後の西暦
        paramCmdHukaYear = japanese_to_western(huka_ymd)
        paramCmdSotoYear = japanese_to_western(soto_ymd) 

        # 画面の賦課年度と相当年度　変換後の西暦
        cmdHukaYearText = japanese_to_western(cmdHuka_td_text.text)
        cmdSotoYearText = japanese_to_western(cmdSoto_td_text.text) 
        
        # クリック数
        cmdHukaClickCount =  paramCmdHukaYear - cmdHukaYearText
        cmdSotoClickCount =  paramCmdSotoYear - cmdSotoYearText
        
        if(cmdHukaClickCount < 0):
            for _ in range(abs(cmdHukaClickCount)):
            # 納付書入力画面 「賦課年度の＜」ボタン押下
                self.click_by_id("CmdHukaMae")
                time.sleep(1)
        
        if(cmdHukaClickCount > 0):
            for _ in range(cmdHukaClickCount):
            # 納付書入力画面 「賦課年度の＞」ボタン押下
                self.click_by_id("CmdHukaTsugi")
                time.sleep(1)

        if(cmdSotoClickCount < 0):
            for _ in range(abs(cmdSotoClickCount)):
                # 納付書入力画面 「相当年度の＜」ボタン押下
                self.click_by_id("CmdSotoMae")
                time.sleep(1)
        
        if(cmdSotoClickCount > 0):
            for _ in range(cmdSotoClickCount):
                # 納付書入力画面 「相当年度の＞」ボタン押下
                self.click_by_id("CmdSotoTsugi")
                time.sleep(1)
        self.form_input_by_id(idstr="TxtTutishoBango", value=tutisho_bango)
        self.form_input_by_id(idstr="ChoshuCmb", text=choshu)
        self.form_input_by_id(idstr="TxtKibetsu", value=kibetsu)
        self.form_input_by_id(idstr="TxtHoken", value=hoken)
        self.form_input_by_id(idstr="TxtEntai", value=entai)
        self.form_input_by_id(idstr="TxtTokusoku", value=tokusoku)
        self.form_input_by_id(idstr="NounyuCmb", text=nounyu)
        self.form_input_by_id(idstr="TxtRyoushuYMD", value=ryoushu_ymd)
        self.form_input_by_id(idstr="TxtNumber", value=number)

        # 納付書入力画面 「収納賦課チェック」ボタン押下
        self.click_button_by_label("収納賦課チェック")
        self.screen_shot("10402-401-10")

        # 納付書入力画面 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        # 納付書入力画面 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("10402-401-12")

        # 納付書入力画面 「戻る」ボタン押下
        self.return_click()
        # エラー納付書更新一覧画面「戻る」ボタン押下
        self.return_click()
        # サブメニュー画面 「戻る」ボタン押下
        self.return_click()
        
# 西暦年変換
def japanese_to_western(year_era):
    # 年号漢字
    temp_year_era = year_era[0:2]
    # 年号数字
    temp_year = year_era[2:4]
    eras = {
        '明治': 1868,
        '大正': 1912,
        '昭和': 1926,
        '平成': 1989,
        '令和': 2019
    }

    # 02 03 ... 状況
    if(temp_year[0:1] == 0):
        temp_year = temp_year[1:2]

    return int(temp_year) + eras.get(temp_year_era, 0)
 