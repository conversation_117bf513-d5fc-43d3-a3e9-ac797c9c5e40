import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK010102011402(FukushiSiteTestCaseBase):
    """TESTQAK010102011402"""

    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("test_qak010_10201_1402.sql", params=atena_list)
        super().setUp()

    def test_case_qak010_10201_1402(self):
        """test_case_qak010_10201_1402"""
        driver = None
        case_data = self.test_data[self.__class__.__name__]
        atenaCD = case_data.get("宛名番号", "")

        # ログイン
        self.do_login()

        # 後期高齢者医療　資格
        # →「資格管理」ボタン押下
        self.click_button_by_label("資格管理")

        # 「宛名番号」テキストボックス入力
        self.form_input_by_id(idstr="AtenaCD", value=atenaCD)

        # 「検索」ボタン押下
        self.click_button_by_label("検索")
        self.screen_shot("10201-1402-08")

        # 「戻る」ボタン押下
        self.return_click()

        # 「戻る」ボタン押下
        self.return_click()