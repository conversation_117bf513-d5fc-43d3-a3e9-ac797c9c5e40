import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAK024001(FukushiSiteTestCaseBase):
    """TESTRAK024001"""
    
    def test_case_rak024_001(self):
        """test_case_rak024_001"""

        driver = None
        test_data = self.common_test_data
        self.do_login()
        
        self.click_button_by_label("収納情報管理")  
        self.save_screenshot_migrate(driver, "RAK024-001-02", True)
        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("rak024_atena_code"))
        self.find_element(By.ID,"Kensa<PERSON>").click()
        self.save_screenshot_migrate(driver, "RAK024-001-05", True)

        self.find_element(By.ID,"Sel21").click()
        self.save_screenshot_migrate(driver, "RAK024-001-07", True)

        self.click_button_by_label("広域滞納者情報更新")  
        self.save_screenshot_migrate(driver, "RAK024-001-09", True)

        self.find_element(By.ID,"ChkUpdateFlag1").click()
        self.find_element(By.ID,"TextTokusokuHakkouYMD1").click()
        self.find_element(By.ID,"TextTokusokuHakkouYMD1").send_keys("")
        self.find_element(By.ID,"TextTokusokuHakkouYMD1").send_keys(u"令和03年09月09日")
        
        self.find_element(By.ID,"span_CmdUpdate").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK024-001-12", True)
        
        self.find_element(By.ID,"span_CmdShokiHyoji").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK024-001-15", True)

        
        self.find_element(By.ID,"GOBACK").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.find_element(By.ID,"GOBACK").click()
    
        