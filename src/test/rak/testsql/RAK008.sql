DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

update WR$$JICHITAI_CODE$$QA..QAK特徴対象者管理 set 削除フラグ = 0 where 業務コード = 'QAK010' AND 適用年度 = '2024'  and データ作成担当者 = 'TK'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAK特徴対象者管理	WHERE 業務コード = 'QAK010' AND 被保険者番号 = '00100024'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAK被保険者資格内容	WHERE 業務コード = 'QAK010' AND 宛名コード = '$$RAK008_ATENACODE001$$'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAK被保険者資格内容	WHERE 業務コード = 'QAK010' AND 宛名コード = '$$RAK008_ATENACODE002$$'

INSERT INTO WR$$JICHITAI_CODE$$QA..QAK特徴対象者管理
           ([自治体コード]
           ,[業務コード]
           ,[福祉事務所コード]
           ,[被保険者番号]
           ,[被保険者番号枝番]
           ,[適用年度]
           ,[捕捉年月]
           ,[適用開始年月]
           ,[適用終了年月]
           ,[特徴義務者コード]
           ,[基礎年金番号]
           ,[年金コード]
           ,[年金データ上カナ氏名]
           ,[年金データ上漢字氏名]
           ,[年金データ上カナ住所]
           ,[年金データ上漢字住所]
           ,[年金データ上生年月日]
           ,[年金データ上性別]
           ,[年金データ上郵便番号]
           ,[特徴義務者区分]
           ,[特徴中止事由]
           ,[特徴対象者判定区分]
           ,[エラーコード]
           ,[介護金額1]
           ,[後期高齢金額1]
           ,[年金額]
           ,[介護被保険者番号]
           ,[個人区分コード]
           ,[個人番号]
           ,[介護住所地特例]
           ,[介護捕捉年月日]
           ,[介護待機フラグ]
           ,[データ作成区分]
           ,[取込年月日]
           ,[取込時刻]
           ,[削除フラグ]
           ,[データ作成担当者]
           ,[データ更新担当者]
           ,[データ作成日時]
           ,[データ更新日時]
           ,[データ更新プログラム])
     VALUES
           ('$$JICHITAI_CODE$$','QAK010','00000','00100024',1,'2021','200710','200804','999999','999','0000100024','0120','ｺｳｷ ﾁﾁ30024','後期　父３００２４','ｱｲﾈｽｹﾝｱｲﾈｽｸﾃｽﾄﾁｮｳﾁｮｳ1','アイネス県アイネス区テスト町丁１','19650101','1','1234567','0','00','0','00',4200,4200,12600
           ,'0000100024','1','0000100024','0','00000000','0','0','20210907','000000','0','9501','9501','2008-04-08 00:00:00.000','2008-04-08 00:00:00.000','QAKF212 ')


INSERT INTO WR$$JICHITAI_CODE$$QA..QAK被保険者資格内容
           ([業務コード]
           ,[履歴番号]
           ,[履歴分類]
           ,[自治体コード]
           ,[福祉事務所コード]
           ,[宛名コード]
           ,[個人区分コード]
           ,[被保険者番号]
           ,[被保険者資格取得事由コード]
           ,[被保険者資格取得年月日]
           ,[被保険者資格喪失事由コード]
           ,[被保険者資格喪失年月日]
           ,[保険者番号適用開始年月日]
           ,[保険者番号適用終了年月日]
           ,[氏名カナ]
           ,[生年月日]
           ,[性別コード]
           ,[現都道府県名]
           ,[現市区町村名]
           ,[現住所]
           ,[作成年月日]
           ,[作成時刻]
           ,[取込年月日]
           ,[取込時刻]
           ,[削除フラグ]
           ,[データ作成担当者]
           ,[データ更新担当者]
           ,[データ作成日時]
           ,[データ更新日時]
           ,[データ更新プログラム])
     VALUES
		   ('QAK010',100024,'0','$$JICHITAI_CODE$$','00000','$$RAK008_ATENACODE001$$','1','00100024','001','20080401','000','99999999','20080401','99999999','コウキ チチ30024','19650101','1','アイネス県','アイネス区','テスト町丁１','20210907','000000','20210907','000000','0',9501,9501,'2021-09-08 10:47:38.587','2021-09-08 10:47:38.587','QAKB1003')
		   ,('QAK010',100025,'0','$$JICHITAI_CODE$$','00000','$$RAK008_ATENACODE002$$','1','00100025','001','20080401','000','99999999','20080401','99999999','コウキ チチ30024','19650101','1','アイネス県','アイネス区','テスト町丁１','20210907','000000','20210907','000000','0',9501,9501,'2021-09-08 10:47:38.587','2021-09-08 10:47:38.587','QAKB1003')


IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END