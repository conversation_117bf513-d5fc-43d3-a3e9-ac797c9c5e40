DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

DELETE FROM WR$$JICHITAI_CODE$$QA..QAK入院時支援金申請情報	WHERE 宛名コード = '$$RAKF491_ATENACODE$$' AND 業務コード = 'QAK010' 

INSERT INTO WR$$JICHITAI_CODE$$QA..QAK入院時支援金申請情報
            ( [業務コード]
            , [連番]
            , [自治体コード]
            , [福祉事務所コード]
            , [宛名コード]
            , [被保険者番号]
            , [年度]
            , [申請年月日]
            , [支援金]
            , [決定年月日]
            , [代理人氏名]
            , [代理人住所]
            , [代理人電話番号]
            , [医療機関名称]
            , [医療機関所在地]
            , [入院開始年月日]
            , [入院終了年月日]
            , [支払区分]
            , [金融機関コード]
            , [金融機関名]
            , [金融機関名カナ]
            , [支店コード]
            , [支店名]
            , [支店名カナ]
            , [口座種別]
            , [口座番号]
            , [口座名義人漢字氏名]
            , [口座名義人カナ氏名]
            , [支払処理日]
            , [振込年月日]
            , [削除フラグ]
            , [データ作成担当者]
            , [データ更新担当者]
            , [データ作成日時]
            , [データ更新日時]
            , [データ更新プログラム]
            ) 
      VALUES ( 'QAK010', 15, '$$JICHITAI_CODE$$', '00000', '$$RAKF491_ATENACODE$$', '55007930', '2019', '20190501', 20000, '20190501', N'ふくし　たろう', N'じゅうしょてすと', '', N'あああああ', N'いいいいい', '20190501', '20190501', '2', '0000', N'', '', '000', N'', '', '0', '0000000', N'', '', '20190501', '20190501', '0', '9501', '9501', convert(DateTime, '2018-12-14 19:29:30.093', 21), convert(DateTime, '2024-02-22 17:50:24.170', 21), 'RAKF491 ')



IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END