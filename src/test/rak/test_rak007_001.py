import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAK007001(FukushiSiteTestCaseBase):
    """TESTRAK007001"""
    
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("RAK007.sql", params=atena_list)
        super().setUp()
    
    def test_case_rak007_001(self):
        """test_case_rak007_001"""
        driver = None
        test_data = self.common_test_data
        self.do_login()
        self.click_button_by_label("賦課管理")
        self.save_screenshot_migrate(driver, "RAK007-001-02", True)
        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("rak007_atena_code"))
        self.find_element(By.ID,"Kensaku").click()
        self.save_screenshot_migrate(driver, "RAK007-001-05", True)
        self.click_button_by_label("期別更正")
        self.find_element(By.ID,"span_CmdShusei").click()
        self.find_element(By.ID,"TxtHenkogoNengaku").click()
        self.find_element(By.ID,"TxtHenkogoNengaku").send_keys("10000")
        self.find_element(By.ID,"TxtTokuchoKibetsu").click()
        self.find_element(By.ID,"TxtTokuchoKibetsu").send_keys("6")
        self.find_element(By.ID,"TxtFuchoKibetsu").click()
        self.find_element(By.ID,"TxtFuchoKibetsu").send_keys("12")
        self.find_element(By.ID,"TxtKanendoGaitoTsuki").click()
        self.find_element(By.ID,"TxtKanendoGaitoTsuki").send_keys("3")
        
        self.find_element(By.ID,"span_CmdKiwariKeisan").click()
        self.assertEqual(u"期割を再計算します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK007-001-13", True)
        self.find_element(By.ID,"span_CmdKibetsuKousei").click()
        self.assertEqual(u"期別更正を行います。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK007-001-16", True)
        self.find_element(By.ID,"TxtFuchoKingaku_01").click()
        self.find_element(By.ID,"TxtFuchoKingaku_01").send_keys("")
        self.find_element(By.ID,"TxtFuchoKingaku_01").send_keys("3000")
        self.find_element(By.ID,"TxtFuchoKingaku_02").click()
        self.find_element(By.ID,"TxtFuchoKingaku_02").send_keys("")
        self.find_element(By.ID,"TxtFuchoKingaku_02").send_keys("2000")
        self.find_element(By.ID,"span_CmdSaikeisan").click()
        self.save_screenshot_migrate(driver, "RAK007-001-20", True)
        
        self.find_element(By.ID,"span_CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK007-001-21", True)
        self.find_element(By.ID,"span_CmdKyoseiShusei").click()
        self.save_screenshot_migrate(driver, "RAK007-001-24", True)
        self.find_element(By.ID,"TxtFuchoKingaku_01").click()
        self.find_element(By.ID,"TxtFuchoKingaku_01").send_keys("")
        self.find_element(By.ID,"TxtFuchoKingaku_01").send_keys("2500")
        self.find_element(By.ID,"TxtFuchoKingaku_02").click()
        self.find_element(By.ID,"TxtFuchoKingaku_02").send_keys("")
        self.find_element(By.ID,"TxtFuchoKingaku_02").send_keys("2500")
        self.find_element(By.ID,"span_CmdSaikeisan").click()
        self.save_screenshot_migrate(driver, "RAK007-001-26", True)
        self.find_element(By.ID,"CmdTsuika").click()
        self.save_screenshot_migrate(driver, "RAK007-001-27", True)
        self.find_element(By.ID,"TxtKanendoKingaku_1").click()
        self.find_element(By.ID,"TxtKanendoKingaku_1").send_keys("")
        self.find_element(By.ID,"TxtKanendoKingaku_1").send_keys("0")
        self.find_element(By.ID,"span_CmdSaikeisan").click()
        self.save_screenshot_migrate(driver, "RAK007-001-29", True)

        self.find_element(By.ID,"span_CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK007-001-30", True)
        
        self.find_element(By.ID,"span_CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK007-001-33", True)

        self.find_element(By.ID,"span_CmdShoki").click()
        self.find_element(By.ID,"btnCommon0").click()
        self.save_screenshot_migrate(driver, "RAK007-002-02", True)
        self.find_element(By.ID,"span_CmdBackTekiyo").click()
        self.save_screenshot_migrate(driver, "RAK007-002-03", True)
        self.find_element(By.ID,"span_CmdNextTekiyo").click()
        self.save_screenshot_migrate(driver, "RAK007-002-04", True)
        self.find_element(By.ID,"CmdNextHosoku").click()
        self.save_screenshot_migrate(driver, "RAK007-002-05", True)
        self.find_element(By.XPATH,"//button[@id='CmdBackHosoku']/span").click()
        self.save_screenshot_migrate(driver, "RAK007-002-06", True)        
        self.find_element(By.ID,"CmdShusei").click()
        self.save_screenshot_migrate(driver, "RAK007-002-07", True)
        self.find_element(By.ID,"TxtEndDate").click()
        self.find_element(By.ID,"TxtEndDate").send_keys("令和10年02月")
        self.find_element(By.ID,"TokuchoChushiJiyuCmb").send_keys("転出")

        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.assertEqual(u"口座振替のために普徴へ切替登録がされていませんが、よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK007-002-10", True)
        self.find_element(By.ID,"CmdShusei").click()
        self.save_screenshot_migrate(driver, "RAK007-002-15", True)
        self.find_element(By.ID,"span_CmdCancel").click()
        self.save_screenshot_migrate(driver, "RAK007-002-16", True)

        self.find_element(By.ID,"span_CmdFuchoKirikae").click()
        self.save_screenshot_migrate(driver, "RAK007-003-02", True)
        self.find_element(By.ID,"span_CmdTsuika").click()
        self.save_screenshot_migrate(driver, "RAK007-003-03", True)
        self.find_element(By.ID,"TxtKirikaeKaishiYM").click()
        self.find_element(By.ID,"TxtKirikaeKaishiYM").send_keys("")
        self.find_element(By.ID,"TxtKirikaeKaishiYM").send_keys(u"令和15年01月")
        self.find_element(By.ID,"KirikaeKaishiJiyuCmb").send_keys("本人申請")  
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK007-003-06", True)
        self.find_element(By.ID,"span_CmdShusei").click()
        self.save_screenshot_migrate(driver, "RAK007-003-09", True)
        self.find_element(By.ID,"TxtKirikaeKaishiYM").click()
        self.find_element(By.ID,"TxtKirikaeKaishiYM").send_keys("")
        self.find_element(By.ID,"TxtKirikaeKaishiYM").send_keys(u"令和14年12月")
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK007-003-11", True)
        self.find_element(By.ID,"span_CmdShoki").click()
        
        self.find_element(By.ID,"CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK007-003-15", True)
        self.find_element(By.ID,"span_CmdTokuchoShokai").click()
        self.save_screenshot_migrate(driver, "RAK007-003-19", True)
        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"btnCommon1").click()
        self.save_screenshot_migrate(driver, "RAK007-004-04", True)

        self.find_element(By.ID,"PageCmb").send_keys("02") 
        self.find_element(By.ID,"Idobtn").click()
        self.save_screenshot_migrate(driver, "RAK007-004-06", True)
        self.find_element(By.ID,"Mae").click()
        self.save_screenshot_migrate(driver, "RAK007-004-07", True)
        self.find_element(By.ID,"Tsugi").click()
        self.save_screenshot_migrate(driver, "RAK007-004-08", True)

        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"GOBACK").click()
