import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK01010405401(FukushiSiteTestCaseBase):
    """TESTQAK01010405401"""

    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("test_qak010_10405_401.sql", params=atena_list)
        super().setUp() 
    
    def test_case_qak010_10405_401(self):
        """test_case_qak010_10405_401"""

        case_data = self.test_data[self.__class__.__name__]
        rdomishori = case_data.get("未処理", "")
        rdokanpu = case_data.get("還付お知らせ", "")
        rdosyorizumi = case_data.get("処理済", "")
        rdohoryu = case_data.get("特徴保留分", "")
        rdotokutyou = case_data.get("特徴還付分", "")
        rdojiko = case_data.get("時効分", "")
        rdoall = case_data.get("全て", "")
        kouzajouhou_u_KinyuKikanCode = case_data.get("金融機関コード", "")
        kouzajouhou_u_ShitenCode = case_data.get("支店コード", "")
        kouzajouhou_u_KouzaBango = case_data.get("口座番号", "")
        kouzajouhou_u_KouzaKanriKbn = case_data.get("口座管理区分", "")
        # ログイン
        self.do_login()

        # メインメニュー画面 「過誤納整理」ボタン押下
        self.click_button_by_label("過誤納整理")
        # サブメニュー画面 「還付充当入力」ボタン押下
        self.click_button_by_label("還付充当入力")
        # 過誤納付書検索画面 検索条件１チェック
        self.form_input_by_id(idstr="RdoMishori", value=rdomishori)
        self.form_input_by_id(idstr="RdoKanpu", value=rdokanpu)
        self.form_input_by_id(idstr="RdoSyorizumi", value=rdosyorizumi)
        self.form_input_by_id(idstr="RdoHoryu", value=rdohoryu)
        self.form_input_by_id(idstr="RdoTokutyou", value=rdotokutyou)
        self.form_input_by_id(idstr="RdoJiko", value=rdojiko)
        self.form_input_by_id(idstr="RdoAll", value=rdoall)

        # 過誤納付書検索画面 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 過誤納金一覧画面 入力予定者の「No.」ボタン押下
        self.click_button_by_label("12") 
        self.screen_shot("10405-401-10")

        # 還付充当入力画面 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 還付充当入力画面 
        self.form_input_by_id(idstr="ShiharaiHouhouCmb", text="口座振込払い")
        # 還付充当入力画面 「口座要件」ボタン押下
        self.click_button_by_label("口座要件")
        self.screen_shot("10405-401-14")

        disabled = self.find_element_by_id("口座情報_u_Shusei").get_attribute("disabled")
        if disabled != 'true':
            # 口座情報画面 「修正」ボタン押下
            self.click_button_by_label("修正")
            self.form_input_by_id(idstr="口座情報_u_KinyuKikanCode", value=kouzajouhou_u_KinyuKikanCode)
            self.form_input_by_id(idstr="口座情報_u_ShitenCode", value=kouzajouhou_u_ShitenCode)
            self.form_input_by_id(idstr="口座情報_u_KouzaBango", value=kouzajouhou_u_KouzaBango)
        else:
            # 口座情報画面 「追加」ボタン押下
            self.click_button_by_label("追加")
            self.form_input_by_id(idstr="口座情報_u_KinyuKikanCode", value=kouzajouhou_u_KinyuKikanCode)
            self.form_input_by_id(idstr="口座情報_u_ShitenCode", value=kouzajouhou_u_ShitenCode)
            self.form_input_by_id(idstr="口座情報_u_KouzaBango", value=kouzajouhou_u_KouzaBango)
            self.form_input_by_id(idstr="口座情報_u_KouzaKanriKbn", text=kouzajouhou_u_KouzaKanriKbn)
            
        # 口座情報画面 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(5)
        self.alert_ok()
        # 口座情報画面 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("10405-401-18")

        # 口座情報画面 「戻る」ボタン押下
        self.return_click()
        self.screen_shot("10405-401-20")

        # 還付充当入力画面 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        # 還付充当入力画面 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("10405-401-22")

        # 還付充当入力画面 「戻る」ボタン押下
        self.return_click()
        # 過誤納金一覧画面 「戻る」ボタン押下
        self.return_click()
        # 過誤納付書検索画面 「戻る」ボタン押下
        self.return_click()
        # サブメニュー画面 「戻る」ボタン押下
        self.return_click()