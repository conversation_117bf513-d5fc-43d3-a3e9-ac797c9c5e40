import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAK011001(FukushiSiteTestCaseBase):
    """TESTRAK011001"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("RAK011_実行前.sql", params=atena_list)
        super().setUp()

    def tearDown(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("RAK011_実行後.sql", params=atena_list)
        super().tearDown()

    def test_case_qak011_001(self):
        """test_case_qak011_001"""
        driver = None
        test_data = self.common_test_data
        self.do_login()
        self.click_button_by_label("入金消込処理")
        self.save_screenshot_migrate(driver, "RAK011-001-02", True)
        self.click_button_by_label("入金データ入力")
        self.save_screenshot_migrate(driver, "RAK011-001-04", True)

        self.find_element(By.ID,"PageCmb").send_keys("02")
        self.find_element(By.ID,"CmdIdobtn").click()
        self.save_screenshot_migrate(driver, "RAK011-001-06", True)
        self.find_element(By.ID,"CmdMae").click()
        self.save_screenshot_migrate(driver, "RAK011-001-07", True)
        self.find_element(By.ID,"CmdTsugi").click()
        self.save_screenshot_migrate(driver, "RAK011-001-08", True)
        self.find_element(By.ID,"CmdTsuika").click()
        self.save_screenshot_migrate(driver, "RAK011-001-09", True)
        self.find_element(By.ID,"GOBACK").click()

        self.find_element(By.ID,"Sel7").click()
        self.save_screenshot_migrate(driver, "RAK011-002-02", True)

        self.find_element(By.ID,"span_CmdCheck").click()
        self.save_screenshot_migrate(driver, "RAK011-002-03", True)
        
        self.find_element(By.ID,"span_CmdSakujo").click()
        self.assertEqual(u"送付票に対応するすべての納付書情報も削除します.。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK011-002-04", True)
        self.find_element(By.ID,"Sel4").click()
        self.find_element(By.ID,"span_CmdSort").click()
        self.save_screenshot_migrate(driver, "RAK011-002-08", True)

        self.find_element(By.ID,"PageCmb").send_keys("02")
        self.find_element(By.ID,"CmdJumpPage").click()
        self.save_screenshot_migrate(driver, "RAK011-002-10", True)
        self.find_element(By.ID,"CmdBackPage").click()
        self.save_screenshot_migrate(driver, "RAK011-002-11", True)

        self.find_element(By.ID,"CmdNextPage").click()
        self.save_screenshot_migrate(driver, "RAK011-002-12", True)
        self.find_element(By.ID,"span_CmdSyusei").click()
        self.save_screenshot_migrate(driver, "RAK011-003-02", True)
        self.find_element(By.ID,"CmdShusei").click()
        self.find_element(By.ID,"TxtShunouYMD").click()
        self.find_element(By.ID,"TxtShunouYMD").send_keys("")
        self.find_element(By.ID,"TxtShunouYMD").send_keys(u"令和03年09月13日")
        self.find_element(By.ID,"TxtNouhugakuKei").click()
        self.find_element(By.ID,"TxtNouhugakuKei").send_keys("")
        self.find_element(By.ID,"TxtNouhugakuKei").send_keys("12345")
        self.find_element(By.ID,"TxtMaisuKei").click()
        self.find_element(By.ID,"TxtMaisuKei").send_keys("")
        self.find_element(By.ID,"TxtMaisuKei").send_keys("10")
        
        self.find_element(By.ID,"span_CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK011-003-07", True)
        self.find_element(By.ID,"span_CmdSyusei").click()
        self.save_screenshot_migrate(driver, "RAK011-003-11", True)
        self.find_element(By.ID,"CmdShusei").click()
        self.save_screenshot_migrate(driver, "RAK011-003-12", True)
        self.find_element(By.ID,"TxtShunouYMD").click()
        self.find_element(By.ID,"TxtShunouYMD").send_keys("")
        self.find_element(By.ID,"TxtShunouYMD").send_keys(u"令和03年09月10日")
        
        self.save_screenshot_migrate(driver, "RAK011-003-14", True)
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK011-003-17", True)
        self.find_element(By.ID,"span_CmdSyusei").click()
        self.save_screenshot_migrate(driver, "RAK011-003-19", True)
        self.find_element(By.ID,"CmdShusei").click()
        self.save_screenshot_migrate(driver, "RAK011-003-20", True)
        self.find_element(By.ID,"span_CmdShoki").click()
        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"span_CmdTsuika").click()
        self.save_screenshot_migrate(driver, "RAK011-004-02", True)
        self.find_element(By.ID,"TxtTutishoBango").click()
        self.find_element(By.ID,"TxtTutishoBango").send_keys("")
        self.find_element(By.ID,"TxtTutishoBango").send_keys("0000100031")
        

        self.find_element(By.ID,"ChoshuCmb").send_keys("普通徴収")
        self.find_element(By.ID,"TxtKibetsu").click()
        self.find_element(By.ID,"TxtKibetsu").send_keys("")
        self.find_element(By.ID,"TxtKibetsu").send_keys("01")
        self.find_element(By.ID,"TxtHoken").click()
        self.find_element(By.ID,"TxtHoken").send_keys("")
        self.find_element(By.ID,"TxtHoken").send_keys("10000")
        self.find_element(By.ID,"TxtEntai").click()
        self.find_element(By.ID,"TxtEntai").send_keys("")
        self.find_element(By.ID,"TxtEntai").send_keys("100")
        self.find_element(By.ID,"TxtTokusoku").click()
        self.find_element(By.ID,"TxtTokusoku").send_keys("")
        self.find_element(By.ID,"TxtTokusoku").send_keys("10")
        

        self.find_element(By.ID,"NounyuCmb").send_keys("口座振替")
        self.find_element(By.ID,"TxtRyoushuYMD").click()
        self.find_element(By.ID,"TxtRyoushuYMD").send_keys("")
        self.find_element(By.ID,"TxtRyoushuYMD").send_keys(u"令和03年09月09日")
        self.find_element(By.ID,"TxtNumber").click()
        self.find_element(By.ID,"TxtNumber").send_keys("")
        self.find_element(By.ID,"TxtNumber").send_keys("1")
        self.find_element(By.ID,"span_CmdShunoFuka").click()
        self.save_screenshot_migrate(driver, "RAK011-004-12", True)
        
        self.find_element(By.ID,"span_CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK011-004-13", True)
        self.find_element(By.ID,"GOBACK").click()

        self.find_element(By.ID,"span_NoBtn2").click()
        self.find_element(By.ID,"span_CmdShusei").click()
        self.save_screenshot_migrate(driver, "RAK011-004-18", True)
        self.find_element(By.ID,"TxtHoken").click()
        self.find_element(By.ID,"TxtHoken").send_keys("")
        self.find_element(By.ID,"TxtHoken").send_keys("1000")
        self.find_element(By.ID,"span_CmdShunoFuka").click()
        self.save_screenshot_migrate(driver, "RAK011-004-20", True)
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK011-004-21", True)
        
        
        self.find_element(By.ID,"span_NoBtn2").click()
        self.find_element(By.ID,"span_CmdShusei").click()
        self.save_screenshot_migrate(driver, "RAK011-004-25", True)
        self.find_element(By.ID,"span_CmdShoki").click()
        
        self.find_element(By.ID,"span_CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "RAK011-004-27", True)
        self.find_element(By.ID,"CmdTsuika").click()
        self.save_screenshot_migrate(driver, "RAK011-004-30", True)
        self.find_element(By.ID,"span_CmdHukaMae").click()
        self.save_screenshot_migrate(driver, "RAK011-004-31", True)
        self.find_element(By.ID,"span_CmdHukaTsugi").click()
        self.save_screenshot_migrate(driver, "RAK011-004-32", True)
        self.find_element(By.ID,"span_CmdSotoMae").click()
        self.save_screenshot_migrate(driver, "RAK011-004-33", True)
        self.find_element(By.ID,"span_CmdSotoTsugi").click()
        self.save_screenshot_migrate(driver, "RAK011-004-34", True)
        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.XPATH,"//button[@id='GOBACK']/u").click()
        self.find_element(By.XPATH,"//button[@id='GOBACK']/u").click()
        self.find_element(By.ID,"GOBACK").click()
        self.click_button_by_label("入金消込処理")
        self.save_screenshot_migrate(driver, "RAK011-005-02", True)
        self.click_button_by_label("口振結果入力(紙媒体）")
        self.save_screenshot_migrate(driver, "RAK011-005-04", True)

        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"GOBACK").click()
