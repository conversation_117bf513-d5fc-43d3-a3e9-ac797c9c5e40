import time
import datetime
from datetime import timedelta
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01070503(FukushiSiteTestCaseBase):
    """TestQAJ010_01070503"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01070503"]
        sql_params = {"ATENA_CODE": case_data.get("atena_code", "")}
        super().setUp()
    
        
    def test_QAJ010_01070503(self):
        """RAJJ0025（受診依頼書出力_個別）"""

        date = datetime.date.today()
        thismonth = format(date, '%Y%m')
        today = format(date, '%Y%m%d')
        today_date = int(format(date, '%d'))
        case_data = self.test_data["TestQAJ010_01070503"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")
        
        # 4 個人検索画面: 表示
        self.screen_shot("個人検索画面_4")
        
        # 5 個人検索画面: 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=case_data.get("atena_code",""))
        
        # 6 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
    
        # 7 受給状況画面: 表示
        self.screen_shot("受給状況画面_7")
        
        # 8 受給状況画面: 「障害者総合支援」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAJ010")
        
        # 9 障害福祉サービス受給者台帳画面: 表示
        self.screen_shot("障害福祉サービス受給者台帳画面_9")
        
        # 10 障害福祉サービス受給者台帳画面: 「障害者総合支援申請管理」ボタン押下
        self.click_button_by_label("障害者総合支援申請管理")
        
        # 11 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_11")
        
        # 12 障害福祉サービス申請管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")
        
        # 13 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_13")
        
        # 14 帳票印刷画面: 「医師診断書」行の印刷チェックボックス選択
        # self.form_input_by_id(idstr="insatsuChk_13", value="1")
        exec_params = [
            {"report_name": "医師診断書",}
        ]
        ret = self.print_online_reports(report_param_list=exec_params)

        # 15 帳票印刷画面: 「印刷」ボタン押下
        # self.click_button_by_label("印刷")
        # time.sleep(3)
        # self.assertEqual(u"印刷します。よろしいですか？", self.alert_ok())

        # 16 帳票印刷画面: 表示
        # Assert: メッセージエリアに「プレビューを表示しました」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        self.screen_shot("帳票印刷画面_16")

        # 17 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_17")
        
        # 18 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
        # 19 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        
        # 20 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_20")
