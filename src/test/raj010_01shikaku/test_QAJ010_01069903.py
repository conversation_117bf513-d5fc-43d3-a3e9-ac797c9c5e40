import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01069903(FukushiSiteTestCaseBase):
    """TestQAJ010_01069903"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params = {"TARGET_IRYOKIKAN_CODE": case_data.get("iryokikan_code", ""), "DELETE_KANASHIMEI": case_data.get("kana_shimei", "")}
        self.exec_sqlfile("TestQAJ010_01069903.sql", params=sql_params)
        super().setUp()
    
    # 医師情報が登録できることを確認する。医師情報が検索できることを確認する。医師医療機関情報が参照できることを確認する。
    def test_QAJ010_01069903(self):
        """医師登録"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()

        self.click_button_by_label("総合支援マスタメンテ")

        # 2 サブメニュー画面: 「医師メンテナンス」ボタン押下
        self.click_button_by_label("医師メンテナンス")
        
        # 3 医師検索画面: 表示
        self.screen_shot("医師検索画面_3")
        
        # 4 医師検索画面: 「確定解除」ボタン押下
        self.click_button_by_label("確定解除")
        
        # 5 医師検索画面: 表示
        self.screen_shot("医師検索画面_5")
        
        # 6 医師検索画面: 「確定」ボタン押下
        self.click_button_by_label("確定")
        
        # 7 医師検索画面: 表示
        self.screen_shot("医師検索画面_7")
        
        # 8 医師検索画面: 「医師医療機関登録」ボタン押下
        self.click_button_by_label("医師医療機関登録")
        
        # 9 医師医療機関検索画面: 表示
        self.screen_shot("医師医療機関検索画面_9")
        
        # 10 医師医療機関検索画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click() 
        
        # 11 医師検索画面: 表示
        self.screen_shot("医師検索画面_11")
        
        # 12 医師検索画面: 「都道府県：北海道」選択
        self.form_input_by_id(idstr="CmbTodoufuken", text="北海道")
        
        # 13 医師検索画面: 表示
        self.screen_shot("医師検索画面_13")
        
        # 14 医師検索画面: 「初期表示」ボタン押下
        self.click_button_by_label("初期表示")
        
        # 15 医師検索画面: 表示
        self.screen_shot("医師検索画面_15")
        
        # 16 医師検索画面: 「追加」ボタン押下
        self.click_button_by_label("追加")
        
        # 17 医師入力画面: 表示
        self.screen_shot("医師入力画面_17")
        
        # 18 医師入力画面: 漢字氏名「テスト」入力
        self.form_input_by_id(idstr="TxtKanjiMeisho", value="テスト")
        
        # 19 医師入力画面: 「初期表示」ボタン押下
        self.click_button_by_label("初期表示")
        
        # 20 医師入力画面: 表示
        self.screen_shot("医師入力画面_20")
        
        # 21 医師入力画面: 医師医療機関コード入力
        self.form_input_by_id(idstr="IryoKikanCd", value=case_data.get("iryokikan_code", ""))
        
        # 22 医師入力画面: 「医師医療機関」ボタン押下
        self.click_button_by_label("医師医療機関")
        
        # 23 医師入力画面: 表示
        self.screen_shot("医師入力画面_23")
        
        # 24 医師入力画面: 漢字氏名「医師　氏名」入力カナ氏名「ｲｼ ｼﾒｲ」入力診察科目「内科」選択
        self.form_input_by_id(idstr="TxtKanjiMeisho", value="医師　氏名")
        self.form_input_by_id(idstr="TxtKanaMeisho", value="ｲｼ ｼﾒｲ")
        
        # 25 医師入力画面: 「登録／復活」ボタン押下
        self.click_button_by_label("登録／復活")
        self.alert_ok()
        
        # 26 医師入力画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("医師入力画面_26")
        
        # 27 医師入力画面: 「送付先情報」ボタン押下
        self.return_click()
        self.form_input_by_id(idstr="TxtKanji", value="医師　氏名")
        self.click_button_by_label("検索")
        self.click_button_by_label("1")
        self.click_button_by_label("送付先情報")
        
        # 28 送付先情報画面: 表示
        self.screen_shot("送付先情報画面_28")
        
        # 29 送付先情報画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click() 
        
        # 30 医師入力画面: 表示
        self.screen_shot("医師入力画面_30")
        
        # 31 医師入力画面: 「口座情報」ボタン押下
        self.click_button_by_label("口座情報")
        
        # 32 口座情報画面: 表示
        self.screen_shot("口座情報画面_32")
        
        # 33 口座情報画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click() 
        
        # 34 医師入力画面: 表示
        self.screen_shot("医師入力画面_34")
        
        # 35 医師入力画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click() 
        
        # 36 医師検索画面: 表示
        self.screen_shot("医師検索画面_36")
        
        # 37 医師検索画面: 「カナ名称：ｲｼ」入力
        self.form_input_by_id(idstr="TxtKana", value="ｲｼ")
        
        # 38 医師検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")
        
        # 39 医師検索画面: 表示
        self.screen_shot("医師検索画面_39")
        
        # 40 医師検索画面: 「No」ボタン押下
        self.find_element(By.ID,"Sel1").click()
        
        # 41 医師入力画面: 表示
        self.screen_shot("医師入力画面_41")
        
        # 42 医師入力画面: 「削除」ボタン押下
        self.click_button_by_label("削除")
        self.alert_ok()
        
        # 43 医師入力画面: 表示
        self.screen_shot("医師入力画面_43")
        
        # 44 医師入力画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click() 
        
        # 45 医師検索画面: 表示
        self.screen_shot("医師検索画面_45")
        
        # 46 医師検索画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click() 
        
        # 47 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_47")
        
        # 48 サブメニュー画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click() 
        
        # 49 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_49")
        
