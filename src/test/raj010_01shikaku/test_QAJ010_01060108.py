import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01060108(FukushiSiteTestCaseBase):
    """TestQAJ010_01060108"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01060108"]
        super().setUp()
    
    # 介護給付の支給決定を登録できることを確認する。 受給者証番号が自動附番されることを確認する。障害支援区分を登録できることを確認する。決定サービスを登録できることを確認する。利用者負担額を登録できることを確認する。療養介護情報を登録できることを確認する。
    def test_QAJ010_01060108(self):
        """介護給付支給決定登録"""
        
        case_data = self.test_data["TestQAJ010_01060108"]
        atena_code = case_data.get("atena_code", "")
        hakkoTxt = case_data.get("hakkoTxt", "")
        date = datetime.date.today()
        thisyear = format(date, '%Y')
        thismonth = format(date, '%m')
        next_year = int(thisyear) + 1
        next_ymd = str(next_year) + thismonth + "01"
        today = format(date, '%Y%m%d')

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ010")
        # 2 障害福祉サービス決定管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")
        
        # 3 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_3")
        
        # 4 障害福祉サービス決定管理画面: 決定日「20230601」入力、決定結果「不支給」選択
        self.form_input_by_id(idstr="TxtKetteiYMD", value=today)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="不支給")
        
        # 5 障害福祉サービス決定管理画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        
        # 6 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_6")
        
        # 7 障害福祉サービス申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 8 障害福祉サービス申請管理画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("障害福祉サービス申請管理画面_8")
        
        # 9 障害福祉サービス申請管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")
        
        # 10 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_10")
        
        # 11 帳票印刷画面: 「却下決定通知書（介護給付費等）」行の印刷チェックボックス選択「却下決定通知書（介護給付費等）」行の発行年月日チェックボックス選択、発行年月日「20230601」入力
        exec_params = [
            {"report_name": case_data.get("report_name",""),
             "params":[
                 {"title": "交付日", "value":today},
                {"title": "文書番号", "value":"11111"}
                ]
            }
        ] 
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_11")
        
        # 12 帳票印刷画面: 「印刷」ボタン押下
        # self.click_button_by_label("印刷")
        
        # 13 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        
        # 14 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_14")
        
        # 15 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
        # 16 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        
        # 17 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_17")
        
        # 18 障害福祉サービス申請管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        self.click_by_id("CmdKetteiKirikae")
        
        # 19 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_19")
        
        # 20 障害福祉サービス決定管理画面: 決定日「20230601」入力、決定結果「支給」選択
        self.form_input_by_id(idstr="TxtKetteiYMD", value=today)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="支給")
        
        # 21 障害福祉サービス決定管理画面: 認定証明書　チェック、特記事項「食費等実費負担に係る境界層措置対象者」選択、その他特記事項「その他特記事項入力テスト」入力、受給者証交付年月日「20230601」入力、決定時備考「決定時備考入力テスト」入力
        self.form_input_by_id(idstr="ChkTenshutsu", value="1")
        self.form_input_by_id(idstr="TokkiJikouCmb_1", text="食費等実費負担に係る境界層措置対象者")
        self.form_input_by_id(idstr="TxtTokkijiko", value="その他特記事項入力テスト")
        self.form_input_by_id(idstr="TxtJukyushashoKofuYMD", value=today)
        self.form_input_by_id(idstr="TxtKetteijiBiko", value="決定時備考入力テスト")
        
        # 22 障害福祉サービス決定管理画面: 「決定サービス」ボタン押下
        self.click_button_by_label("決定サービス")
        
        # 23 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_23")
        
        # 24 障害福祉サービス決定管理画面: 体験利用有無　チェック、体験利用期間開始日「20230601」入力、体験利用期間終了日「20230630」入力、サテライト型住居利用有無　チェック、サテライト型住居利用期間開始日「20230601」入力、サテライト型住居利用期間終了日「20230630」入力
        self.form_input_by_id(idstr="TaikenRiyoUmuCheckBox", value="1")
        self.form_input_by_id(idstr="TxtTaikenRiyoKikanKaishiYMD", value=today)
        self.form_input_by_id(idstr="TxtTaikenRiyoKikanShuryoYMD", value=next_ymd)
        self.form_input_by_id(idstr="SateraitoRiyoUmuCheckBox", value="1")
        self.form_input_by_id(idstr="TxtSateraitoRiyoKikanKaishiYMD", value=today)
        self.form_input_by_id(idstr="TxtSateraitoRiyoKikanShuryoYMD", value=next_ymd)
        
        # 25 障害福祉サービス決定管理画面: 開始日「20230601」入力、資格状態「追加」選択、サービス種類「居宅介護」選択、サービス区分　居宅身体介護　行の状態区分「追加」選択、サービス区分　居宅身体介護　以外の行の状態区分「　　」（空白）選択、サービス区分　居宅身体介護　行の決定支給量「60」.「0」時間／月入力、サービス区分　居宅身体介護　行の決定支給量一回「6」.「0」入力、サービス決定備考「サービス決定備考入力テスト」入力
        self.form_input_by_id(idstr="TxtKetteiServiceShikyuKetteiYMD", value=today)
        self.form_input_by_id(idstr="ServiceShuruiJokyoCmb_1", text="追加")
        self.form_input_by_id(idstr="ServiceShuruiCmb_1", text="居宅介護")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_1", text="追加")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_2", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_3", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_4", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_5", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_6", text="")
        self.form_input_by_id(idstr="TxtShikyuryo1_1_1", value="60")
        self.form_input_by_id(idstr="TxtShikyuryo2_1_1", value="0")
        self.form_input_by_id(idstr="TxtShikyuryo3_1_1", value="6")
        self.form_input_by_id(idstr="TxtShikyuryo4_1_1", value="0")
        self.click_button_by_label("無")
        self.form_input_by_id(idstr="ServiceKetteiBiko_1_1", value="サービス決定備考入力テスト")
        
        # 26 障害福祉サービス決定管理画面: 資格状態「追加」選択、サービス種類「療養介護」選択、サービス区分　療養介護基本　行の状態区分「追加」選択
        self.form_input_by_id(idstr="ServiceShuruiJokyoCmb_2", text="追加")
        self.form_input_by_id(idstr="ServiceShuruiCmb_2", text="療養介護")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_2_1", text="追加")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_2_2", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_2_3", text="")
        
        # 27 障害福祉サービス決定管理画面: 「負担額」ボタン押下
        self.click_button_by_label("負担額")
        
        # 28 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_28")
        
        # 29 障害福祉サービス決定管理画面: 「世帯範囲情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("世帯範囲情報")
        
        # 30 世帯範囲情報画面: 表示
        
        # 31 世帯範囲情報画面: 世帯員入日「20230401」入力
        self.form_input_by_id(idstr="TxtSetaiStartYMD1", value="20230401")
        self.screen_shot("世帯範囲情報画面_31")
        
        # 32 世帯範囲情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 33 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_33")
        
        # 34 障害福祉サービス決定管理画面: 「収入・資産入力」ボタン押下
        self.click_button_by_label("収入・資産入力")
        
        # 35 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_35")
        
        # 36 減免申請・収入資産登録画面: 「確定」ボタン押下
        self.click_button_by_label("確定")
        
        # 37 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_37")
        
        # 38 減免申請・収入資産登録画面: 「取込」ボタン押下
        self.click_button_by_label("取込")
        
        # 39 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_39")
        
        # 40 減免申請・収入資産登録画面: 「計算ボタン」ボタン押下
        self.click_button_by_label("計算ボタン")
        
        # 41 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_41")
        
        # 42 減免申請・収入資産登録画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 43 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_43")
        
        # 44 障害福祉サービス決定管理画面: 上限管理対象　チェック、負担額判定時備考「負担額判定時備考入力テスト」入力
        # self.form_input_by_id(idstr="ChkJogenKanriTaisho", value="1")
        self.form_input_by_id(idstr="TxtFutangakuHanteijiBiko", value="負担額判定時備考入力テスト")
        
        # 45 障害福祉サービス決定管理画面: 「療養介護」ボタン押下
        self.click_button_by_label("療養介護")
        
        # 46 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_46")
        
        # 47 障害福祉サービス決定管理画面: 公費受給者番号「9000001」入力、医療部分負担上限月額「100」入力、食事負担限度額「100」入力、医療受給者証交付年月日「20230601」入力
        # self.form_input_by_id(idstr="TxtRyouyouKaigoJukyushaNo", value="9000001")
        self.form_input_by_id(idstr="TxtRyouyouKaigoJogengaku", value="100")
        self.form_input_by_id(idstr="TxtShokujiRyouyouJogengaku", value="100")
        self.form_input_by_id(idstr="TxtIryoJukyushashoKofuYMD", value=today)
        
        # 48 障害福祉サービス決定管理画面: 「医療機関」ボタン押下
        self.click_button_by_label("医療機関")
        
        # 49 医療機関検索画面: 表示
        self.screen_shot("医療機関検索画面_49")
        
        # 50 医療機関検索画面: 医療機関一覧「１」ボタン押下
        self.click_button_by_label("1")
        
        # 51 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_51")
        
        # 52 障害福祉サービス決定管理画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        
        # 53 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_53")
        
        # 54 障害福祉サービス申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 55 障害福祉サービス申請管理画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("障害福祉サービス申請管理画面_55")
        
