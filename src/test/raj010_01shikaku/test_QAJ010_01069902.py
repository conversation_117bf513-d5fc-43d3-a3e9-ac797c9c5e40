import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01069902(FukushiSiteTestCaseBase):
    """TestQAJ010_01069902"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        sql_params = {"TARGET_IIN_KUBUN": '1', "DELETE_KANASHIMEI": case_data.get("kana_shimei", "")}
        self.exec_sqlfile("TestQAJ010_01069902.sql", params=sql_params)
        super().setUp()
    
    # 認定調査員情報が登録できることを確認する。認定調査員情報が検索できることを確認する。指定相談支援事業者情報が参照できることを確認する。
    def test_QAJ010_01069902(self):
        """認定調査員登録"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()

        self.click_button_by_label("総合支援マスタメンテ")

        # 2 サブメニュー画面: 「認定調査員メンテナンス」ボタン押下
        self.click_button_by_label("認定調査員メンテナンス")
        
        # 3 認定調査員検索画面: 表示
        self.screen_shot("認定調査員検索画面_3")
        
        # 4 認定調査員検索画面: 「確定解除」ボタン押下
        self.click_button_by_label("確定解除")
        
        # 5 認定調査員検索画面: 表示
        self.screen_shot("認定調査員検索画面_5")
        
        # 6 認定調査員検索画面: 「確定」ボタン押下
        self.click_button_by_label("確定")
        
        # 7 認定調査員検索画面: 表示
        self.screen_shot("認定調査員検索画面_7")
        
        # 8 認定調査員検索画面: 「指定相談支援事業者等番号」ボタン押下
        self.click_button_by_label("指定相談支援事業者等番号")
        
        # 9 事業所検索画面: 表示
        self.screen_shot("事業所検索画面_9")
        
        # 10 事業所検索画面: 「No」ボタン押下
        self.find_element(By.ID,"Sel1").click()
        
        # 11 認定調査員検索画面: 表示
        self.screen_shot("認定調査員検索画面_11")
        
        # 12 認定調査員検索画面: 「追加」ボタン押下
        self.click_button_by_label("追加")
        
        # 13 認定調査員入力画面: 表示
        self.screen_shot("認定調査員入力画面_13")
        
        # 14 認定調査員入力画面: 認定調査員漢字名_氏「認定」入力認定調査員漢字名_名「調査」入力認定調査員カナ名_氏「ﾆﾝﾃｲ」入力認定調査員カナ名_名「ﾁﾖｳｻ」入力
        self.form_input_by_id(idstr="TxtKanjiNameShi", value="認定")
        self.form_input_by_id(idstr="TxtKanjiNameMei", value="調査")
        self.form_input_by_id(idstr="TxtKanaNameShi", value="ﾆﾝﾃｲ")
        self.form_input_by_id(idstr="TxtKanaNameMei", value="ﾁｮｳｻ")
        
        # 15 認定調査員入力画面: 「初期表示」ボタン押下
        self.click_button_by_label("初期表示")
        self.alert_ok()
        
        # 16 認定調査員入力画面: 表示
        self.screen_shot("認定調査員入力画面_16")
        
        # 17 認定調査員入力画面: 「指定相談支援事業者等番号」ボタン押下
        self.click_button_by_label("指定相談支援事業者等番号")
        
        # 18 サービス事業者検索画面: 表示
        self.screen_shot("サービス事業者検索画面_18")
        
        # 19 サービス事業者検索画面: 「No」ボタン押下
        self.find_element(By.ID,"Sel1").click()
        
        # 20 認定調査員入力画面: 表示
        self.screen_shot("認定調査員入力画面_20")
        
        # 21 認定調査員入力画面: 認定調査員漢字名_氏「認定」入力認定調査員漢字名_名「調査」入力認定調査員カナ名_氏「ﾆﾝﾃｲ」入力認定調査員カナ名_名「ﾁﾖｳｻ」入力認定調査員資格コード「医師」選択
        self.form_input_by_id(idstr="TxtKanjiNameShi", value="認定")
        self.form_input_by_id(idstr="TxtKanjiNameMei", value="調査")
        self.form_input_by_id(idstr="TxtKanaNameShi", value="ﾆﾝﾃｲ")
        self.form_input_by_id(idstr="TxtKanaNameMei", value="ﾁｮｳｻ")
        self.form_input_by_id(idstr="CmbShokushu", text="医師")
        
        # 22 認定調査員入力画面: 「登録／復活」ボタン押下
        self.click_button_by_label("登録／復活")
        self.alert_ok()
        
        # 23 認定調査員入力画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("認定調査員入力画面_23")
        
        # 24 認定調査員入力画面: 「送付先情報」ボタン押下
        self.click_button_by_label("送付先情報")
        
        # 25 送付先情報画面: 表示
        self.screen_shot("送付先情報画面_25")
        
        # 26 送付先情報画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click() 
        
        # 27 認定調査員入力画面: 表示
        self.screen_shot("認定調査員入力画面_27")
        
        # 28 認定調査員入力画面: 「口座情報」ボタン押下
        self.click_button_by_label("口座情報")
        
        # 29 口座情報画面: 表示
        self.screen_shot("口座情報画面_29")
        
        # 30 口座情報画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click() 
        
        # 31 認定調査員入力画面: 表示
        self.screen_shot("認定調査員入力画面_31")
        
        # 32 認定調査員入力画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click() 
        
        # 33 認定調査員検索画面: 表示
        self.screen_shot("認定調査員検索画面_33")
        
        # 34 認定調査員検索画面: カナ氏名「ﾆﾝﾃｲ」入力
        self.form_input_by_id(idstr="TxtkanaName", value="ﾆﾝﾃｲ ﾁｮｳｻ")
        
        # 35 認定調査員検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")
        
        # 36 認定調査員検索画面: 表示
        self.screen_shot("認定調査員検索画面_36")
        
        # 37 認定調査員検索画面: 「No」ボタン押下
        self.find_element(By.ID,"HenshuButton0").click()
        
        # 38 認定調査員入力画面: 表示
        self.screen_shot("認定調査員入力画面_38")
        
        # 39 認定調査員入力画面: 「削除」ボタン押下
        self.click_button_by_label("削除")
        self.alert_ok()
        
        # 40 認定調査員入力画面: 表示
        self.screen_shot("認定調査員入力画面_40")
        
        # 41 認定調査員入力画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click() 
        
        # 42 認定調査員検索画面: 表示
        self.screen_shot("認定調査員検索画面_42")
        
        # 43 認定調査員検索画面: カナ氏名「ﾆﾝﾃｲ」入力
        self.form_input_by_id(idstr="TxtkanaName", value="ﾆﾝﾃｲ")
        
        # 44 認定調査員検索画面: 「初期表示」ボタン押下
        self.click_button_by_label("初期表示")
        
        # 45 認定調査員検索画面: 表示
        self.screen_shot("認定調査員検索画面_45")
        
        # 46 認定調査員検索画面: 「指定相談支援事業者等番号」ボタン押下
        self.click_button_by_label("指定相談支援事業者等番号")
        
        # 47 サービス事業者検索画面: 表示
        self.screen_shot("サービス事業者検索画面_47")
        
        # 48 サービス事業者検索画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click() 
        
        # 49 認定調査員検索画面: 表示
        self.screen_shot("認定調査員検索画面_49")
        
        # 50 認定調査員検索画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click() 
        
        # 51 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_51")
        
