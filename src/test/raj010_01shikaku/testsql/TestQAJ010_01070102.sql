DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

DELETE WR$$JICHITAI_CODE$$QA..QAJ請求基本 WHERE 業務コード = 'QAJ030' AND 受付年月 = '$$SEIKYU_YEAR_MONTH$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ請求明細 WHERE 業務コード = 'QAJ030' AND 受付年月 = '$$SEIKYU_YEAR_MONTH$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ支給実績基本 WHERE 業務コード = 'QAJ030' AND 受付年月 = '$$SEIKYU_YEAR_MONTH$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ支給実績集計 WHERE 業務コード = 'QAJ030' AND 受付年月 = '$$SEIKYU_YEAR_MONTH$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ支給実績明細 WHERE 業務コード = 'QAJ030' AND 受付年月 = '$$SEIKYU_YEAR_MONTH$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ支給実績日数 WHERE 業務コード = 'QAJ030' AND 受付年月 = '$$SEIKYU_YEAR_MONTH$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ国保連支給実績基本_送付履歴 WHERE 業務コード = 'QAJ030' AND 受付年月 = '$$SEIKYU_YEAR_MONTH$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ国保連支給実績集計_送付履歴 WHERE 業務コード = 'QAJ030' AND 受付年月 = '$$SEIKYU_YEAR_MONTH$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ国保連支給実績明細_送付履歴 WHERE 業務コード = 'QAJ030' AND 受付年月 = '$$SEIKYU_YEAR_MONTH$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ国保連支給実績日数_送付履歴 WHERE 業務コード = 'QAJ030' AND 受付年月 = '$$SEIKYU_YEAR_MONTH$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ国保連契約情報_送付履歴 WHERE 業務コード = 'QAJ030' AND 受付年月 = '$$SEIKYU_YEAR_MONTH$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ国保連契約情報 WHERE 業務コード = 'QAJ030' AND 受付年月 = '$$SEIKYU_YEAR_MONTH$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ上限額管理結果基本 WHERE 業務コード = 'QAJ030' AND 受付年月 = '$$SEIKYU_YEAR_MONTH$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ上限額管理結果明細 WHERE 業務コード = 'QAJ030' AND 受付年月 = '$$SEIKYU_YEAR_MONTH$$'

IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END
