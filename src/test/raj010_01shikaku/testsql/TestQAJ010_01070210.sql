DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

DELETE FROM WR$$JICHITAI_CODE$$QA..QAZ受給状況 WHERE 業務コード='QAJ105' AND 宛名コード='$$ATENA_CODE$$' AND 削除フラグ='0'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAS資格履歴 WHERE 業務コード='QAJ105' AND 宛名コード='$$ATENA_CODE$$' AND 削除フラグ='0'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAS汎用台帳資格内容 WHERE 業務コード='QAJ105' AND 宛名コード='$$ATENA_CODE$$' AND 削除フラグ='0'

IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END