import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01060305(FukushiSiteTestCaseBase):
    """TestQAJ010_01060305"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01060305"]
        super().setUp()
    
    # 地域相談支援給付の支給決定を登録できることを確認する。 受給者証番号が自動附番されることを確認する。決定サービスを登録できることを確認する。利用者負担額を登録できることを確認する。
    def test_QAJ010_01060305(self):
        """地域相談支援給付支給決定登録"""
        
        case_data = self.test_data["TestQAJ010_01060305"]
        atena_code = case_data.get("atena_code", "")
        hakkoTxt = case_data.get("hakkoTxt", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ010")
        # 2 障害福祉サービス決定管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")
        
        # 3 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_3")
        
        # 4 障害福祉サービス決定管理画面: 決定日「20230601」入力決定結果「不支給」選択
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230601")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="不支給")
        
        # 5 障害福祉サービス決定管理画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        
        # 6 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_6")
        
        # 7 障害福祉サービス申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 8 障害福祉サービス申請管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("障害福祉サービス申請管理画面_8")
        
        # 9 障害福祉サービス申請管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")
        
        # 10 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_10")
        
        # 11 帳票印刷画面: 「却下決定通知書（介護給付費等）」行の印刷チェックボックス選択「却下決定通知書（介護給付費等）」行の発行年月日チェックボックス選択発行年月日「20230601」入力
        exec_params = [
            {"report_name": case_data.get("report_name",""),
             "params":[
                 {"title": "交付日", "value":hakkoTxt},
                {"title": "文書番号", "value":"11111"}
                ]
            }
        ] 
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_11")
        
        # 12 帳票印刷画面: 「印刷」ボタン押下
        # self.click_button_by_label("印刷")
        
        # 13 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        
        # 14 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_14")
        
        # 15 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
        # 16 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        
        # 17 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_17")
        
        # 18 障害福祉サービス申請管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        self.click_by_id("CmdKetteiKirikae")
        
        # 19 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_19")
        
        # 20 障害福祉サービス決定管理画面: 決定日「20230601」入力、決定結果「支給」選択、受給者証交付年月日「20230601」入力、地域相談支援受給者証交付年月日「20230601」入力
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230601")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="支給")
        self.form_input_by_id(idstr="TxtJukyushashoKofuYMD", value="20230601")
        self.form_input_by_id(idstr="TxtChikiSodanShienJukyushashoKofuYMD", value="20230601")
        
        # 21 障害福祉サービス決定管理画面: 「決定サービス」ボタン押下
        self.click_button_by_label("決定サービス")
        
        # 22 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_22")
        
        # 23 障害福祉サービス決定管理画面: 開始日「20230601」入力、資格状態「追加」選択、サービス種類「地域移行支援」選択、サービス区分　地域移行支援基本決定　行の状態区分「追加」選択、サービス区分　地域移行支援基本決定　以外行の状態区分「　　」（空白）選択、サービス区分　地域移行支援基本決定　行の決定支給量「20」日／月入力、サービス区分　地域移行支援基本決定　行の決定支給量一回「2」入力、サービス決定備考「サービス決定備考入力テスト」入力
        self.form_input_by_id(idstr="TxtKetteiServiceShikyuKetteiYMD", value="20230601")
        self.form_input_by_id(idstr="ServiceShuruiJokyoCmb_1", text="追加")
        self.form_input_by_id(idstr="ServiceShuruiCmb_1", text="地域移行支援")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_1", text="追加")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_2", text="")
        # self.form_input_by_id(idstr="TxtTxtShikyuryo1_1_1", value="20")
        # self.form_input_by_id(idstr="TxtTxtShikyuryo2_1_1", value="2")
        self.click_by_id("ServiceKetteiBikoBtn_1_1")
        self.form_input_by_id(idstr="ServiceKetteiBiko_1_1", value="サービス決定備考入力テスト")
        
        # 24 障害福祉サービス決定管理画面: 「負担額」ボタン押下
        self.click_button_by_label("負担額")
        
        # 25 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_25")
        
        # 26 障害福祉サービス決定管理画面: 「世帯範囲情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("世帯範囲情報")
        
        # 27 世帯範囲情報画面: 表示
        self.screen_shot("世帯範囲情報画面_27")
        
        # 28 世帯範囲情報画面: 世帯員入日「20230401」入力
        self.form_input_by_id(idstr="TxtSetaiStartYMD1", value="20230401")
        self.screen_shot("世帯範囲情報画面_28")
        
        # 29 世帯範囲情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 30 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_30")
        
        # 31 障害福祉サービス決定管理画面: 「収入・資産入力」ボタン押下
        self.click_button_by_label("収入・資産入力")
        
        # 32 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_32")
        
        # 33 減免申請・収入資産登録画面: 「確定」ボタン押下
        self.click_button_by_label("確定")
        
        # 34 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_34")
        
        # 35 減免申請・収入資産登録画面: 「取込」ボタン押下
        self.click_button_by_label("取込")
        
        # 36 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_36")
        
        # 37 減免申請・収入資産登録画面: 「計算ボタン」ボタン押下
        self.click_button_by_label("計算ボタン")
        
        # 38 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_38")
        
        # 39 減免申請・収入資産登録画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 40 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_40")
        
        # 41 障害福祉サービス決定管理画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        
        # 42 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_42")
        
        # 43 障害福祉サービス申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 44 障害福祉サービス申請管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("障害福祉サービス申請管理画面_44")
        
