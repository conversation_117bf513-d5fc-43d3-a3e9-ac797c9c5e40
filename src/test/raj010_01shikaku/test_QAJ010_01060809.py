import time
import datetime
from datetime import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01060809(FukushiSiteTestCaseBase):
    """TestQAJ010_01060809"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01060809"]
        super().setUp()
    
    # 【障害支援区分判定を必要とするプロセスの場合】市町村審査会の開催予定を登録できることを確認する。一次判定済の対象者を審査対象者として登録できることを確認する。審査会資料を出力できることを確認する。
    def test_QAJ010_01060809(self):
        """審査会登録"""
        
        case_data = self.test_data["TestQAJ010_01060809"]
        atena_code = case_data.get("atena_code", "")
        #システム日付を変数に設定
        date = datetime.now()
        today = format(date, '%Y%m%d')
        today_date = date.strftime("%d").lstrip("0")
        Day = '{0}'.format(today_date)

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ010")
        # 2 障害福祉サービス申請管理画面: 「戻る」ボタン押下
        # self.return_click()
        
        # 3 障害福祉サービス受給者台帳画面: 表示
        self.screen_shot("障害福祉サービス受給者台帳画面_3")
        
        # 4 障害福祉サービス受給者台帳画面: 「戻る」ボタン押下
        self.return_click()
        
        # 5 受給状況画面: 表示
        self.screen_shot("受給状況画面_5")
        
        # 6 受給状況画面: 「戻る」ボタン押下
        self.return_click()
        
        # 7 個人検索画面: 表示
        self.screen_shot("個人検索画面_7")
        
        # 8 個人検索画面: 「戻る」ボタン押下
        self.return_click()
        
        # 9 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_9")
        
        # 10 メインメニュー画面: 「市町村審査会管理」ボタン押下
        self.click_button_by_label("市町村審査会管理")
        
        # 11 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_11")
        
        # 12 サブメニュー画面: 「審査会管理」ボタン押下
        self.click_button_by_label("審査会管理")
        
        # 13 審査会検索画面: 表示
        self.screen_shot("審査会検索画面_13")
        
        # 14 審査会検索画面: 数値「１」ボタン押下（X）　⇒　数値「{システム日付の日}」ボタン押下：「TestQAJ010_01060105」で登録したものを使う
        self.click_button_by_label(Day)
        
        # 15 審査会検索画面: 「対象割当」ボタン押下
        self.click_by_id("Target1")
        
        # 16 審査対象者割当画面: 表示
        self.screen_shot("審査対象者割当画面_16")
        
        # 17 審査対象者割当画面: 「対象者追加」ボタン押下
        self.click_button_by_label("対象者追加")
        
        # 18 審査待ち対象者一覧画面: 表示
        self.screen_shot("審査待ち対象者一覧画面_18")
        
        # 19 審査待ち対象者一覧画面: チェックボックス押下、「対象者決定」ボタン押下
        self.form_input_by_id(idstr="ChkSentakuNo_1", value="1")
        self.click_button_by_label("対象者決定")
        
        # 20 審査対象者割当画面: 表示
        self.screen_shot("審査対象者割当画面_20")
        
        # 21 審査対象者割当画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())

        # 22 審査対象者割当画面: 表示
        self.screen_shot("審査対象者割当画面_22")
        
        # 23 審査対象者割当画面: 「戻る」ボタン押下
        self.return_click()
        
        # 24 審査会検索画面: 表示
        self.screen_shot("審査会検索画面_24")

# 「TestQAJ010_01060105」で登録済みのため省略
#        # 25 審査会検索画面: 「資料作成」ボタン押下
#        self.click_button_by_label("作成")
#        
#        # 26 バッチ起動画面: 表示
#        self.screen_shot("バッチ起動画面_26")
#        
#        # 27 バッチ起動画面: 「処理開始」ボタン押下
#        # ジョブパラ定義
#        params = [
#            {"title":"審査会資料提供同意", "type": "text", "value": "0"}
#        ]
#
#        # ジョブパラに入力
#        self.set_job_params(params)
#        self.form_input_by_id(idstr="UQZGC402_PrintSelect", text="ファイルアウト")
#        self.form_input_by_id(idstr="UQZGC402_chkPrinter", value="0")
#        # ジョブ実行(実行した日時を保持しておく)
#        exec_datetime = self.exec_batch_job()
#        # 基盤メッセージのアサート
#        self.assert_message_base_header("ジョブを起動しました")
#
#        # 実行履歴画面へ遷移
#        self.click_job_exec_log()
#        # 処理が終わるまで待機する
#        self.assert_job_normal_end(exec_datetime=exec_datetime)
#
#        # 作成データをダウンロード
#        # 作成データページに遷移を試す。遷移できればTrue 出来なければFalse
#        exists_down_load_page = self.goto_output_files_dl_page(exec_datetime=exec_datetime)
#        if(exists_down_load_page):
#            # 作成データぺージに遷移出来た場合は、全ファイルの取得を行う。（戻値はDLしたファイル数）
#            output_file_dl_count = self.get_job_output_files(case_name="dl_file")
#            # 作成データページに遷移出来てる場合は戻るボタンで実行履歴に戻る。
#            self.return_click()
#
#        # 帳票履歴画面へ遷移
#        self.click_report_log()
#        # 今回の処理で作成したPDFのDL（戻値はDLしたファイル数）
#        report_dl_count = self.get_job_report_pdf(exec_datetime=exec_datetime)
#        self.return_click()
#
#        # 28 バッチ起動画面: 「ファイルを開く(O)」ボタンを押下
#        # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
#        # self.assert_message_area("プレビューを表示しました ")
#        # self.screen_shot("バッチ起動画面_28")
#        
#        # 29 帳票（PDF）: 表示
#        # self.screen_shot("帳票（PDF）_29")
#        
#        # 30 帳票（PDF）: ×ボタン押下でPDFを閉じる
#        # self.screen_shot("帳票（PDF）_30")
#        
#        # 31 バッチ起動画面: 「戻る」ボタン押下
#        self.return_click()
#        
#        # 32 審査会検索画面: 表示
#        self.screen_shot("審査会検索画面_32")
        
        # 33 審査会検索画面: 「戻る」ボタン押下
        self.return_click()
        
        # 34 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_34")
        
