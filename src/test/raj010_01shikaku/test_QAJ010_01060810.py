import time
from datetime import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01060810(FukushiSiteTestCaseBase):
    """TestQAJ010_01060810"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01060810"]
        super().setUp()
    
    # 【障害支援区分判定を必要とするプロセスの場合】審査会審査結果を登録できることを確認する。二次判定結果を登録できることを確認する。
    def test_QAJ010_01060810(self):
        """二次判定結果登録"""
        
        case_data = self.test_data["TestQAJ010_01060810"]
        atena_code = case_data.get("atena_code", "")
        #システム日付を変数に設定
        date = datetime.now()
        today = format(date, '%Y%m%d')
        today_date = date.strftime("%d").lstrip("0")
        Day = '{0}'.format(today_date)

        self.do_login()
        self.click_button_by_label("市町村審査会管理")
        # 2 サブメニュー画面: 「審査会管理」ボタン押下
        self.click_button_by_label("審査会管理")
        
        # 3 審査会検索画面: 表示
        self.screen_shot("審査会検索画面_3")
        
        # 4 審査会検索画面: 数値「１」ボタン押下（X）　⇒　数値「{システム日付の日}」ボタン押下：「TestQAJ010_01060105」で登録したものを使う
        self.click_button_by_label(Day)
        
        # 5 審査会検索画面: 「対象割当」ボタン押下
        self.click_by_id("Target1")
        
        # 6 審査会結果登録画面: 表示
        self.screen_shot("審査会結果登録画面_6")
        
        # 7 審査会結果登録画面: No「１」ボタン押下（X）　⇒　No「2」ボタン押下：No「1」は「TestQAJ010_01060106」で登録済みのため
        self.click_button_by_label("2")
        
        # 8 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_8")
        
        # 9 障害福祉サービス申請管理画面: 「審査入力」ボタン押下
        self.click_button_by_label("審査入力")
        
        # 10 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_10")
        
        # 11 障害福祉サービス申請管理画面: 二次判定年月日「20230701」入力、二次判定結果「区分５」選択、変更理由「特記・その他 」選択、「登録」ボタン押下
        self.form_input_by_id(idstr="TxtNinteiYMD", value="20230701")
        self.form_input_by_id(idstr="ShinsaHanteiKekkaCmb", text="区分５")
        self.form_input_by_id(idstr="TxtNinteiGekkan", value="36")
        self.form_input_by_id(idstr="TxtNinteiYMD2", value=today)
        self.form_input_by_id(idstr="TxtNinteikikanStartYMD", value=today)
        self.form_input_by_id(idstr="HenkoRiyuCmb", text="特記・その他")
        self.form_input_by_id(idstr="HenkoRiyuChkBox1", value="1")
        self.form_input_by_id(idstr="NinteiRiyuCmb", text="その他")
        self.form_input_by_id(idstr="TxtNinteiRiyu", value="その他認定理由入力テスト")
        self.form_input_by_id(idstr="TxtShinsakaiIken", value="審査会意見入力テスト")
        self.click_button_by_label("認定期間開始日")
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 12 障害福祉サービス申請管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("障害福祉サービス申請管理画面_12")
        
        # 13 障害福祉サービス申請管理画面: 「戻る」ボタン押下
        self.return_click()
        
        # 14 審査会結果登録画面: 表示
        self.screen_shot("審査会結果登録画面_14")
        
        # 15 審査会結果登録画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 16 審査会結果登録画面: 表示
        self.screen_shot("審査会結果登録画面_16")
        
        # 17 審査会結果登録画面: 「戻る」ボタン押下
        self.return_click()
        
        # 18 審査会検索画面: 表示
        self.screen_shot("審査会検索画面_18")
        
        # 19 審査会検索画面: 「戻る」ボタン押下
        self.return_click()
        
        # 20 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_20")
        
        # 21 サブメニュー画面: 「戻る」ボタン押下
        self.return_click()
        
        # 22 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_22")
        
        # 23 メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")
        
        # 24 個人検索画面: 表示
        self.screen_shot("個人検索画面_24")
        
        # 25 個人検索画面: 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=case_data.get("atena_code",""))

        # 26 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        
        # 27 受給状況画面: 表示
        self.screen_shot("受給状況画面_27")
        
        # 28 受給状況画面: 「障害者総合支援」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAJ010")
        
        # 29 障害福祉サービス受給者台帳画面: 表示
        self.screen_shot("障害福祉サービス受給者台帳画面_29")
        
        # 30 障害福祉サービス受給者台帳画面: 「障害者総合支援申請管理」ボタン押下
        self.click_button_by_label("障害者総合支援申請管理")
        
        # 31 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_31")
        
