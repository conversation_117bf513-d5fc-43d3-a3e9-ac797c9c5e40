import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC030_1050109(FukushiSiteTestCaseBase):
    """TestQAC030_1050109"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 受給者に対し、オンラインにて被災非該当通知書が出力できることを確認する。
    def test_QAC030_1050109(self):
        """被災非該当通知書作成"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC030")
        # self.do_login()
        # 1 経過的福祉手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 資格管理画面: 表示
        self.screen_shot("資格管理画面_2")

        # 3 帳票印刷画面: 「経過的福祉手当被災非該当通知書」行の印刷チェックボックス選択「経過的福祉手当被災非該当通知書」行の発行年月日チェックボックス選択「経過的福祉手当被災非該当通知書」行の発行年月日「20230502」
        self.print_online_reports(case_name="帳票印刷画面", report_name="経過的福祉手当被災非該当通知書", hakkou_ymd="20230502")
        self.screen_shot("帳票印刷画面_3")

        # 4 帳票印刷画面: 「印刷」ボタン押下

        # 5 帳票印刷画面: 経過的福祉手当被災非該当通知書「ファイルを開く(O)」ボタンを押下
        # Assert: 「プレビューを表示しました」のメッセージチェック

        # 6 経過的福祉手当被災非該当通知書（PDF）: 表示
        self.screen_shot("経過的福祉手当被災非該当通知書（PDF）_6")

        # 7 経過的福祉手当被災非該当通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 8 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_8")
