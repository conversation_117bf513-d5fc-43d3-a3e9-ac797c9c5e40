import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC030_1050601(FukushiSiteTestCaseBase):
    """TestQAC030_1050601"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 以下のEUCが正しく抽出できることを確認する。・経過的福祉手当_受給者一覧・経過的福祉手当_差止一覧・経過的福祉手当_現況一覧・経過的福祉手当_税無し及び未申告者・経過的福祉手当_履歴一覧
    def test_QAC030_1050601(self):
        """必要に応じて業務データの抽出・確認_資格関連_"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 EUC画面: 表示
        self.transit_euc_std()
        self.screen_shot("EUC画面_1")

        # 2 EUC画面: テーブル参照「経過的福祉手当_受給者一覧」ダブルクリック
        self.get_euc_table(table_name="経過的福祉手当_受給者一覧")
        self.screen_shot("EUC画面_2")

        # 3 EUC_経過的福祉手当_受給者一覧タグ画面: 表示

        # 4 EUC_経過的福祉手当_受給者一覧タグ画面: 「検索」ボタン押下

        # 5 [結果]EUC_経過的福祉手当_受給者一覧タグ画面: 表示

        # 6 [結果]EUC_経過的福祉手当_受給者一覧タグ画面: ×ボタン押下で閉じる

        # 7 EUC_経過的福祉手当_受給者一覧タグ画面: 表示

        # 8 EUC_経過的福祉手当_受給者一覧タグ画面: ×ボタン押下で閉じる

        # 9 EUC画面: 表示

        # 10 EUC画面: テーブル参照「経過的福祉手当_差止一覧」ダブルクリック
        self.get_euc_table(table_name="経過的福祉手当_差止一覧")
        self.screen_shot("EUC画面_10")

        # 11 EUC_経過的福祉手当_差止一覧タグ画面: 表示

        # 12 EUC_経過的福祉手当_差止一覧タグ画面: 「検索」ボタン押下

        # 13 [結果]EUC_経過的福祉手当_差止一覧タグ画面: 表示

        # 14 [結果]EUC_経過的福祉手当_差止一覧タグ画面: ×ボタン押下で閉じる

        # 15 EUC_経過的福祉手当_差止一覧タグ画面: 表示

        # 16 EUC_経過的福祉手当_差止一覧タグ画面: ×ボタン押下で閉じる

        # 17 EUC画面: 表示

        # 18 EUC画面: テーブル参照「経過的福祉手当_現況一覧」ダブルクリック
        self.get_euc_table(table_name="経過的福祉手当_現況一覧")
        self.screen_shot("EUC画面_18")

        # 19 EUC_経過的福祉手当_現況一覧タグ画面: 表示

        # 20 EUC_経過的福祉手当_現況一覧タグ画面: 「検索」ボタン押下

        # 21 [結果]EUC_経過的福祉手当_現況一覧タグ画面: 表示

        # 22 [結果]EUC_経過的福祉手当_現況一覧タグ画面: ×ボタン押下で閉じる

        # 23 EUC_経過的福祉手当_現況一覧タグ画面: 表示

        # 24 EUC_経過的福祉手当_現況一覧タグ画面: ×ボタン押下で閉じる

        # 25 EUC画面: 表示

        # 26 EUC画面: テーブル参照「経過的福祉手当_履歴一覧」ダブルクリック
        self.get_euc_table(table_name="経過的福祉手当_履歴一覧")
        self.screen_shot("EUC画面_26")

        # 27 EUC_経過的福祉手当_履歴一覧タグ画面: 表示

        # 28 EUC_経過的福祉手当_履歴一覧タグ画面: 「検索」ボタン押下

        # 29 [結果]EUC_経過的福祉手当_履歴一覧タグ画面: 表示

        # 30 [結果]EUC_経過的福祉手当_履歴一覧タグ画面: ×ボタン押下で閉じる

        # 31 EUC_経過的福祉手当_履歴一覧タグ画面: 表示

        # 32 EUC_経過的福祉手当_履歴一覧タグ画面: ×ボタン押下で閉じる

        # 33 EUC画面: テーブル参照「経過的福祉手当_税無し及び未申告者」ダブルクリック
        self.get_euc_table(table_name="経過的福祉手当_税無し及び未申告者")
        self.screen_shot("EUC画面_26")

        # 34 EUC_経過的福祉手当_税無し及び未申告者タグ画面: 表示

        # 35 EUC_経過的福祉手当_税無し及び未申告者タグ画面: 「検索」ボタン押下

        # 36 [結果]EUC_経過的福祉手当_税無し及び未申告者タグ画面: 表示

        # 37 [結果]EUC_経過的福祉手当_税無し及び未申告者タグ画面: ×ボタン押下で閉じる

        # 38 EUC_経過的福祉手当_税無し及び未申告者タグ画面: 表示

        # 39 EUC_経過的福祉手当_税無し及び未申告者タグ画面: ×ボタン押下で閉じる

        # 40 EUC画面: 表示
