import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC030_1050107(FukushiSiteTestCaseBase):
    """TestQAC030_1050107"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 転入者に対し、申請情報を登録し、受給者台帳送付依頼書が出力できることを確認する。
    def test_QAC030_1050107(self):
        """受給者台帳送付依頼書作成"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        kaitei_ymd = case_data.get("kaitei_ymd", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「経過的福祉手当」ボタン押下
        self.click_button_by_label("経過的福祉手当")

        # 8 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_8")

        # 9 経過的福祉手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 経過的福祉手当資格管理画面: 申請種別「転入」選択申請理由「転入」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="転入")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="転入")

        # 11 経過的福祉手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 経過的福祉手当資格管理画面: 申請日「20230401」担当所管区「第一区」選択誓約有無「チェック」
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        self.form_input_by_id(idstr="SeiyakuumuChkBox", value="1")

        # 13 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_13")

        # 14 経過的福祉手当資格管理画面: 障害区分１「視覚障害」選択有期認定年月１「20251031」認定基準１「一 両眼の視力がそれぞれ〇・〇二以下のもの」選択
        self.form_input_by_id(idstr="Shougai1Cmb", text="視覚障害")
        self.form_input_by_id(idstr="TxtNinteiYMD1", value="20251031")
        self.form_input_by_id(idstr="SelectHanyo1", text="一 両眼の視力がそれぞれ〇・〇二以下のもの")

        # 15 経過的福祉手当資格管理画面: 「障害程度審査情報1」ボタン押下
        self.click_button_by_label("障害程度審査情報1")

        # 16 障害審査情報画面: 表示
        self.screen_shot("障害審査情報画面_16")

        # 17 障害審査情報画面: 判定機関依頼日「20230401」資格判定方法「審査機関」選択資格判定内容「診断書」選択
        self.form_input_by_id(idstr="TxtHanteiYMD", value="20230401")
        self.form_input_by_id(idstr="CmbHRiyu", text="審査機関")
        self.form_input_by_id(idstr="CmbHNaiyou", text="診断書")

        # 18 障害審査情報画面: 表示
        self.screen_shot("障害審査情報画面_18")

        # 19 障害審査情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 20 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_20")

        # 21 経過的福祉手当資格管理画面: 「所得情報」ボタン押下
        self.click_button_by_label("所得情報")

        # 22 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_22")

        # 23 住民税世帯情報画面: 対象者世帯一覧「1」ボタン押下
        self.click_button_by_label("1")

        # 24 住民税個人情報画面: 表示
        self.screen_shot("住民税個人情報画面_24")

        # 25 住民税個人情報画面: 「戻る」ボタン押下
        self.return_click()

        # 26 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_26")

        # 27 住民税世帯情報画面: 「戻る」ボタン押下
        self.return_click()

        # 28 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_28")

        # 29 経過的福祉手当資格管理画面: 「福祉世帯情報」ボタン押下
        self.click_button_by_label("福祉世帯情報")

        # 30 福祉世帯情報画面: 表示
        self.screen_shot("福祉世帯情報画面_30")

        # 31 福祉世帯情報画面: 本人から見た続柄「本人」選択受給者との関係「本人」該当日「20230501」
        self.fukushi_setai_entry_helper(kankei_text="扶養義務者", gaitou_ymd=kaitei_ymd, sonota_sakujo_flg=True)

        # 32 経過的福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 33 経過的福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 34 経過的福祉手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("経過的福祉手当資格管理画面_34")

        # 35 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 36 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_36")

        # 37 バッチ起動画面: 業務：障害事業：経過的福祉手当処理区分：月次処理処理分類：通知書出力
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="経過的福祉手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="通知書出力")

        # 38 バッチ起動画面: 「受給者台帳送付依頼書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("受給者台帳送付依頼書出力処理")

        # 39 バッチ起動画面: 福祉事務所コード「」開始申請年月日「20230401」終了申請年月日「20230401」発行年月日「20230502」
        params = [
            {"title": "福祉事務所コード", "type": "text", "value": ""},
            {"title": "開始申請年月日", "type": "text", "value": "20230401"},
            {"title": "終了申請年月日", "type": "text", "value": "20230401"},
            {"title": "発行年月日", "type": "text", "value": "20230502"},
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_39")

        # 40 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 41 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 42 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_42")

        # 43 ジョブ実行履歴画面: 「検索」ボタン押下

        # 44 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_44")

        # 45 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 46 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_46")

        # 47 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 48 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_48")

        # 49 ジョブ帳票履歴画面: 「受給者台帳の送付依頼について」のNoボタン押下

        # 50 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 51 受給者台帳の送付依頼について（PDF）: 表示
        self.screen_shot("受給者台帳の送付依頼について（PDF）_51")

        # 52 受給者台帳の送付依頼について（PDF）: ×ボタン押下でPDFを閉じる
        self.screen_shot("受給者台帳の送付依頼について（PDF）_52")
