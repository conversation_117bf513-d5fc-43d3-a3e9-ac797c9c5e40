import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC030_1050401(FukushiSiteTestCaseBase):
    """TestQAC030_1050401"""

    def setUp(self):
        case_data = self.test_data["TestQAC030_1050401"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC030", "DELETE_ATENA_CODE": case_data.get("atena_code", ""), "TARGET_NENDO": case_data.get("nendoY", "")}
        self.exec_sqlfile("Test_QAC030_1050401.sql", params=sql_params)
        super().setUp()

    # 有期関連書類の提出により、有期更新された対象者の情報を登録できることを確認する。
    def test_QAC030_1050401(self):
        """有期認定更新者の登録"""

        case_data = self.test_data["TestQAC030_1050401"]
        atena_code = case_data.get("atena_code", "")
        shinseiShubetsu = case_data.get("shinseiShubetsu", "")
        shinseiRiyuu = case_data.get("shinseiRiyuu", "")
        shinseiYMD = case_data.get("shinseiYMD", "")
        seiyakuumu = case_data.get("seiyakuumu", "")
        shougai1 = case_data.get("shougai1", "")
        ninteiYMD1 = case_data.get("ninteiYMD1", "")
        hanyo1 = case_data.get("hanyo1", "")
        hanteiYMD = case_data.get("hanteiYMD", "")
        hRiyu = case_data.get("hRiyu", "")
        hNaiyou = case_data.get("hNaiyou", "")
        gaitouYMD = case_data.get("gaitouYMD", "")
        txtKaitei = case_data.get("txtKaitei", "")
        shintasuBtn = case_data.get("shintasuBtn", "")
        shintatsuYMD = case_data.get("shintatsuYMD", "")
        shintatsuHanteiYMD = case_data.get("shintatsuHanteiYMD", "")
        shintasuHantei = case_data.get("shintasuHantei", "")
        ketteiYMD = case_data.get("ketteiYMD", "")
        ketteiKekka = case_data.get("ketteiKekka", "")
        shinsei_shubetsu = case_data.get("shinsei_shubetsu", "")
        shinsei_riyuu = case_data.get("shinsei_riyuu", "")
        shinsei_ymd = case_data.get("shinsei_ymd", "")
        kaitei = case_data.get("kaitei", "")
        nintei_ymd1 = case_data.get("nintei_ymd1", "")
        shintatsuYMD_2 = case_data.get("shintatsuYMD_2", "")
        shintatsuHanteiYMD_2 = case_data.get("shintatsuHanteiYMD_2", "")
        shintasuHantei_2 = case_data.get("shintasuHantei_2", "")
        kettei_ymd = case_data.get("kettei_ymd", "")
        kettei_kekka = case_data.get("kettei_kekka", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        self.screen_shot("個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「経過的福祉手当」ボタン押下
        self.click_button_by_label("経過的福祉手当")

        # 8 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_8")

        # ＜対象者の条件＞
        # ・障害児福祉手当の資格を持っている住民
        # ・障害有期が20230531以前の対象者
        self.click_button_by_label("申請内容入力")
        # 申請種別
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinseiShubetsu)
        # 申請理由
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinseiRiyuu)
        self.click_button_by_label("確定")
        # 申請日
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinseiYMD)
        # 誓約有無
        self.form_input_by_id(idstr="SeiyakuumuChkBox", value=seiyakuumu)
        # 障害区分１
        self.form_input_by_id(idstr="Shougai1Cmb", text=shougai1)
        # 有期認定年月１
        self.form_input_by_id(idstr="TxtNinteiYMD1", value=ninteiYMD1)
        # 認定基準１
        self.form_input_by_id(idstr="SelectHanyo1", text=hanyo1)
        self.click_button_by_label("障害程度審査情報1")
        # 判定機関判定依頼日
        self.form_input_by_id(idstr="TxtHanteiYMD", value=hanteiYMD)
        # 資格判定方法
        self.form_input_by_id(idstr="CmbHRiyu", text=hRiyu)
        # 資格判定内容
        self.form_input_by_id(idstr="CmbHNaiyou", text=hNaiyou)
        self.click_button_by_label("入力完了")
        self.click_button_by_label("福祉世帯情報")
        self.fukushi_setai_entry_helper(gaitou_ymd=gaitouYMD, sonota_sakujo_flg=True)
        self.click_button_by_label("入力完了")
        # 支給開始年月
        self.form_input_by_id(idstr="TxtKaitei", value=txtKaitei)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        can_shintatsu_button = self.click_button_by_label(shintasuBtn)
        if (can_shintatsu_button):
            self.form_input_by_id(idstr="TxtShintatsuYMD", value=shintatsuYMD)
            self.form_input_by_id(idstr="TxtShintatsuHanteiYMD", value=shintatsuHanteiYMD)
            self.form_input_by_id(idstr="ShintasuHanteiCmb", text=shintasuHantei)
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました。")

        self.click_button_by_label("決定内容入力")
        self.form_input_by_id(idstr="TxtKetteiYMD", value=ketteiYMD)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=ketteiKekka)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        # 9 経過的福祉手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 経過的福祉手当資格管理画面: 申請種別「変更」選択申請理由「有期更新」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinsei_shubetsu)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinsei_riyuu)
        self.screen_shot("経過的福祉手当資格管理画面_10")

        # 11 経過的福祉手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 経過的福祉手当資格管理画面: 申請日「20230601」改定年月「20230601」有期認定年月１「20251031」
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinsei_ymd)
        self.form_input_by_id(idstr="TxtKaitei", value=kaitei)
        self.form_input_by_id(idstr="TxtNinteiYMD1", value=nintei_ymd1)
        self.screen_shot("経過的福祉手当資格管理画面_12")

        # 13 経過的福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 14 経過的福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 15 経過的福祉手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("経過的福祉手当資格管理画面_15")

        # 16 経過的福祉手当資格管理画面: 「進達入力」ボタン押下
        # 17 経過的福祉手当資格管理画面: 進達日「20230601」進達判定年月日「20230601」進達結果「該当」
        # 18 経過的福祉手当資格管理画面: 「月額計算」ボタン押下
        # 19 経過的福祉手当資格管理画面: 「登録」ボタン押下
        # 20 経過的福祉手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        can_shintatsu_button = self.click_button_by_label(shintasuBtn)
        if (can_shintatsu_button):
            self.form_input_by_id(idstr="TxtShintatsuYMD", value=shintatsuYMD_2)
            self.form_input_by_id(idstr="TxtShintatsuHanteiYMD", value=shintatsuHanteiYMD_2)
            self.form_input_by_id(idstr="ShintasuHanteiCmb", text=shintasuHantei_2)
            self.screen_shot("障害児福祉手当資格管理画面_17")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました。")
            self.screen_shot("障害児福祉手当資格管理画面_20")

        # 21 経過的福祉手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 22 経過的福祉手当資格管理画面: 判定日「20230601」判定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value=kettei_ymd)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=kettei_kekka)
        self.screen_shot("経過的福祉手当資格管理画面_22")

        # 23 経過的福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 24 経過的福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 25 経過的福祉手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("経過的福祉手当資格管理画面_25")
