import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC030_1050105(FukushiSiteTestCaseBase):
    """TestQAC030_1050105"""

    def setUp(self):
        case_data = self.test_data["TestQAC030_1050105"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC030", "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
                      "TARGET_NENDO":case_data.get("nendo", ""),"TARGET_SOUSYOTOKU":case_data.get("soushotoku", "")}
        self.exec_sqlfile("Test_QAC030_1050105.sql", params=sql_params)
        super().setUp()

    # 認定請求した住民に対し却下登録ができることを確認する。
    def test_QAC030_1050105(self):
        """審査結果登録_却下_"""

        case_data = self.test_data["TestQAC030_1050105"]
        atena_code = case_data.get("atena_code", "")
        gaito_ymd = case_data.get("gaito_ymd", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「経過的福祉手当」ボタン押下
        self.click_button_by_label("経過的福祉手当")

        # 8 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_8")

        # 9 経過的福祉手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 経過的福祉手当資格管理画面: 申請種別「認定請求」選択申請理由「新規」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="新規")
        self.screen_shot("経過的福祉手当資格管理画面_10")

        # 11 経過的福祉手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 経過的福祉手当資格管理画面: 申請日「20230401」担当所管区「第一区」選択誓約有無「チェック」
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        self.form_input_by_id(idstr="SeiyakuumuChkBox", value="1")
        self.screen_shot("経過的福祉手当資格管理画面_12")

        # 13 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_13")

        # 14 経過的福祉手当資格管理画面: 障害区分１「視覚障害」選択有期認定年月１「20251031」認定基準１「一 両眼の視力がそれぞれ〇・〇二以下のもの」選択
        self.form_input_by_id(idstr="Shougai1Cmb", text="視覚障害")
        self.form_input_by_id(idstr="TxtNinteiYMD1", value="20251031")
        self.form_input_by_id(idstr="SelectHanyo1", text="一 両眼の視力がそれぞれ〇・〇二以下のもの")
        self.screen_shot("経過的福祉手当資格管理画面_14")

        # 15 経過的福祉手当資格管理画面: 「障害程度審査情報1」ボタン押下
        self.click_button_by_label("障害程度審査情報１")

        # 16 障害審査情報画面: 表示
        self.screen_shot("障害審査情報画面_16")

        # 17 障害審査情報画面: 判定機関依頼日「20230401」資格判定方法「審査機関」選択資格判定内容「診断書」選択
        self.form_input_by_id(idstr="TxtHanteiYMD", value="20230401")
        self.form_input_by_id(idstr="CmbHRiyu", text="審査機関")
        self.form_input_by_id(idstr="CmbHNaiyou", text="診断書")

        # 18 障害審査情報画面: 表示
        self.screen_shot("障害審査情報画面_18")

        # 19 障害審査情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 20 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_20")

        # 21 経過的福祉手当資格管理画面: 「福祉世帯情報」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("福祉世帯情報")

        # 22 福祉世帯情報画面: 表示
        self.screen_shot("福祉世帯情報画面_22")

        # 23 福祉世帯情報画面: 本人から見た続柄「本人」選択受給者との関係「本人」該当日「20230501」
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value=gaito_ymd)
        self.form_input_by_id(idstr="ChkFlg_2", value="1")
        self.form_input_by_id(idstr="ChkFlg_3", value="1")
        self.click_button_by_label("入力完了")

        # 24 経過的福祉手当資格管理画面: 「所得情報」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("所得情報")

        # 25 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_25")

        # 26 住民税世帯情報画面: 対象者世帯一覧「1」ボタン押下
        self.click_button_by_label("1")

        # 27 住民税個人情報画面: 表示
        self.screen_shot("住民税個人情報画面_27")

        # 28 住民税個人情報画面: 「戻る」ボタン押下
        self.return_click()

        # 29 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_29")

        # 30 住民税世帯情報画面: 「戻る」ボタン押下
        self.return_click()

        # 31 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_31")

        # 32 経過的福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 33 経過的福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 34 経過的福祉手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("経過的福祉手当資格管理画面_34")

        # 35 経過的福祉手当資格管理画面: 「進達入力」ボタン押下
        self.find_common_buttons_open_button().click()
        can_shintatsu_button = self.click_button_by_label("進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("進達入力")

            # 36 経過的福祉手当資格管理画面: 進達日「20230502」進達判定年月日「20230502」進達結果「該当」
            self.form_input_by_id(idstr="TxtShintatsuYMD", value="20230502")
            self.form_input_by_id(idstr="TxtShintatsuHanteiYMD", value="20230502")
            self.form_input_by_id(idstr="ShintasuHanteiCmb", text="該当")
            self.screen_shot("経過的福祉手当資格管理画面_36")

            # 37 経過的福祉手当資格管理画面: 「月額計算」ボタン押下
            self.click_button_by_label("月額計算")

            # 38 経過的福祉手当資格管理画面: 「登録」ボタン押下
            self.click_button_by_label("登録")
            self.alert_ok()

            # 39 経過的福祉手当資格管理画面: 表示
            # Assert: 「登録しました」のメッセージチェック
            self.assert_message_area("登録しました")
            self.screen_shot("経過的福祉手当資格管理画面_39")

        # 40 経過的福祉手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 41 経過的福祉手当資格管理画面: 判定日「20230502」判定結果「却下」却下理由「要件非該当のため却下」
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230502")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="却下")
        self.form_input_by_id(idstr="KetteiRiyuCmb", text="障害非該当")
        self.form_input_by_id(idstr="TxtHanteiriyu", value="要件非該当のため却下")

        self.screen_shot("経過的福祉手当資格管理画面_41")

        # 42 経過的福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 43 経過的福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 44 経過的福祉手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("経過的福祉手当資格管理画面_44")
