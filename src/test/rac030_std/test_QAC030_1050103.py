import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC030_1050103(FukushiSiteTestCaseBase):
    """TestQAC030_1050103"""

    def setUp(self):
        case_data = self.test_data["TestQAC030_1050103"]
        super().setUp()

    # 認定請求した住民に対し決定登録ができることを確認する。かつ受給者番号が付与されていることを確認する。
    def test_QAC030_1050103(self):
        """審査結果登録_認定_"""

        case_data = self.test_data["TestQAC030_1050103"]
        atena_code = case_data.get("atena_code", "")
        hantei_ymd = case_data.get("hantei_ymd", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC030")
        # 1 経過的福祉手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        self.click_button_by_label("確定")

        # 2 経過的福祉手当資格管理画面: 「提出書類管理」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("提出書類管理")

        # 3 提出書類管理: 表示
        self.screen_shot("提出書類管理_3")

        # 4 提出書類管理: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 5 提出書類管理: 認定診断書の提出日「20230502」その他の提出日「20230502」
        self.entry_teishutsu_shorui(shorui_name="認定診断書", is_check=True, date_ymd=hantei_ymd)
        self.entry_teishutsu_shorui(shorui_name="その他", is_check=True, date_ymd=hantei_ymd)
        self.screen_shot("提出書類管理_5")

        # 6 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 7 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_7")

        # 8 経過的福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 9 経過的福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 10 経過的福祉手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("経過的福祉手当資格管理画面_10")

        # 11 経過的福祉手当資格管理画面: 「進達入力」ボタン押下
        can_shintatsu_button = self.click_button_by_label("進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("進達入力")

            # 12 経過的福祉手当資格管理画面: 進達日「20230502」進達判定年月日「20230502」進達結果「該当」
            self.form_input_by_id(idstr="TxtShintatsuYMD", value="20230502")
            self.form_input_by_id(idstr="TxtShintatsuHanteiYMD", value="20230502")
            self.form_input_by_id(idstr="ShintasuHanteiCmb", text="該当")
            self.screen_shot("経過的福祉手当資格管理画面_12")

            # 13 経過的福祉手当資格管理画面: 「月額計算」ボタン押下
            self.click_button_by_label("月額計算")

            # 14 経過的福祉手当資格管理画面: 「登録」ボタン押下
            self.click_button_by_label("登録")
            self.alert_ok()

            # 15 経過的福祉手当資格管理画面: 表示
            # Assert: 「登録しました」のメッセージチェック
            self.assert_message_area("登録しました")
            self.screen_shot("経過的福祉手当資格管理画面_15")

        # 16 経過的福祉手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 17 経過的福祉手当資格管理画面: 判定日「20230502」判定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value=20230502)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.screen_shot("障害児福祉手当資格管理画面_17")

        # 18 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_18")

        # 19 経過的福祉手当資格管理画面: 「所得判定詳細情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("所得判定詳細情報")

        # 20 所得判定詳細情報画面: 表示
        self.screen_shot("所得判定詳細情報画面_20")

        # 21 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 22 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_22")

        # 23 経過的福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 24 経過的福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 25 経過的福祉手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("経過的福祉手当資格管理画面_25")
