import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC030_1050402(FukushiSiteTestCaseBase):
    """TestQAC030_1050402"""

    def setUp(self):
        case_data = self.test_data["TestQAC030_1050402"]
        super().setUp()

    # 有期認定更新者に対し、再認定通知書が出力できることを確認する。
    def test_QAC030_1050402(self):
        """有期認定更新者への各種帳票作成"""

        case_data = self.test_data["TestQAC030_1050402"]
        gyomuSelect = case_data.get("gyomuSelect", "")
        jigyoSelect = case_data.get("jigyoSelect", "")
        shoriKubunSelect = case_data.get("shoriKubunSelect", "")
        shoriBunruiSelect = case_data.get("shoriBunruiSelect", "")
        hakkoYMD = case_data.get("hakkoYMD", "")
        cStartYMD = case_data.get("cStartYMD", "")
        cEndYMD = case_data.get("cEndYMD", "")
        outputOrder = case_data.get("outputOrder", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：障害事業：経過的福祉手当処理区分：月次処理処理分類：通知書出力
        self.form_input_by_id(idstr="GyomuSelect", text=gyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=jigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=shoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=shoriBunruiSelect)

        # 5 バッチ起動画面: 「再認定通知書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("再認定通知書出力処理")

        # 6 バッチ起動画面: 発行年月日「20230501」抽出開始年月日「20230601」抽出終了年月日「20230630」出力順「カナ氏名順」選択
        params = [
            {"title": "発行年月日", "type": "text", "value": hakkoYMD},
            {"title": "抽出開始年月日", "type": "text", "value": cStartYMD},
            {"title": "抽出終了年月日", "type": "text", "value": cEndYMD},
            {"title": "出力順", "type": "select", "value": outputOrder},
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_6")

        # 7 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 8 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 9 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_9")

        # 10 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 11 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_11")

        # 12 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 13 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_13")

        # 14 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 15 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_15")

        # 16 ジョブ帳票履歴画面: 「経過的福祉手当再認定通知書」のNoボタン押下

        # 17 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 18 経過的福祉手当再認定通知書（PDF）: 表示
        # self.screen_shot("経過的福祉手当再認定通知書（PDF）_18")

        # 19 経過的福祉手当再認定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 20 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_20")
