import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC030_1050104(FukushiSiteTestCaseBase):
    """TestQAC030_1050104"""

    def setUp(self):
        case_data = self.test_data["TestQAC030_1050104"]
        super().setUp()

    # 認定請求決定通知書、支給停止通知書、通知書対象者一覧  を出力できることを確認する。
    def test_QAC030_1050104(self):
        """認定請求決定通知書等作成"""

        case_data = self.test_data["TestQAC030_1050104"]
        atena_code = case_data.get("atena_code", "")
        hakkou_ymd = case_data.get("hakkou_ymd","")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC030")
        # self.do_login()

        # 1 経過的福祉手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 資格管理画面: 表示
        self.screen_shot("資格管理画面_2")

        # 3 帳票印刷画面: 「経過的福祉手当認定通知書」行の印刷チェックボックス選択「経過的福祉手当認定通知書」行の発行年月日チェックボックス選択「経過的福祉手当認定通知書」行の発行年月日「20230502」「経過的福祉手当支給停止通知書」行の印刷チェックボックス選択「経過的福祉手当支給停止通知書」行の発行年月日チェックボックス選択「経過的福祉手当支給停止通知書」行の発行年月日「20230502」
        exec_params = [
            {
                "report_name": "経過的福祉手当　認定通知書",
                "params":[
                    {"title": "発行年月日", "value":hakkou_ymd,"is_no_print":"1"},
                    {"title": "文書番号", "value":"12345"},
                ]
            },
            {
                "report_name": "経過的福祉手当支給停止通知書",
                "params":[
                    {"title": "発行年月日", "value":hakkou_ymd,"is_no_print":"1"},
                    {"title": "文書番号", "value":"12345"},
                ]
            }
        ]
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_3")
        self.assert_message_area("プレビューを表示しました")

        # 4 帳票印刷画面: 「印刷」ボタン押下

        # 5 帳票印刷画面: 経過的福祉手当認定通知書「ファイルを開く(O)」ボタンを押下

        # Assert: 「プレビューを表示しました」のメッセージチェック

        # 6 経過的福祉手当認定通知書（PDF）: 表示

        # 7 経過的福祉手当認定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 8 帳票印刷画面: 経過的福祉手当支給停止通知書「ファイルを開く(O)」ボタンを押下

        # Assert: 「プレビューを表示しました」のメッセージチェック

        # 9 経過的福祉手当支給停止通知書（PDF）: 表示

        # 10 経過的福祉手当支給停止通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 11 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 12 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_12")

        # 13 メインメニュー画面: 「バッチ起動」ボタン押下
        self.do_login()
        self.click_button_by_label("バッチ起動")

        # 14 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_14")

        # 15 バッチ起動画面: 業務：障害事業：経過的福祉手当処理区分：月次処理処理分類：通知書出力
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="経過的福祉手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="通知書出力")

        # 16 バッチ起動画面: 「通知書対象者一覧出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("通知書対象者一覧")

        # 17 バッチ起動画面: 発行年月日「20230502」開始決定日「20230502」終了決定日「20230502」申請種別「認定請求」決定結果「結果」出力順「認定番号順」選択
        params = [
            {"title": "発行年月日", "type": "text", "value": "20230502"},
            {"title": "抽出開始年月日", "type": "text", "value": "20230502"},
            {"title": "抽出終了年月日", "type": "text", "value": "20230502"},
            {"title": "申請種別", "type": "select", "value": "認定請求"},
            {"title": "決定結果", "type": "select", "value": "決定"},
            {"title": "出力順", "type": "select", "value": "認定番号"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_17")

        # 18 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 19 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 20 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_20")

        # 22 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 23 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_23")

        # 24 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 25 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_25")

        # 26 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 27 ジョブ帳票履歴画面: 表示

        # 28 ジョブ帳票履歴画面: 「経過的福祉手当　通知書対象者一覧」のNoボタン押下

        # 29 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 30 経過的福祉手当　通知書対象者一覧（PDF）: 表示

        # 31 経過的福祉手当　通知書対象者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 32 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 33 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 34 バッチ起動画面: 業務：障害事業：経過的福祉手当処理区分：月次処理処理分類：通知書出力
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="経過的福祉手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="通知書出力")

        # 35 バッチ起動画面: 「認定通知書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("認定通知書出力処理")

        # 36 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」出力順「認定番号順」選択発行年月日「20230502」
        params = [
            {"title": "抽出開始年月日", "type": "text", "value": "20230502"},
            {"title": "抽出終了年月日", "type": "text", "value": "20230502"},
            {"title": "出力順", "type": "select", "value": "認定番号順"},
            {"title": "発行年月日", "type": "text", "value": "20230502"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_36")

        # 37 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 38 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 39 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_39")

        # 40 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 41 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_41")

        # 42 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 43 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_43")

        # 44 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 45 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_45")

        # 46 ジョブ帳票履歴画面: 「経過的福祉手当認定通知書」のNoボタン押下

        # 47 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 48 経過的福祉手当認定通知書（PDF）: 表示

        # 49 経過的福祉手当認定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 50 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 51 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 52 バッチ起動画面: 業務：障害事業：経過的福祉手当処理区分：月次処理処理分類：通知書出力
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="経過的福祉手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="通知書出力")

        # 53 バッチ起動画面: 「支給停止通知書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("支給停止通知書出力処理")

        # 54 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」出力順「認定番号順」選択発行年月日「20230502」
        params = [
            {"title": "抽出開始年月日", "type": "text", "value": "20230502"},
            {"title": "抽出終了年月日", "type": "text", "value": "20230502"},
            {"title": "出力順", "type": "select", "value": "認定番号順"},
            {"title": "発行年月日", "type": "text", "value": "20230502"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_54")

        # 55 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 56 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 57 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_57")

        # 58 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 59 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_59")

        # 60 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 61 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_61")

        # 62 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 63 ジョブ帳票履歴画面: 表示

        # 64 ジョブ帳票履歴画面: 「経過的福祉手当支給停止通知書」のNoボタン押下

        # 65 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 66 経過的福祉手当支給停止通知書（PDF）: 表示

        # 67 経過的福祉手当支給停止通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 68 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()
